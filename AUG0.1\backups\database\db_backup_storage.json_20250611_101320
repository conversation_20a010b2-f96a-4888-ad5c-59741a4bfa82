{"telemetry.macMachineId": "6814b45252b098555d377a775a48e6dad250561347f45aa01633652f7b45f1e7", "backupWorkspaces": {"workspaces": [], "folders": [{"folderUri": "file:///d%3A/AIGC-dm/Cross-border%20E-commerce%20Website%20Project/%E6%B8%85%E7%90%86/augment-vip/AUG0.1"}], "emptyWindows": []}, "windowControlHeight": 35, "profileAssociations": {"workspaces": {"file:///d%3A/AIGC-dm/Cross-border%20E-commerce%20Website%20Project/%E6%B8%85%E7%90%86/augment-vip/AUG0.1": "__default__profile__"}, "emptyWindows": {}}, "windowsState": {"lastActiveWindow": {"folder": "file:///d%3A/AIGC-dm/Cross-border%20E-commerce%20Website%20Project/%E6%B8%85%E7%90%86/augment-vip/AUG0.1", "backupPath": "C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\Backups\\76bf2bfa6278c017db4e0d71fddd9028", "uiState": {"mode": 0, "x": 342, "y": 150, "width": 1026, "height": 770}}, "openedWindows": []}, "theme": "vs-dark", "themeBackground": "#1a1a1a", "windowSplash": {"zoomLevel": 0, "baseTheme": "vs-dark", "colorInfo": {"foreground": "rgba(204, 204, 204, 0.87)", "background": "#1a1a1a", "editorBackground": "#1a1a1a", "titleBarBackground": "#141414", "titleBarBorder": "rgba(255, 255, 255, 0.05)", "activityBarBackground": "#141414", "sideBarBackground": "#141414", "sideBarBorder": "rgba(255, 255, 255, 0.05)", "statusBarBackground": "#141414", "statusBarBorder": "rgba(255, 255, 255, 0.05)", "statusBarNoFolderBackground": "#141414"}, "layoutInfo": {"sideBarSide": "left", "editorPartMinWidth": 220, "titleBarHeight": 35, "activityBarWidth": 0, "sideBarWidth": 350, "statusBarHeight": 22, "windowBorder": false}}, "telemetry.lastSessionDate": "2024-10-20T09:54:15.466808Z", "telemetry.sqmId": "0bba4957-ce9c-4ae0-bd50-66eaf3e04341", "telemetry.firstSessionDate": "2024-09-12T09:54:15.466808Z", "telemetry.optIn": "ab50b6f3-88c1-4cae-abdc-25d713cf0e73", "telemetry.machineId": "98648797d7414893918b22d1bf76da3b", "telemetry.isNewAppInstall": "5614f2be-21a7-4272-a03e-bc3e83cadab8", "telemetry.devDeviceId": "d3b2bbdf-50f3-4279-bcc4-286c0f607738", "telemetry.sessionId": "4dac80f9-8951-4272-893f-1b0a8eeb83b3"}