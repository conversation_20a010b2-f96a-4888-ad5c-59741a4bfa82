# AUG 0.1 Database Cleaner PowerShell Script
# Version: 0.1.0
# Description: Advanced database cleaning for editor telemetry data

param(
    [string[]]$CleanOptions = @("telemetry", "augment"),
    [switch]$CreateBackup = $true,
    [switch]$VerifyIntegrity = $true,
    [switch]$DryRun = $false,
    [string]$ConfigPath = "",
    [switch]$Verbose = $false
)

# Script configuration
$script:ScriptVersion = "0.1.0"
$script:ScriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
$script:ProjectRoot = Split-Path -Parent $script:ScriptPath
$script:BackupDir = Join-Path $script:ProjectRoot "backups\database"
$script:LogDir = Join-Path $script:ProjectRoot "logs"
$script:ConfigFile = if ($ConfigPath) { $ConfigPath } else { Join-Path $script:ProjectRoot "config\database_config.json" }

# Logging functions
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Console output with colors
    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARNING" { Write-Host $logMessage -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
        "VERBOSE" { if ($Verbose) { Write-Host $logMessage -ForegroundColor Cyan } }
        default { Write-Host $logMessage -ForegroundColor White }
    }
    
    # File logging
    if (-not (Test-Path $script:LogDir)) {
        New-Item -ItemType Directory -Path $script:LogDir -Force | Out-Null
    }
    
    $logFile = Join-Path $script:LogDir "database_cleaning.log"
    Add-Content -Path $logFile -Value $logMessage
}

function Initialize-DatabaseCleaner {
    Write-Log "Initializing AUG 0.1 Database Cleaner v$script:ScriptVersion" "INFO"
    Write-Log "Script Path: $script:ScriptPath" "VERBOSE"
    Write-Log "Project Root: $script:ProjectRoot" "VERBOSE"
    Write-Log "Backup Directory: $script:BackupDir" "VERBOSE"
    Write-Log "Config File: $script:ConfigFile" "VERBOSE"
    
    # Create necessary directories
    @($script:BackupDir, $script:LogDir) | ForEach-Object {
        if (-not (Test-Path $_)) {
            New-Item -ItemType Directory -Path $_ -Force | Out-Null
            Write-Log "Created directory: $_" "VERBOSE"
        }
    }
    
    # Load configuration
    if (Test-Path $script:ConfigFile) {
        try {
            $script:Config = Get-Content $script:ConfigFile | ConvertFrom-Json
            Write-Log "Configuration loaded successfully" "SUCCESS"
        }
        catch {
            Write-Log "Failed to load configuration: $($_.Exception.Message)" "ERROR"
            return $false
        }
    }
    else {
        Write-Log "Configuration file not found: $script:ConfigFile" "WARNING"
        Write-Log "Using default configuration" "INFO"
        $script:Config = Get-DefaultConfig
    }
    
    return $true
}

function Get-DefaultConfig {
    return @{
        database_paths = @{
            vscode = @{
                name = "Visual Studio Code"
                storage_files = @("%APPDATA%\Code\User\globalStorage\storage.json")
            }
            cursor = @{
                name = "Cursor Editor"
                storage_files = @("%APPDATA%\Cursor\User\globalStorage\storage.json")
            }
        }
        cleaning_rules = @{
            telemetry = @{
                patterns = @("telemetry.*", "machineId", "devDeviceId", "sessionId")
            }
            augment = @{
                patterns = @("*augment*", "*Augment*")
            }
        }
    }
}

function Find-DatabaseFiles {
    Write-Log "Scanning for database files..." "INFO"
    $databaseFiles = @()
    
    foreach ($editorKey in $script:Config.database_paths.PSObject.Properties.Name) {
        $editor = $script:Config.database_paths.$editorKey
        Write-Log "Checking $($editor.name)..." "VERBOSE"
        
        foreach ($pathPattern in $editor.storage_files) {
            $expandedPath = [Environment]::ExpandEnvironmentVariables($pathPattern)
            Write-Log "Checking path: $expandedPath" "VERBOSE"
            
            if ($expandedPath -like "*`**") {
                # Handle wildcard paths
                $basePath = Split-Path $expandedPath -Parent
                $pattern = Split-Path $expandedPath -Leaf
                
                if (Test-Path $basePath) {
                    $files = Get-ChildItem -Path $basePath -Filter $pattern -Recurse -ErrorAction SilentlyContinue
                    foreach ($file in $files) {
                        $databaseFiles += @{
                            Path = $file.FullName
                            Editor = $editor.name
                            Size = $file.Length
                            LastModified = $file.LastWriteTime
                        }
                        Write-Log "Found: $($file.FullName)" "VERBOSE"
                    }
                }
            }
            else {
                # Handle single file paths
                if (Test-Path $expandedPath) {
                    $file = Get-Item $expandedPath
                    $databaseFiles += @{
                        Path = $file.FullName
                        Editor = $editor.name
                        Size = $file.Length
                        LastModified = $file.LastWriteTime
                    }
                    Write-Log "Found: $($file.FullName)" "VERBOSE"
                }
            }
        }
    }
    
    Write-Log "Found $($databaseFiles.Count) database files" "SUCCESS"
    return $databaseFiles
}

function New-DatabaseBackup {
    param(
        [string]$FilePath
    )
    
    if (-not $CreateBackup) {
        Write-Log "Backup creation disabled" "VERBOSE"
        return $true
    }
    
    try {
        $fileName = Split-Path $FilePath -Leaf
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupFileName = "db_backup_$fileName`_$timestamp"
        $backupPath = Join-Path $script:BackupDir $backupFileName
        
        Copy-Item -Path $FilePath -Destination $backupPath -Force
        Write-Log "Created backup: $backupFileName" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to create backup for $FilePath`: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-JsonIntegrity {
    param(
        [string]$FilePath
    )
    
    try {
        $content = Get-Content -Path $FilePath -Raw -Encoding UTF8
        $json = $content | ConvertFrom-Json
        Write-Log "JSON integrity check passed: $FilePath" "VERBOSE"
        return $true
    }
    catch {
        Write-Log "JSON integrity check failed: $FilePath - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Remove-TelemetryData {
    param(
        [PSCustomObject]$JsonData,
        [string[]]$Patterns
    )
    
    $removedCount = 0
    $patterns = $script:Config.cleaning_rules.telemetry.patterns
    
    function Remove-MatchingKeys {
        param($Object, $Path = "")
        
        if ($Object -is [PSCustomObject]) {
            $keysToRemove = @()
            foreach ($property in $Object.PSObject.Properties) {
                $currentPath = if ($Path) { "$Path.$($property.Name)" } else { $property.Name }
                
                foreach ($pattern in $patterns) {
                    if ($property.Name -like $pattern) {
                        $keysToRemove += $property.Name
                        Write-Log "Marking for removal: $currentPath" "VERBOSE"
                        break
                    }
                }
                
                if ($property.Value -is [PSCustomObject] -or $property.Value -is [Array]) {
                    Remove-MatchingKeys $property.Value $currentPath
                }
            }
            
            foreach ($key in $keysToRemove) {
                $Object.PSObject.Properties.Remove($key)
                $script:removedCount++
                Write-Log "Removed telemetry key: $key" "VERBOSE"
            }
        }
        elseif ($Object -is [Array]) {
            for ($i = 0; $i -lt $Object.Count; $i++) {
                if ($Object[$i] -is [PSCustomObject] -or $Object[$i] -is [Array]) {
                    Remove-MatchingKeys $Object[$i] "$Path[$i]"
                }
            }
        }
    }
    
    Remove-MatchingKeys $JsonData
    return $script:removedCount
}

function Remove-AugmentData {
    param(
        [PSCustomObject]$JsonData
    )
    
    $removedCount = 0
    $patterns = $script:Config.cleaning_rules.augment.patterns
    
    function Remove-AugmentKeys {
        param($Object, $Path = "")
        
        if ($Object -is [PSCustomObject]) {
            $keysToRemove = @()
            foreach ($property in $Object.PSObject.Properties) {
                $currentPath = if ($Path) { "$Path.$($property.Name)" } else { $property.Name }
                
                foreach ($pattern in $patterns) {
                    if ($property.Name -like $pattern -or 
                        ($property.Value -is [string] -and $property.Value -like $pattern)) {
                        $keysToRemove += $property.Name
                        Write-Log "Marking Augment data for removal: $currentPath" "VERBOSE"
                        break
                    }
                }
                
                if ($property.Value -is [PSCustomObject] -or $property.Value -is [Array]) {
                    Remove-AugmentKeys $property.Value $currentPath
                }
            }
            
            foreach ($key in $keysToRemove) {
                $Object.PSObject.Properties.Remove($key)
                $script:removedCount++
                Write-Log "Removed Augment key: $key" "VERBOSE"
            }
        }
        elseif ($Object -is [Array]) {
            for ($i = 0; $i -lt $Object.Count; $i++) {
                if ($Object[$i] -is [PSCustomObject] -or $Object[$i] -is [Array]) {
                    Remove-AugmentKeys $Object[$i] "$Path[$i]"
                }
            }
        }
    }
    
    Remove-AugmentKeys $JsonData
    return $script:removedCount
}

function Invoke-DatabaseCleaning {
    param(
        [array]$DatabaseFiles
    )
    
    Write-Log "Starting database cleaning process..." "INFO"
    Write-Log "Clean options: $($CleanOptions -join ', ')" "INFO"
    Write-Log "Dry run mode: $DryRun" "INFO"
    
    $totalFiles = $DatabaseFiles.Count
    $processedFiles = 0
    $successCount = 0
    
    foreach ($dbFile in $DatabaseFiles) {
        $processedFiles++
        Write-Log "Processing file $processedFiles/$totalFiles`: $($dbFile.Path)" "INFO"
        
        # Create backup
        if (-not (New-DatabaseBackup -FilePath $dbFile.Path)) {
            Write-Log "Skipping file due to backup failure: $($dbFile.Path)" "WARNING"
            continue
        }
        
        try {
            # Read and parse JSON
            $content = Get-Content -Path $dbFile.Path -Raw -Encoding UTF8
            $originalSize = $content.Length
            $jsonData = $content | ConvertFrom-Json
            
            $totalRemoved = 0
            
            # Apply cleaning rules
            foreach ($option in $CleanOptions) {
                switch ($option.ToLower()) {
                    "telemetry" {
                        $removed = Remove-TelemetryData -JsonData $jsonData
                        $totalRemoved += $removed
                        Write-Log "Removed $removed telemetry entries" "INFO"
                    }
                    "augment" {
                        $removed = Remove-AugmentData -JsonData $jsonData
                        $totalRemoved += $removed
                        Write-Log "Removed $removed Augment entries" "INFO"
                    }
                    default {
                        Write-Log "Unknown clean option: $option" "WARNING"
                    }
                }
            }
            
            if ($totalRemoved -gt 0 -and -not $DryRun) {
                # Save cleaned data
                $cleanedContent = $jsonData | ConvertTo-Json -Depth 100 -Compress:$false
                Set-Content -Path $dbFile.Path -Value $cleanedContent -Encoding UTF8
                
                $newSize = $cleanedContent.Length
                $sizeReduction = $originalSize - $newSize
                Write-Log "File size reduced by $sizeReduction bytes" "INFO"
                
                # Verify integrity
                if ($VerifyIntegrity) {
                    if (Test-JsonIntegrity -FilePath $dbFile.Path) {
                        Write-Log "File integrity verified" "SUCCESS"
                    }
                    else {
                        Write-Log "File integrity check failed!" "ERROR"
                        continue
                    }
                }
            }
            elseif ($DryRun) {
                Write-Log "DRY RUN: Would remove $totalRemoved entries" "INFO"
            }
            else {
                Write-Log "No data to clean in this file" "INFO"
            }
            
            $successCount++
            Write-Log "Successfully processed: $($dbFile.Path)" "SUCCESS"
        }
        catch {
            Write-Log "Failed to process $($dbFile.Path)`: $($_.Exception.Message)" "ERROR"
        }
    }
    
    Write-Log "Database cleaning completed" "SUCCESS"
    Write-Log "Processed: $processedFiles files" "INFO"
    Write-Log "Successful: $successCount files" "INFO"
    Write-Log "Failed: $($processedFiles - $successCount) files" "INFO"
    
    return $successCount -eq $processedFiles
}

# Main execution
try {
    Write-Log "=== AUG 0.1 Database Cleaner Started ===" "INFO"
    
    if (-not (Initialize-DatabaseCleaner)) {
        Write-Log "Initialization failed" "ERROR"
        exit 1
    }
    
    $databaseFiles = Find-DatabaseFiles
    
    if ($databaseFiles.Count -eq 0) {
        Write-Log "No database files found to clean" "WARNING"
        exit 0
    }
    
    $success = Invoke-DatabaseCleaning -DatabaseFiles $databaseFiles
    
    if ($success) {
        Write-Log "=== Database Cleaning Completed Successfully ===" "SUCCESS"
        exit 0
    }
    else {
        Write-Log "=== Database Cleaning Completed with Errors ===" "WARNING"
        exit 1
    }
}
catch {
    Write-Log "Critical error: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
}
