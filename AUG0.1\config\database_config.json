{"version": "0.1.0", "description": "Database Cleaning Configuration for AUG 0.1", "database_paths": {"vscode": {"name": "Visual Studio Code", "storage_files": ["%APPDATA%/Code/User/globalStorage/storage.json", "%APPDATA%/Code/User/workspaceStorage/*/workspace.json"], "log_files": ["%APPDATA%/Code/logs/*.log"], "cache_files": ["%APPDATA%/Code/CachedExtensions/*", "%APPDATA%/Code/logs/*/exthost*.log"]}, "vscode-insiders": {"name": "Visual Studio Code Insiders", "storage_files": ["%APPDATA%/Code - Insiders/User/globalStorage/storage.json", "%APPDATA%/Code - Insiders/User/workspaceStorage/*/workspace.json"], "log_files": ["%APPDATA%/Code - Insiders/logs/*.log"], "cache_files": ["%APPDATA%/Code - Insiders/CachedExtensions/*"]}, "cursor": {"name": "Cursor Editor", "storage_files": ["%APPDATA%/Cursor/User/globalStorage/storage.json", "%APPDATA%/Cursor/User/workspaceStorage/*/workspace.json"], "log_files": ["%APPDATA%/Cursor/logs/*.log"], "cache_files": ["%APPDATA%/Cursor/CachedExtensions/*"]}, "vscodium": {"name": "VSCodium", "storage_files": ["%APPDATA%/VSCodium/User/globalStorage/storage.json", "%APPDATA%/VSCodium/User/workspaceStorage/*/workspace.json"], "log_files": ["%APPDATA%/VSCodium/logs/*.log"], "cache_files": ["%APPDATA%/VSCodium/CachedExtensions/*"]}, "code-oss": {"name": "Code - OSS", "storage_files": ["%APPDATA%/Code - OSS/User/globalStorage/storage.json", "%APPDATA%/Code - OSS/User/workspaceStorage/*/workspace.json"], "log_files": ["%APPDATA%/Code - OSS/logs/*.log"], "cache_files": ["%APPDATA%/Code - OSS/CachedExtensions/*"]}}, "cleaning_rules": {"telemetry": {"description": "Remove telemetry data", "patterns": ["telemetry.*", "*.telemetry.*", "machineId", "devDeviceId", "sessionId", "sqmId", "firstSessionDate", "lastSessionDate", "isNewAppInstall", "optIn"], "priority": "high"}, "augment": {"description": "Remove Augment-related data", "patterns": ["*augment*", "*Augment*", "*AUGMENT*"], "priority": "high"}, "workspace": {"description": "Remove workspace data", "patterns": ["workspaceId", "workspace.*", "project.*", "folder.*", "recentlyOpened*", "workbench.startupEditor"], "priority": "medium"}, "cache": {"description": "Remove cache and temporary data", "patterns": ["cache.*", "temp.*", "recent.*", "history.*", "*.cache", "*.temp", "*.tmp"], "priority": "low"}}, "backup_settings": {"enabled": true, "backup_directory": "backups/database", "retention_days": 30, "compression": false, "encryption": false}, "verification_settings": {"enabled": true, "check_json_syntax": true, "check_file_size": true, "check_permissions": true}, "logging_settings": {"enabled": true, "log_level": "INFO", "log_file": "logs/database_cleaning.log", "max_log_size": "10MB", "log_rotation": true}, "safety_settings": {"require_confirmation": true, "dry_run_mode": false, "max_file_size": "100MB", "excluded_files": ["settings.json", "keybindings.json", "snippets/*"]}}