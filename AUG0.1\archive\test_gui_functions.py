"""
测试AUG GUI功能模块
此脚本用于测试aug_gui.py中的各项功能是否正常工作
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def test_powershell_availability():
    """测试PowerShell是否可用"""
    print("测试PowerShell可用性...")
    try:
        process = subprocess.run(["powershell", "-Command", "echo 'PowerShell Test'"], 
                                capture_output=True, text=True, check=False)
        
        if process.returncode == 0:
            print("✓ PowerShell可用")
            return True
        else:
            print("✗ PowerShell不可用")
            print(f"错误输出: {process.stderr}")
            return False
    except Exception as e:
        print(f"✗ 测试PowerShell时出错: {str(e)}")
        return False

def test_script_existence():
    """测试脚本文件是否存在"""
    print("测试脚本文件存在性...")
    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                              "scripts", "enhanced_id_modifier.ps1")
    
    if os.path.exists(script_path):
        print(f"✓ 脚本文件存在: {script_path}")
        return True
    else:
        print(f"✗ 脚本文件不存在: {script_path}")
        return False

def test_backup_directory():
    """测试备份目录是否存在并可写"""
    print("测试备份目录...")
    backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backups")
    
    # 检查目录是否存在
    if not os.path.exists(backup_dir):
        try:
            os.makedirs(backup_dir)
            print(f"✓ 创建了备份目录: {backup_dir}")
        except Exception as e:
            print(f"✗ 无法创建备份目录: {str(e)}")
            return False
    else:
        print(f"✓ 备份目录已存在: {backup_dir}")
    
    # 测试写入权限
    test_file = os.path.join(backup_dir, "test_write.tmp")
    try:
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("✓ 备份目录可写")
        return True
    except Exception as e:
        print(f"✗ 备份目录不可写: {str(e)}")
        return False

def test_editor_detection():
    """测试编辑器检测功能"""
    print("测试编辑器检测...")
    editors = {
        "VSCode": "%APPDATA%\\Code\\User\\globalStorage\\storage.json",
        "VSCode Insiders": "%APPDATA%\\Code - Insiders\\User\\globalStorage\\storage.json",
        "Cursor Editor": "%APPDATA%\\Cursor\\User\\globalStorage\\storage.json",
        "VSCodium": "%APPDATA%\\VSCodium\\User\\globalStorage\\storage.json",
        "Code - OSS": "%APPDATA%\\Code - OSS\\User\\globalStorage\\storage.json"
    }
    
    found_editors = []
    
    for editor, path in editors.items():
        expanded_path = os.path.expandvars(path)
        if os.path.exists(expanded_path):
            found_editors.append(editor)
            print(f"✓ 检测到编辑器: {editor} ({expanded_path})")
    
    if not found_editors:
        print("✗ 未检测到任何支持的编辑器")
        return False
    
    return True

def test_script_execution():
    """测试脚本执行功能"""
    print("测试脚本执行...")
    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                              "scripts", "enhanced_id_modifier.ps1")
    
    # 仅测试帮助命令，不进行实际修改
    cmd = ["powershell", "-ExecutionPolicy", "Bypass", "-File", script_path, "-Help"]
    
    try:
        process = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        if process.returncode == 0:
            print("✓ 脚本执行成功")
            return True
        else:
            print(f"✗ 脚本执行失败: {process.stderr}")
            return False
    except Exception as e:
        print(f"✗ 脚本执行出错: {str(e)}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始测试AUG GUI功能...\n")
    
    tests = [
        test_powershell_availability,
        test_script_existence,
        test_backup_directory,
        test_editor_detection,
        test_script_execution
    ]
    
    results = []
    for test in tests:
        results.append(test())
        print()
    
    # 显示测试结果摘要
    print("测试结果摘要:")
    print("=" * 40)
    for i, test in enumerate(tests):
        status = "通过" if results[i] else "失败"
        print(f"{test.__name__}: {status}")
    
    passed = results.count(True)
    total = len(results)
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if all(results):
        print("\n✓ 所有测试通过，GUI功能应该可以正常工作")
    else:
        print("\n✗ 部分测试失败，GUI可能无法正常工作")

if __name__ == "__main__":
    run_all_tests()