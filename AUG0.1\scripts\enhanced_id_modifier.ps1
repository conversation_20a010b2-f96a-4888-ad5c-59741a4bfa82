# enhanced_id_modifier.ps1
#
# Description: Enhanced telemetry ID modifier for multiple code editors
# Supports VS Code, Cursor, VSCodium and other VS Code-based editors
# Version: 0.1.0

param(
    [switch]$Help,
    [switch]$Enhanced,
    [switch]$All,
    [switch]$Silent,
    [switch]$EncryptBackups,
    [switch]$ListBackups,
    [switch]$Restore,
    [switch]$CleanBackups,
    [string]$Editor = "",
    [string]$Fields = "",
    [string]$BackupDate = "",
    [int]$OlderThan = 0,
    [string]$LogLevel = "Normal"
)

# Show help message
if ($Help) {
    Write-Host "AUG 0.1 - Enhanced Telemetry ID Modifier" -ForegroundColor Green
    Write-Host ""
    Write-Host "Description: Advanced telemetry ID modification for multiple code editors"
    Write-Host ""
    Write-Host "Usage: .\enhanced_id_modifier.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Enhanced          Enable enhanced mode with all features"
    Write-Host "  -All               Process all detected editors"
    Write-Host "  -Editor <names>    Specify editors (<PERSON>SC<PERSON>,<PERSON>ursor,VSCodium)"
    Write-Host "  -Fields <fields>   Specify fields to modify"
    Write-Host "  -Silent            Run in silent mode"
    Write-Host "  -EncryptBackups    Encrypt backup files"
    Write-Host "  -ListBackups       List all backup files"
    Write-Host "  -Restore           Restore from backup"
    Write-Host "  -CleanBackups      Clean old backup files"
    Write-Host "  -BackupDate <date> Specify backup date for restore"
    Write-Host "  -OlderThan <days>  Clean backups older than specified days"
    Write-Host "  -LogLevel <level>  Set log level (Minimal,Normal,Verbose)"
    Write-Host "  -Help              Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\enhanced_id_modifier.ps1 -Enhanced"
    Write-Host "  .\enhanced_id_modifier.ps1 -Editor 'VSCode,Cursor'"
    Write-Host "  .\enhanced_id_modifier.ps1 -All -EncryptBackups"
    Write-Host ""
    exit 0
}

# Global variables
$script:LogLevel = $LogLevel
$script:Silent = $Silent
$script:BackupDir = Join-Path (Get-Location) "backups"
$script:LogDir = Join-Path (Get-Location) "logs"
$script:ConfigDir = Join-Path (Get-Location) "config"

# Ensure directories exist
@($script:BackupDir, $script:LogDir, $script:ConfigDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ -Force | Out-Null
    }
}

# Logging functions
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Console output based on log level and silent mode
    if (-not $script:Silent) {
        switch ($Level) {
            "ERROR" { Write-Host $logMessage -ForegroundColor Red }
            "WARNING" { Write-Host $logMessage -ForegroundColor Yellow }
            "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
            "INFO" { 
                if ($script:LogLevel -ne "Minimal") {
                    Write-Host $logMessage -ForegroundColor Cyan 
                }
            }
            "VERBOSE" { 
                if ($script:LogLevel -eq "Verbose") {
                    Write-Host $logMessage -ForegroundColor Gray 
                }
            }
            default { Write-Host $logMessage }
        }
    }
    
    # File logging
    $logFile = Join-Path $script:LogDir "enhanced_id_modifier_$(Get-Date -Format 'yyyyMMdd').log"
    Add-Content -Path $logFile -Value $logMessage
}

# Editor configuration
$script:EditorConfigs = @{
    "VSCode" = @{
        "Name" = "Visual Studio Code"
        "Paths" = @{
            "Windows" = "$env:APPDATA\Code\User\globalStorage\storage.json"
        }
        "ProcessNames" = @("Code.exe")
    }
    "VSCodeInsiders" = @{
        "Name" = "Visual Studio Code Insiders"
        "Paths" = @{
            "Windows" = "$env:APPDATA\Code - Insiders\User\globalStorage\storage.json"
        }
        "ProcessNames" = @("Code - Insiders.exe")
    }
    "Cursor" = @{
        "Name" = "Cursor Editor"
        "Paths" = @{
            "Windows" = "$env:APPDATA\Cursor\User\globalStorage\storage.json"
        }
        "ProcessNames" = @("Cursor.exe")
    }
    "VSCodium" = @{
        "Name" = "VSCodium"
        "Paths" = @{
            "Windows" = "$env:APPDATA\VSCodium\User\globalStorage\storage.json"
        }
        "ProcessNames" = @("VSCodium.exe")
    }
    "CodeOSS" = @{
        "Name" = "Code - OSS"
        "Paths" = @{
            "Windows" = "$env:APPDATA\Code - OSS\User\globalStorage\storage.json"
        }
        "ProcessNames" = @("code-oss.exe")
    }
}

# Enhanced telemetry fields configuration
$script:TelemetryFields = @{
    "telemetry.machineId" = @{
        "Type" = "hex64"
        "Description" = "Machine unique identifier"
        "Required" = $true
    }
    "telemetry.devDeviceId" = @{
        "Type" = "uuid4"
        "Description" = "Device identifier"
        "Required" = $true
    }
    "telemetry.sessionId" = @{
        "Type" = "uuid4"
        "Description" = "Session identifier"
        "Required" = $false
    }
    "telemetry.sqmId" = @{
        "Type" = "uuid4"
        "Description" = "Software Quality Metrics ID"
        "Required" = $false
    }
    "telemetry.firstSessionDate" = @{
        "Type" = "timestamp"
        "Description" = "First session date"
        "Required" = $false
    }
    "telemetry.lastSessionDate" = @{
        "Type" = "timestamp"
        "Description" = "Last session date"
        "Required" = $false
    }
    "telemetry.isNewAppInstall" = @{
        "Type" = "boolean"
        "Description" = "New app install flag"
        "Required" = $false
    }
    "telemetry.optIn" = @{
        "Type" = "boolean"
        "Description" = "Telemetry opt-in status"
        "Required" = $false
    }
}

# Enhanced ID generation functions
function New-EnhancedMachineId {
    Write-Log "Generating enhanced machine ID..." "VERBOSE"
    
    # Use cryptographically secure random number generator
    $bytes = New-Object byte[] 32
    $rng = [System.Security.Cryptography.RNGCryptoServiceProvider]::Create()
    $rng.GetBytes($bytes)
    $rng.Dispose()
    
    # Add entropy from system information (but anonymized)
    $entropy = [System.Text.Encoding]::UTF8.GetBytes((Get-Date).Ticks.ToString())
    for ($i = 0; $i -lt [Math]::Min($bytes.Length, $entropy.Length); $i++) {
        $bytes[$i] = $bytes[$i] -bxor $entropy[$i]
    }
    
    return [System.BitConverter]::ToString($bytes).Replace("-", "").ToLower()
}

function New-EnhancedDeviceId {
    Write-Log "Generating enhanced device ID..." "VERBOSE"
    
    # Generate UUID v4 with additional entropy
    $guid = [System.Guid]::NewGuid()
    return $guid.ToString().ToLower()
}

function New-SessionId {
    Write-Log "Generating session ID..." "VERBOSE"
    return [System.Guid]::NewGuid().ToString().ToLower()
}

function New-RandomTimestamp {
    Write-Log "Generating random timestamp..." "VERBOSE"
    
    # Generate a random date within the last year
    $now = Get-Date
    $pastYear = $now.AddDays(-365)
    $randomDays = Get-Random -Minimum 0 -Maximum 365
    $randomDate = $pastYear.AddDays($randomDays)
    
    return $randomDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
}

function New-RandomBoolean {
    return (Get-Random -Minimum 0 -Maximum 2) -eq 1
}

# Function to generate ID based on type
function New-IdByType {
    param([string]$Type)
    
    switch ($Type) {
        "hex64" { return New-EnhancedMachineId }
        "uuid4" { return New-EnhancedDeviceId }
        "timestamp" { return New-RandomTimestamp }
        "boolean" { return New-RandomBoolean }
        default { return New-EnhancedDeviceId }
    }
}

# Function to detect installed editors
function Get-InstalledEditors {
    Write-Log "Detecting installed editors..." "INFO"
    
    $installedEditors = @()
    
    foreach ($editorKey in $script:EditorConfigs.Keys) {
        $config = $script:EditorConfigs[$editorKey]
        $storagePath = $config.Paths.Windows
        
        if (Test-Path $storagePath) {
            $installedEditors += $editorKey
            Write-Log "Found $($config.Name) at: $storagePath" "SUCCESS"
        } else {
            Write-Log "$($config.Name) not found" "VERBOSE"
        }
    }
    
    return $installedEditors
}

# Function to check if editor processes are running
function Test-EditorProcesses {
    param([array]$EditorKeys)
    
    $runningProcesses = @()
    
    foreach ($editorKey in $EditorKeys) {
        $config = $script:EditorConfigs[$editorKey]
        foreach ($processName in $config.ProcessNames) {
            $process = Get-Process -Name $processName.Replace(".exe", "") -ErrorAction SilentlyContinue
            if ($process) {
                $runningProcesses += "$($config.Name) ($processName)"
            }
        }
    }
    
    return $runningProcesses
}

# Function to create enhanced backup
function New-EnhancedBackup {
    param(
        [string]$FilePath,
        [string]$EditorName
    )

    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFileName = "$EditorName`_storage_$timestamp.json"
    $backupPath = Join-Path $script:BackupDir $backupFileName

    try {
        Copy-Item $FilePath $backupPath -Force
        Write-Log "Created backup: $backupPath" "SUCCESS"

        # Optionally encrypt backup
        if ($EncryptBackups) {
            $encryptedPath = "$backupPath.encrypted"
            # Simple encryption implementation would go here
            Write-Log "Backup encryption enabled (placeholder)" "INFO"
        }

        return $backupPath
    }
    catch {
        Write-Log "Failed to create backup: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# Function to modify telemetry fields in storage file
function Update-TelemetryFields {
    param(
        [string]$StoragePath,
        [string]$EditorName,
        [array]$FieldsToModify = @()
    )

    Write-Log "Modifying telemetry fields for $EditorName..." "INFO"

    try {
        # Read and parse JSON
        $content = Get-Content $StoragePath -Raw -Encoding UTF8
        $jsonObject = $content | ConvertFrom-Json

        # Determine which fields to modify
        $fieldsToProcess = if ($FieldsToModify.Count -gt 0) {
            $FieldsToModify
        } elseif ($Enhanced) {
            $script:TelemetryFields.Keys
        } else {
            @("telemetry.machineId", "telemetry.devDeviceId")
        }

        $modifiedFields = @()

        foreach ($field in $fieldsToProcess) {
            if ($script:TelemetryFields.ContainsKey($field)) {
                $fieldConfig = $script:TelemetryFields[$field]
                $newValue = New-IdByType -Type $fieldConfig.Type

                # Add or update the field
                $jsonObject | Add-Member -Type NoteProperty -Name $field -Value $newValue -Force
                $modifiedFields += $field

                Write-Log "Updated $field`: $newValue" "SUCCESS"
            }
        }

        # Save updated JSON
        $updatedContent = $jsonObject | ConvertTo-Json -Depth 100
        $updatedContent | Set-Content $StoragePath -Encoding UTF8

        Write-Log "Successfully modified $($modifiedFields.Count) telemetry fields" "SUCCESS"
        return $modifiedFields
    }
    catch {
        Write-Log "Failed to modify telemetry fields: $($_.Exception.Message)" "ERROR"
        return @()
    }
}

# Function to process a single editor
function Invoke-EditorTelemetryModification {
    param(
        [string]$EditorKey,
        [array]$FieldsToModify = @()
    )

    $config = $script:EditorConfigs[$EditorKey]
    $storagePath = $config.Paths.Windows

    Write-Log "Processing $($config.Name)..." "INFO"

    # Check if storage file exists
    if (-not (Test-Path $storagePath)) {
        Write-Log "$($config.Name) storage file not found: $storagePath" "WARNING"
        return $false
    }

    # Create backup
    $backupPath = New-EnhancedBackup -FilePath $storagePath -EditorName $EditorKey
    if (-not $backupPath) {
        Write-Log "Skipping $($config.Name) due to backup failure" "ERROR"
        return $false
    }

    # Modify telemetry fields
    $modifiedFields = Update-TelemetryFields -StoragePath $storagePath -EditorName $config.Name -FieldsToModify $FieldsToModify

    if ($modifiedFields.Count -gt 0) {
        Write-Log "Successfully processed $($config.Name)" "SUCCESS"
        return $true
    } else {
        Write-Log "Failed to process $($config.Name)" "ERROR"
        return $false
    }
}

# Function to list backups
function Show-Backups {
    Write-Log "Listing backup files..." "INFO"

    $backupFiles = Get-ChildItem -Path $script:BackupDir -Filter "*.json" | Sort-Object LastWriteTime -Descending

    if ($backupFiles.Count -eq 0) {
        Write-Log "No backup files found" "WARNING"
        return
    }

    Write-Host "`nBackup Files:" -ForegroundColor Green
    Write-Host "=============" -ForegroundColor Green

    foreach ($file in $backupFiles) {
        $size = [math]::Round($file.Length / 1KB, 2)
        Write-Host "$($file.Name) - $($file.LastWriteTime) - $size KB" -ForegroundColor Cyan
    }

    Write-Host ""
}

# Function to clean old backups
function Remove-OldBackups {
    param([int]$DaysOld)

    if ($DaysOld -le 0) {
        Write-Log "Invalid days parameter for backup cleanup" "ERROR"
        return
    }

    Write-Log "Cleaning backups older than $DaysOld days..." "INFO"

    $cutoffDate = (Get-Date).AddDays(-$DaysOld)
    $oldBackups = Get-ChildItem -Path $script:BackupDir -Filter "*.json" | Where-Object { $_.LastWriteTime -lt $cutoffDate }

    if ($oldBackups.Count -eq 0) {
        Write-Log "No old backups found to clean" "INFO"
        return
    }

    foreach ($backup in $oldBackups) {
        try {
            Remove-Item $backup.FullName -Force
            Write-Log "Removed old backup: $($backup.Name)" "SUCCESS"
        }
        catch {
            Write-Log "Failed to remove backup $($backup.Name): $($_.Exception.Message)" "ERROR"
        }
    }

    Write-Log "Cleaned $($oldBackups.Count) old backup files" "SUCCESS"
}

# Function to restore from backup
function Restore-FromBackup {
    param(
        [string]$EditorKey,
        [string]$BackupDate
    )

    Write-Log "Restoring $EditorKey from backup..." "INFO"

    # Find backup file
    $backupPattern = if ($BackupDate) {
        "$EditorKey`_storage_$BackupDate*.json"
    } else {
        "$EditorKey`_storage_*.json"
    }

    $backupFiles = Get-ChildItem -Path $script:BackupDir -Filter $backupPattern | Sort-Object LastWriteTime -Descending

    if ($backupFiles.Count -eq 0) {
        Write-Log "No backup files found for $EditorKey" "ERROR"
        return $false
    }

    $latestBackup = $backupFiles[0]
    $config = $script:EditorConfigs[$EditorKey]
    $storagePath = $config.Paths.Windows

    try {
        Copy-Item $latestBackup.FullName $storagePath -Force
        Write-Log "Successfully restored from backup: $($latestBackup.Name)" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to restore from backup: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main execution function
function Invoke-EnhancedTelemetryModification {
    Write-Log "Starting Enhanced Telemetry ID Modification v0.1.0" "INFO"

    # Handle special operations first
    if ($ListBackups) {
        Show-Backups
        return
    }

    if ($CleanBackups -and $OlderThan -gt 0) {
        Remove-OldBackups -DaysOld $OlderThan
        return
    }

    if ($Restore) {
        if ($Editor) {
            $editorKeys = $Editor -split ","
            foreach ($editorKey in $editorKeys) {
                Restore-FromBackup -EditorKey $editorKey.Trim() -BackupDate $BackupDate
            }
        } else {
            Write-Log "Please specify editor(s) to restore with -Editor parameter" "ERROR"
        }
        return
    }

    # Detect installed editors
    $installedEditors = Get-InstalledEditors

    if ($installedEditors.Count -eq 0) {
        Write-Log "No supported editors found on this system" "ERROR"
        return
    }

    # Determine which editors to process
    $editorsToProcess = if ($Editor) {
        $Editor -split "," | ForEach-Object { $_.Trim() } | Where-Object { $installedEditors -contains $_ }
    } elseif ($All) {
        $installedEditors
    } else {
        # Default to VSCode if available, otherwise first found editor
        if ($installedEditors -contains "VSCode") {
            @("VSCode")
        } else {
            @($installedEditors[0])
        }
    }

    if ($editorsToProcess.Count -eq 0) {
        Write-Log "No valid editors specified or found" "ERROR"
        return
    }

    # Check for running processes
    $runningProcesses = Test-EditorProcesses -EditorKeys $editorsToProcess
    if ($runningProcesses.Count -gt 0) {
        Write-Log "Warning: The following editors are currently running:" "WARNING"
        foreach ($process in $runningProcesses) {
            Write-Log "  - $process" "WARNING"
        }
        Write-Log "Please close these editors before proceeding" "WARNING"

        if (-not $script:Silent) {
            $response = Read-Host "Continue anyway? (y/N)"
            if ($response -ne "y" -and $response -ne "Y") {
                Write-Log "Operation cancelled by user" "INFO"
                return
            }
        }
    }

    # Parse fields to modify
    $fieldsToModify = if ($Fields) {
        $Fields -split "," | ForEach-Object {
            $field = $_.Trim()
            if (-not $field.StartsWith("telemetry.")) {
                "telemetry.$field"
            } else {
                $field
            }
        }
    } else {
        @()
    }

    # Process each editor
    $successCount = 0
    $totalCount = $editorsToProcess.Count

    foreach ($editorKey in $editorsToProcess) {
        if (Invoke-EditorTelemetryModification -EditorKey $editorKey -FieldsToModify $fieldsToModify) {
            $successCount++
        }
    }

    # Summary
    Write-Log "Processing complete: $successCount/$totalCount editors successfully modified" "INFO"

    if ($successCount -gt 0) {
        Write-Log "Telemetry IDs have been successfully modified" "SUCCESS"
        Write-Log "Please restart the affected editors for changes to take effect" "INFO"
    }
}

# Execute main function
Invoke-EnhancedTelemetryModification
