{"version": "0.1.0", "description": "Extended Telemetry Fields Configuration - Additional tracking identifiers", "extended_fields": {"microsoft_ecosystem": {"telemetry.microsoftId": {"type": "uuid4", "description": "Microsoft account identifier", "required": false, "category": "microsoft", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.githubId": {"type": "uuid4", "description": "GitHub account identifier", "required": false, "category": "microsoft", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.azureId": {"type": "uuid4", "description": "Azure service identifier", "required": false, "category": "microsoft", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.officeId": {"type": "uuid4", "description": "Office integration identifier", "required": false, "category": "microsoft", "privacy_level": "medium", "platforms": ["windows", "macos"]}, "telemetry.teamsId": {"type": "uuid4", "description": "Microsoft Teams integration ID", "required": false, "category": "microsoft", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}, "telemetry.oneDriveId": {"type": "uuid4", "description": "OneDrive integration identifier", "required": false, "category": "microsoft", "privacy_level": "high", "platforms": ["windows", "macos"]}}, "development_tools": {"telemetry.debugId": {"type": "uuid4", "description": "Debug session identifier", "required": false, "category": "development", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}, "telemetry.buildId": {"type": "uuid4", "description": "Build process identifier", "required": false, "category": "development", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}, "telemetry.deployId": {"type": "uuid4", "description": "Deployment identifier", "required": false, "category": "development", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}, "telemetry.testId": {"type": "uuid4", "description": "Testing framework identifier", "required": false, "category": "development", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}, "telemetry.gitId": {"type": "hex32", "description": "Git repository identifier", "required": false, "category": "development", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}, "telemetry.terminalId": {"type": "uuid4", "description": "Integrated terminal identifier", "required": false, "category": "development", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}}, "extensions_plugins": {"telemetry.extensionId": {"type": "uuid4", "description": "Extension usage identifier", "required": false, "category": "extensions", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}, "telemetry.pluginId": {"type": "uuid4", "description": "Plugin identifier", "required": false, "category": "extensions", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}, "telemetry.themeId": {"type": "uuid4", "description": "Theme usage identifier", "required": false, "category": "extensions", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}, "telemetry.settingsId": {"type": "uuid4", "description": "Settings configuration identifier", "required": false, "category": "extensions", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}, "telemetry.keybindingId": {"type": "hex32", "description": "Keybinding configuration identifier", "required": false, "category": "extensions", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}, "telemetry.snippetId": {"type": "uuid4", "description": "Code snippet usage identifier", "required": false, "category": "extensions", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}}, "analytics_metrics": {"telemetry.analyticsId": {"type": "uuid4", "description": "Analytics collection identifier", "required": false, "category": "analytics", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.metricsId": {"type": "uuid4", "description": "Metrics collection identifier", "required": false, "category": "analytics", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.experimentId": {"type": "uuid4", "description": "A/B testing identifier", "required": false, "category": "analytics", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.cohortId": {"type": "uuid4", "description": "User cohort identifier", "required": false, "category": "analytics", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.segmentId": {"type": "uuid4", "description": "User segment identifier", "required": false, "category": "analytics", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.campaignId": {"type": "hex32", "description": "Marketing campaign identifier", "required": false, "category": "analytics", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}}, "privacy_compliance": {"telemetry.consentId": {"type": "uuid4", "description": "Consent tracking identifier", "required": false, "category": "privacy", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.privacyId": {"type": "uuid4", "description": "Privacy settings identifier", "required": false, "category": "privacy", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.gdprId": {"type": "uuid4", "description": "GDPR compliance identifier", "required": false, "category": "privacy", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.ccpaId": {"type": "uuid4", "description": "CCPA compliance identifier", "required": false, "category": "privacy", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.cookieId": {"type": "hex64", "description": "Cookie consent identifier", "required": false, "category": "privacy", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.trackingId": {"type": "hex64", "description": "Tracking prevention identifier", "required": false, "category": "privacy", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}}, "ai_copilot": {"telemetry.copilotId": {"type": "uuid4", "description": "GitHub Copilot identifier", "required": false, "category": "ai", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.aiAssistantId": {"type": "uuid4", "description": "AI assistant identifier", "required": false, "category": "ai", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.codeCompletionId": {"type": "uuid4", "description": "Code completion tracking ID", "required": false, "category": "ai", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}, "telemetry.suggestionId": {"type": "uuid4", "description": "AI suggestion identifier", "required": false, "category": "ai", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}, "telemetry.chatId": {"type": "uuid4", "description": "AI chat session identifier", "required": false, "category": "ai", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}}, "cloud_sync": {"telemetry.syncId": {"type": "uuid4", "description": "Settings sync identifier", "required": false, "category": "cloud", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.cloudId": {"type": "uuid4", "description": "Cloud service identifier", "required": false, "category": "cloud", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}, "telemetry.backupId": {"type": "uuid4", "description": "Backup service identifier", "required": false, "category": "cloud", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}, "telemetry.roamingId": {"type": "uuid4", "description": "Roaming profile identifier", "required": false, "category": "cloud", "privacy_level": "high", "platforms": ["windows", "macos", "linux"]}}, "short_identifiers": {"telemetry.shortId": {"type": "hex32", "description": "Short-term identifier", "required": false, "category": "temporary", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}, "telemetry.quickId": {"type": "hex16", "description": "Quick access identifier", "required": false, "category": "temporary", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}, "telemetry.tempId": {"type": "hex8", "description": "Temporary session identifier", "required": false, "category": "temporary", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}, "telemetry.nonce": {"type": "hex16", "description": "Cryptographic nonce", "required": false, "category": "temporary", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}}, "language_features": {"telemetry.languageId": {"type": "hex32", "description": "Programming language identifier", "required": false, "category": "language", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}, "telemetry.lspId": {"type": "uuid4", "description": "Language Server Protocol identifier", "required": false, "category": "language", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}, "telemetry.intellisenseId": {"type": "uuid4", "description": "IntelliSense usage identifier", "required": false, "category": "language", "privacy_level": "medium", "platforms": ["windows", "macos", "linux"]}, "telemetry.formatterId": {"type": "hex32", "description": "Code formatter identifier", "required": false, "category": "language", "privacy_level": "low", "platforms": ["windows", "macos", "linux"]}}}, "field_groups": {"essential": ["telemetry.machineId", "telemetry.devDeviceId"], "standard": ["telemetry.machineId", "telemetry.devDeviceId", "telemetry.sessionId", "telemetry.instanceId", "telemetry.optIn"], "comprehensive": ["telemetry.machineId", "telemetry.devDeviceId", "telemetry.sessionId", "telemetry.instanceId", "telemetry.userId", "telemetry.hardwareId", "telemetry.networkId", "telemetry.analyticsId", "telemetry.consentId"], "maximum": "all_fields"}, "modification_strategies": {"conservative": {"description": "Only modify essential tracking IDs", "fields": "essential", "backup_required": true}, "balanced": {"description": "Modify standard set of tracking IDs", "fields": "standard", "backup_required": true}, "aggressive": {"description": "Modify comprehensive set of tracking IDs", "fields": "comprehensive", "backup_required": true}, "nuclear": {"description": "Modify all possible tracking IDs", "fields": "maximum", "backup_required": true, "warning_required": true}}}