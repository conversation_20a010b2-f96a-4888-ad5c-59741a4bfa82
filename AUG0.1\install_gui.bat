@echo off
chcp 65001 >nul
title AUG 0.1 GUI版本 - 安装脚本

echo.
echo ========================================
echo  AUG 0.1 Enhanced Telemetry ID Modifier
echo  GUI版本安装脚本
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [信息] 检测到管理员权限
) else (
    echo [警告] 建议以管理员身份运行此脚本
)

echo.

:: 检查Python环境
echo [步骤 1/4] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python，请先安装Python 3.7或更高版本
    echo.
    echo 下载地址: https://www.python.org/downloads/
    echo.
    echo 安装Python时请确保勾选以下选项:
    echo - Add Python to PATH
    echo - Install pip
    echo - Install tkinter
    echo.
    pause
    exit /b 1
)

echo [成功] Python环境检查通过
python --version

:: 检查pip
echo.
echo [步骤 2/4] 检查pip包管理器...
pip --version >nul 2>&1
if errorlevel 1 (
    echo [错误] pip未找到，请重新安装Python并确保包含pip
    pause
    exit /b 1
)

echo [成功] pip检查通过
pip --version

:: 安装依赖
echo.
echo [步骤 3/4] 安装Python依赖包...

echo [信息] 正在安装psutil...
pip install psutil
if errorlevel 1 (
    echo [错误] psutil安装失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查网络连接
    echo 2. 尝试使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple psutil
    echo 3. 以管理员身份运行此脚本
    echo.
    pause
    exit /b 1
)

echo [成功] 依赖包安装完成

:: 验证安装
echo.
echo [步骤 4/4] 验证安装...

echo [信息] 检查tkinter模块...
python -c "import tkinter; print('tkinter: OK')" 2>nul
if errorlevel 1 (
    echo [错误] tkinter模块不可用
    echo.
    echo 解决方案:
    echo 1. 重新安装Python，确保包含tkinter
    echo 2. 在Linux上: sudo apt-get install python3-tk
    echo 3. 在macOS上: brew install python-tk
    echo.
    pause
    exit /b 1
)

echo [信息] 检查psutil模块...
python -c "import psutil; print('psutil version:', psutil.__version__)" 2>nul
if errorlevel 1 (
    echo [错误] psutil模块不可用
    pause
    exit /b 1
)

echo [信息] 检查GUI模块...
if not exist "gui_modules" (
    echo [错误] gui_modules目录不存在
    echo 请确保所有文件都已正确解压到当前目录
    pause
    exit /b 1
)

if not exist "gui_launcher_simple.py" (
    echo [错误] GUI启动文件不存在
    echo 请确保gui_launcher_simple.py文件存在
    pause
    exit /b 1
)

echo [成功] 所有模块检查通过

:: 创建桌面快捷方式（可选）
echo.
echo [可选] 是否创建桌面快捷方式？ (Y/N)
set /p create_shortcut=

if /i "%create_shortcut%"=="Y" (
    echo [信息] 正在创建桌面快捷方式...
    
    set "desktop=%USERPROFILE%\Desktop"
    set "current_dir=%CD%"
    
    echo @echo off > "%desktop%\AUG 0.1 GUI.bat"
    echo cd /d "%current_dir%" >> "%desktop%\AUG 0.1 GUI.bat"
    echo python gui_launcher_simple.py >> "%desktop%\AUG 0.1 GUI.bat"
    
    echo [成功] 桌面快捷方式已创建
)

:: 安装完成
echo.
echo ========================================
echo  安装完成！
echo ========================================
echo.
echo AUG 0.1 GUI版本已成功安装并配置完成。
echo.
echo 启动方式:
echo 1. 双击 "启动GUI.bat" 文件
echo 2. 运行命令: python gui_launcher_simple.py
if /i "%create_shortcut%"=="Y" (
    echo 3. 双击桌面上的 "AUG 0.1 GUI.bat" 快捷方式
)
echo.
echo 使用说明:
echo - 首次使用请先点击"检测编辑器"
echo - 推荐使用"增强模式"获得最佳保护
echo - 操作前建议关闭所有代码编辑器
echo - 详细说明请查看 README_GUI.md
echo.
echo 技术支持:
echo - 查看日志获取详细信息
echo - 检查 logs/ 目录中的日志文件
echo - 如有问题请联系技术支持
echo.

pause
