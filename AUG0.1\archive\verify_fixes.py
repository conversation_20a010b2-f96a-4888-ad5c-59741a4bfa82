"""
验证所有编辑器问题是否已修复
"""

import os
import json

def get_editor_paths():
    """获取所有支持的编辑器路径"""
    return {
        "VSCode": os.path.expandvars("%APPDATA%\\Code\\User\\globalStorage\\storage.json"),
        "VSCodeInsiders": os.path.expandvars("%APPDATA%\\Code - Insiders\\User\\globalStorage\\storage.json"),
        "Cursor": os.path.expandvars("%APPDATA%\\Cursor\\User\\globalStorage\\storage.json"),
        "VSCodium": os.path.expandvars("%APPDATA%\\VSCodium\\User\\globalStorage\\storage.json"),
        "CodeOSS": os.path.expandvars("%APPDATA%\\Code - OSS\\User\\globalStorage\\storage.json")
    }

def check_utf8_bom(file_path):
    """检查文件是否有UTF-8 BOM"""
    try:
        with open(file_path, 'rb') as f:
            first_bytes = f.read(3)
            return first_bytes == b'\xef\xbb\xbf'
    except:
        return False

def validate_json(file_path):
    """验证JSON文件是否有效"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            json.load(f)
        return True, "有效"
    except json.JSONDecodeError as e:
        return False, f"JSON错误: {str(e)}"
    except UnicodeDecodeError as e:
        return False, f"编码错误: {str(e)}"
    except Exception as e:
        return False, f"其他错误: {str(e)}"

def check_telemetry_fields(file_path):
    """检查遥测字段是否存在"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        required_fields = [
            "telemetry.machineId",
            "telemetry.devDeviceId"
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in data:
                missing_fields.append(field)
        
        return len(missing_fields) == 0, missing_fields
    except:
        return False, ["无法读取文件"]

def verify_all_fixes():
    """验证所有修复"""
    print("编辑器修复验证工具")
    print("=" * 50)
    
    editor_paths = get_editor_paths()
    total_editors = len(editor_paths)
    working_editors = 0
    issues_found = []
    
    for editor_name, file_path in editor_paths.items():
        print(f"\n检查编辑器: {editor_name}")
        print(f"路径: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"  ✗ 状态: 文件不存在")
            issues_found.append(f"{editor_name}: 配置文件不存在")
            continue
        
        print(f"  ✓ 状态: 文件存在")
        
        # 检查UTF-8 BOM
        has_bom = check_utf8_bom(file_path)
        if has_bom:
            print(f"  ⚠ 编码: 仍有UTF-8 BOM")
            issues_found.append(f"{editor_name}: 仍有UTF-8 BOM问题")
        else:
            print(f"  ✓ 编码: 无UTF-8 BOM")
        
        # 验证JSON格式
        is_valid, message = validate_json(file_path)
        if is_valid:
            print(f"  ✓ JSON: {message}")
        else:
            print(f"  ✗ JSON: {message}")
            issues_found.append(f"{editor_name}: {message}")
            continue
        
        # 检查遥测字段
        has_fields, missing = check_telemetry_fields(file_path)
        if has_fields:
            print(f"  ✓ 遥测: 必需字段完整")
            working_editors += 1
        else:
            print(f"  ✗ 遥测: 缺少字段 {missing}")
            issues_found.append(f"{editor_name}: 缺少遥测字段 {missing}")
    
    print(f"\n" + "=" * 50)
    print(f"验证结果总结")
    print(f"=" * 50)
    print(f"总编辑器数: {total_editors}")
    print(f"正常工作: {working_editors}")
    print(f"存在问题: {total_editors - working_editors}")
    
    if issues_found:
        print(f"\n发现的问题:")
        for issue in issues_found:
            print(f"  • {issue}")
        print(f"\n建议运行修复脚本: python fix_editor_issues.py")
        return False
    else:
        print(f"\n✓ 所有编辑器配置正常！")
        print(f"✓ 所有问题已成功修复！")
        return True

if __name__ == "__main__":
    try:
        success = verify_all_fixes()
        if success:
            print(f"\n🎉 验证通过！可以正常使用 Augment ID 修改器。")
        else:
            print(f"\n⚠ 验证失败！请运行修复脚本解决问题。")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
    finally:
        input("\n按Enter键退出...")
