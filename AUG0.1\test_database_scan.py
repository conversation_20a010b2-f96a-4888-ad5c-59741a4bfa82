#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库扫描测试脚本
专门测试和修复数据库扫描功能
"""

import os
import sys
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_databases():
    """创建测试数据库"""
    print("🔧 创建测试数据库...")
    
    test_dir = Path("test_databases")
    test_dir.mkdir(exist_ok=True)
    
    # 创建不同类型的测试数据库
    databases = [
        ("storage.db", "存储数据库"),
        ("cache.sqlite", "缓存数据库"),
        ("history.sqlite3", "历史数据库"),
        ("extensions.db", "扩展数据库"),
        ("telemetry.db", "遥测数据库")
    ]
    
    created_dbs = []
    
    for db_name, description in databases:
        db_path = test_dir / db_name
        
        try:
            with sqlite3.connect(str(db_path)) as conn:
                cursor = conn.cursor()
                
                # 创建测试表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS test_data (
                        id INTEGER PRIMARY KEY,
                        name TEXT,
                        value TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 插入测试数据
                test_data = [
                    ("normal_data", "正常数据"),
                    ("augment_related", "包含augment的数据"),
                    ("telemetry_info", "遥测信息"),
                    ("user_settings", "用户设置"),
                    ("cache_entry", "缓存条目")
                ]
                
                for name, value in test_data:
                    cursor.execute("INSERT OR REPLACE INTO test_data (name, value) VALUES (?, ?)", 
                                 (name, value))
                
                # 为遥测数据库添加特殊表
                if "telemetry" in db_name:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS telemetry_events (
                            event_id TEXT PRIMARY KEY,
                            event_type TEXT,
                            data TEXT,
                            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    
                    cursor.execute("""
                        INSERT OR REPLACE INTO telemetry_events (event_id, event_type, data) 
                        VALUES (?, ?, ?)
                    """, ("augment_event_1", "user_action", "augment related telemetry"))
                
                conn.commit()
                
            created_dbs.append((str(db_path), description))
            print(f"✅ 创建 {description}: {db_path}")
            
        except Exception as e:
            print(f"❌ 创建数据库失败 {db_name}: {e}")
    
    return created_dbs

def test_database_detection():
    """测试数据库检测功能"""
    print("\n🔍 测试数据库检测功能...")
    
    try:
        from gui_modules.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试文件头检测
        print("测试SQLite文件头检测...")
        test_files = [
            "test_databases/storage.db",
            "test_databases/cache.sqlite",
            "gui_launcher_simple.py"  # 非数据库文件
        ]
        
        for file_path in test_files:
            if os.path.exists(file_path):
                is_sqlite = db_manager._is_sqlite_file_by_header(file_path)
                print(f"  {file_path}: {'SQLite' if is_sqlite else '非SQLite'}")
        
        # 测试数据库文件名检测
        print("\n测试数据库文件名检测...")
        test_names = [
            "storage.db", "cache.sqlite", "history.sqlite3",
            "extensions.db", "telemetry.db", "normal.txt"
        ]
        
        for name in test_names:
            is_db = db_manager._is_database_file(name)
            print(f"  {name}: {'数据库文件' if is_db else '普通文件'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库检测测试失败: {e}")
        return False

def test_database_scanning():
    """测试数据库扫描功能"""
    print("\n🔍 测试数据库扫描功能...")
    
    try:
        from gui_modules.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 扫描测试数据库
        test_paths = [str(Path("test_databases").absolute())]
        databases = db_manager.scan_databases(test_paths)
        
        print(f"✅ 扫描结果: 发现 {len(databases)} 个数据库")
        
        for db_info in databases:
            print(f"\n📁 数据库: {os.path.basename(db_info.get('path', ''))}")
            print(f"   路径: {db_info.get('path', '')}")
            print(f"   大小: {db_manager.format_size(db_info.get('size', 0))}")
            print(f"   可访问: {'是' if db_info.get('accessible', False) else '否'}")
            print(f"   表数量: {len(db_info.get('tables', []))}")
            print(f"   总记录: {db_info.get('total_records', 0)}")
            print(f"   Augment记录: {db_info.get('augment_records', 0)}")
            
            if db_info.get('error'):
                print(f"   错误: {db_info.get('error')}")
        
        return len(databases) > 0
        
    except Exception as e:
        print(f"❌ 数据库扫描测试失败: {e}")
        return False

def test_real_editor_databases():
    """测试真实编辑器数据库扫描"""
    print("\n🔍 测试真实编辑器数据库扫描...")
    
    try:
        from gui_modules.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 扫描真实编辑器路径
        databases = db_manager.scan_databases()
        
        print(f"✅ 真实扫描结果: 发现 {len(databases)} 个数据库")
        
        if databases:
            print("\n发现的真实数据库:")
            for i, db_info in enumerate(databases[:10]):  # 只显示前10个
                print(f"\n{i+1}. {os.path.basename(db_info.get('path', ''))}")
                print(f"   路径: {db_info.get('path', '')}")
                print(f"   大小: {db_manager.format_size(db_info.get('size', 0))}")
                print(f"   可访问: {'是' if db_info.get('accessible', False) else '否'}")
                
                if db_info.get('accessible', False):
                    print(f"   表数量: {len(db_info.get('tables', []))}")
                    print(f"   总记录: {db_info.get('total_records', 0)}")
                    print(f"   Augment记录: {db_info.get('augment_records', 0)}")
                    
                    # 显示表名
                    tables = db_info.get('tables', [])
                    if tables:
                        print(f"   表名: {', '.join(tables[:5])}")
                        if len(tables) > 5:
                            print(f"         ... 还有 {len(tables) - 5} 个表")
                
                if db_info.get('error'):
                    print(f"   错误: {db_info.get('error')}")
            
            if len(databases) > 10:
                print(f"\n... 还有 {len(databases) - 10} 个数据库")
        else:
            print("未发现真实的编辑器数据库文件")
            print("\n可能的原因:")
            print("1. 编辑器未使用SQLite数据库")
            print("2. 数据库文件位置不在扫描路径中")
            print("3. 数据库文件被其他程序锁定")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实数据库扫描测试失败: {e}")
        return False

def test_database_operations():
    """测试数据库操作功能"""
    print("\n🔍 测试数据库操作功能...")
    
    try:
        from gui_modules.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 使用测试数据库
        test_db = "test_databases/telemetry.db"
        
        if not os.path.exists(test_db):
            print("❌ 测试数据库不存在")
            return False
        
        print(f"使用测试数据库: {test_db}")
        
        # 测试备份功能
        backup_path = db_manager.backup_database(test_db)
        if backup_path and os.path.exists(backup_path):
            print(f"✅ 数据库备份成功: {backup_path}")
            
            # 清理备份文件
            os.remove(backup_path)
            info_file = Path(backup_path).with_suffix('.info')
            if info_file.exists():
                info_file.unlink()
        else:
            print("❌ 数据库备份失败")
        
        # 测试清理功能
        result = db_manager.clean_augment_records(test_db)
        if result["success"]:
            print(f"✅ Augment记录清理成功: 删除 {result['deleted_records']} 条记录")
            if result["affected_tables"]:
                print("   受影响的表:")
                for table_info in result["affected_tables"]:
                    print(f"     {table_info['table']}: {table_info['deleted_records']} 条")
        else:
            print(f"❌ Augment记录清理失败: {result.get('error', '未知错误')}")
        
        # 测试压缩功能
        if db_manager.vacuum_database(test_db):
            print("✅ 数据库压缩成功")
        else:
            print("❌ 数据库压缩失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    try:
        test_dir = Path("test_databases")
        if test_dir.exists():
            import shutil
            shutil.rmtree(test_dir)
            print("✅ 测试文件清理完成")
    except Exception as e:
        print(f"⚠️ 清理测试文件失败: {e}")

def main():
    """主测试函数"""
    print("🚀 数据库扫描功能测试开始")
    print("=" * 50)
    
    tests = [
        ("创建测试数据库", create_test_databases),
        ("数据库检测功能", test_database_detection),
        ("数据库扫描功能", test_database_scanning),
        ("真实编辑器数据库扫描", test_real_editor_databases),
        ("数据库操作功能", test_database_operations)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                failed += 1
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            failed += 1
    
    # 清理测试文件
    cleanup_test_files()
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed} 个通过, {failed} 个失败")
    
    if failed == 0:
        print("🎉 数据库扫描功能完全正常！")
    elif passed > failed:
        print("✅ 数据库扫描功能基本正常")
    else:
        print("⚠️ 数据库扫描功能需要进一步修复")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
