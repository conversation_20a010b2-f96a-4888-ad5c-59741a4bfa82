#!/usr/bin/env bash
#
# telemetry_test_suite.sh
# Comprehensive testing suite for AUG 0.1 Enhanced Telemetry ID Modifier (Unix)
# Version: 1.0
# Author: AI Analysis System

set -e

# Configuration
VERBOSE=false
GENERATE_REPORT=false
TEST_MODIFICATION=false
START_TIME=$(date '+%Y-%m-%d %H:%M:%S')

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test results
PASS_COUNT=0
WARNING_COUNT=0
FAIL_COUNT=0
TEST_RESULTS=()

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --verbose)
            VERBOSE=true
            shift
            ;;
        --generate-report)
            GENERATE_REPORT=true
            shift
            ;;
        --test-modification)
            TEST_MODIFICATION=true
            shift
            ;;
        --help)
            echo "AUG 0.1 Telemetry Testing Suite (Unix)"
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --verbose           Show detailed output"
            echo "  --generate-report   Generate JSON report"
            echo "  --test-modification Test telemetry modification (safe)"
            echo "  --help              Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Utility functions
log_info() {
    echo -e "${CYAN}$1${NC}"
}

log_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

log_error() {
    echo -e "${RED}✗ $1${NC}"
}

log_detail() {
    if [ "$VERBOSE" = true ]; then
        echo -e "  ${BLUE}$1${NC}"
    fi
}

# Test functions
test_editor_detection() {
    echo -e "${YELLOW}Test 1: Editor Detection and Telemetry Analysis${NC}"
    
    local status="PASS"
    local details=()
    local errors=()
    local detected_editors=0
    local total_telemetry_fields=0
    
    # Determine OS and set paths
    local os_name=$(uname -s)
    declare -A editor_paths
    
    case "$os_name" in
        Darwin)  # macOS
            editor_paths["vscode"]="$HOME/Library/Application Support/Code/User/globalStorage/storage.json"
            editor_paths["cursor"]="$HOME/Library/Application Support/Cursor/User/globalStorage/storage.json"
            editor_paths["vscodium"]="$HOME/Library/Application Support/VSCodium/User/globalStorage/storage.json"
            ;;
        Linux)
            editor_paths["vscode"]="$HOME/.config/Code/User/globalStorage/storage.json"
            editor_paths["cursor"]="$HOME/.config/Cursor/User/globalStorage/storage.json"
            editor_paths["vscodium"]="$HOME/.config/VSCodium/User/globalStorage/storage.json"
            ;;
        *)
            errors+=("Unsupported operating system: $os_name")
            status="FAIL"
            ;;
    esac
    
    for editor in "${!editor_paths[@]}"; do
        local path="${editor_paths[$editor]}"
        if [ -f "$path" ]; then
            ((detected_editors++))
            details+=("✓ $editor detected at: $path")
            
            if command -v jq &> /dev/null; then
                local field_count=$(jq -r 'keys[] | select(startswith("telemetry."))' "$path" 2>/dev/null | wc -l)
                ((total_telemetry_fields += field_count))
                details+=("  → Current telemetry fields: $field_count")
                
                if [ "$VERBOSE" = true ]; then
                    jq -r 'to_entries[] | select(.key | startswith("telemetry.")) | "    \(.key): \(.value)"' "$path" 2>/dev/null | while read -r line; do
                        details+=("$line")
                    done
                fi
            else
                details+=("  → jq not available, cannot count fields")
            fi
        else
            details+=("✗ $editor not found")
        fi
    done
    
    details+=("Summary: $detected_editors editors detected, $total_telemetry_fields total telemetry fields")
    
    if [ $detected_editors -eq 0 ]; then
        status="FAIL"
        errors+=("No supported editors found on this system")
    fi
    
    # Display results
    for detail in "${details[@]}"; do
        log_detail "$detail"
    done
    
    for error in "${errors[@]}"; do
        log_error "$error"
    done
    
    case "$status" in
        "PASS") log_success "Editor Detection: PASS"; ((PASS_COUNT++)) ;;
        "WARNING") log_warning "Editor Detection: WARNING"; ((WARNING_COUNT++)) ;;
        "FAIL") log_error "Editor Detection: FAIL"; ((FAIL_COUNT++)) ;;
    esac
    
    TEST_RESULTS+=("{\"test\":\"Editor Detection\",\"status\":\"$status\",\"details\":$(printf '%s\n' "${details[@]}" | jq -R . | jq -s .),\"errors\":$(printf '%s\n' "${errors[@]}" | jq -R . | jq -s .)}")
}

test_dependencies() {
    echo -e "${YELLOW}Test 2: Dependency Check${NC}"
    
    local status="PASS"
    local details=()
    local errors=()
    
    local deps=("jq" "hexdump" "uuidgen")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if command -v "$dep" &> /dev/null; then
            details+=("✓ $dep available")
        else
            details+=("✗ $dep missing")
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        status="WARNING"
        errors+=("Missing dependencies: ${missing_deps[*]}")
        errors+=("Install with: brew install ${missing_deps[*]} (macOS) or apt install ${missing_deps[*]} (Ubuntu)")
    fi
    
    # Display results
    for detail in "${details[@]}"; do
        log_detail "$detail"
    done
    
    for error in "${errors[@]}"; do
        log_warning "$error"
    done
    
    case "$status" in
        "PASS") log_success "Dependencies: PASS"; ((PASS_COUNT++)) ;;
        "WARNING") log_warning "Dependencies: WARNING"; ((WARNING_COUNT++)) ;;
        "FAIL") log_error "Dependencies: FAIL"; ((FAIL_COUNT++)) ;;
    esac
    
    TEST_RESULTS+=("{\"test\":\"Dependencies\",\"status\":\"$status\",\"details\":$(printf '%s\n' "${details[@]}" | jq -R . | jq -s .),\"errors\":$(printf '%s\n' "${errors[@]}" | jq -R . | jq -s .)}")
}

test_configuration() {
    echo -e "${YELLOW}Test 3: Configuration Validation${NC}"
    
    local status="PASS"
    local details=()
    local errors=()
    
    local configs=("config/telemetry_config.json" "config/extended_telemetry_fields.json")
    
    for config in "${configs[@]}"; do
        if [ -f "$config" ]; then
            details+=("✓ $(basename "$config") found")
            
            if command -v jq &> /dev/null; then
                if jq . "$config" > /dev/null 2>&1; then
                    details+=("  → Valid JSON format")
                else
                    errors+=("Invalid JSON in $config")
                    status="FAIL"
                fi
            else
                details+=("  → Cannot validate JSON (jq not available)")
            fi
        else
            errors+=("Configuration file not found: $config")
            status="WARNING"
        fi
    done
    
    # Display results
    for detail in "${details[@]}"; do
        log_detail "$detail"
    done
    
    for error in "${errors[@]}"; do
        log_error "$error"
    done
    
    case "$status" in
        "PASS") log_success "Configuration: PASS"; ((PASS_COUNT++)) ;;
        "WARNING") log_warning "Configuration: WARNING"; ((WARNING_COUNT++)) ;;
        "FAIL") log_error "Configuration: FAIL"; ((FAIL_COUNT++)) ;;
    esac
    
    TEST_RESULTS+=("{\"test\":\"Configuration\",\"status\":\"$status\",\"details\":$(printf '%s\n' "${details[@]}" | jq -R . | jq -s .),\"errors\":$(printf '%s\n' "${errors[@]}" | jq -R . | jq -s .)}")
}

test_scripts() {
    echo -e "${YELLOW}Test 4: Script Validation${NC}"
    
    local status="PASS"
    local details=()
    local errors=()
    
    local scripts=("scripts/enhanced_id_modifier.sh" "scripts/enhanced_id_modifier.ps1")
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            details+=("✓ $(basename "$script") found")
            
            local size=$(du -h "$script" | cut -f1)
            details+=("  → Size: $size")
            
            # Check if script is executable
            if [ -x "$script" ]; then
                details+=("  → Executable: ✓")
            else
                details+=("  → Executable: ✗ (use chmod +x)")
                status="WARNING"
            fi
        else
            errors+=("Script not found: $script")
            status="WARNING"
        fi
    done
    
    # Display results
    for detail in "${details[@]}"; do
        log_detail "$detail"
    done
    
    for error in "${errors[@]}"; do
        log_warning "$error"
    done
    
    case "$status" in
        "PASS") log_success "Scripts: PASS"; ((PASS_COUNT++)) ;;
        "WARNING") log_warning "Scripts: WARNING"; ((WARNING_COUNT++)) ;;
        "FAIL") log_error "Scripts: FAIL"; ((FAIL_COUNT++)) ;;
    esac
    
    TEST_RESULTS+=("{\"test\":\"Scripts\",\"status\":\"$status\",\"details\":$(printf '%s\n' "${details[@]}" | jq -R . | jq -s .),\"errors\":$(printf '%s\n' "${errors[@]}" | jq -R . | jq -s .)}")
}

test_processes() {
    echo -e "${YELLOW}Test 5: Process Detection${NC}"
    
    local status="PASS"
    local details=()
    local errors=()
    
    local processes=("code" "cursor" "codium")
    local running_processes=()
    
    for proc in "${processes[@]}"; do
        if pgrep -f "$proc" > /dev/null 2>&1; then
            details+=("⚠ $proc is running")
            running_processes+=("$proc")
        else
            details+=("✓ $proc not running")
        fi
    done
    
    if [ ${#running_processes[@]} -gt 0 ]; then
        status="WARNING"
        errors+=("${#running_processes[@]} editor process(es) detected: ${running_processes[*]}")
        errors+=("Recommendation: Close editors before running telemetry modifications")
    fi
    
    # Display results
    for detail in "${details[@]}"; do
        log_detail "$detail"
    done
    
    for error in "${errors[@]}"; do
        log_warning "$error"
    done
    
    case "$status" in
        "PASS") log_success "Processes: PASS"; ((PASS_COUNT++)) ;;
        "WARNING") log_warning "Processes: WARNING"; ((WARNING_COUNT++)) ;;
        "FAIL") log_error "Processes: FAIL"; ((FAIL_COUNT++)) ;;
    esac
    
    TEST_RESULTS+=("{\"test\":\"Processes\",\"status\":\"$status\",\"details\":$(printf '%s\n' "${details[@]}" | jq -R . | jq -s .),\"errors\":$(printf '%s\n' "${errors[@]}" | jq -R . | jq -s .)}")
}

test_modification() {
    echo -e "${YELLOW}Test 6: Telemetry Modification (Safe Test)${NC}"
    
    local status="PASS"
    local details=()
    local errors=()
    
    local test_file="test_telemetry_storage.json"
    
    # Create test data
    cat > "$test_file" << 'EOF'
{
  "telemetry.machineId": "test-machine-id-12345",
  "telemetry.devDeviceId": "test-device-id-67890",
  "telemetry.sessionId": "test-session-id-abcdef",
  "otherData": "should-remain-unchanged",
  "settings": {
    "theme": "dark",
    "fontSize": 14
  }
}
EOF
    
    if [ -f "$test_file" ]; then
        details+=("✓ Created test storage file")
        
        # Simulate field modification
        if command -v jq &> /dev/null; then
            local original_machine_id=$(jq -r '."telemetry.machineId"' "$test_file")
            local original_device_id=$(jq -r '."telemetry.devDeviceId"' "$test_file")
            
            # Generate new IDs
            local new_machine_id=$(hexdump -n 32 -v -e '1/1 "%02x"' /dev/urandom)
            local new_device_id
            if command -v uuidgen &> /dev/null; then
                new_device_id=$(uuidgen | tr '[:upper:]' '[:lower:]')
            else
                new_device_id="$(hexdump -n 16 -v -e '1/1 "%02x"' /dev/urandom | sed 's/\(..\)/\1-/g' | sed 's/-$//')"
            fi
            
            # Apply modifications
            jq --arg mid "$new_machine_id" --arg did "$new_device_id" \
               '."telemetry.machineId" = $mid | ."telemetry.devDeviceId" = $did' \
               "$test_file" > "${test_file}.tmp" && mv "${test_file}.tmp" "$test_file"
            
            details+=("✓ Modified telemetry fields")
            
            # Verify modification
            local modified_machine_id=$(jq -r '."telemetry.machineId"' "$test_file")
            local modified_device_id=$(jq -r '."telemetry.devDeviceId"' "$test_file")
            local other_data=$(jq -r '.otherData' "$test_file")
            
            if [ "$modified_machine_id" = "$new_machine_id" ] && \
               [ "$modified_device_id" = "$new_device_id" ] && \
               [ "$other_data" = "should-remain-unchanged" ]; then
                details+=("✓ Field modification successful")
                details+=("  → Machine ID changed: $([ "$original_machine_id" != "$modified_machine_id" ] && echo "✓" || echo "✗")")
                details+=("  → Device ID changed: $([ "$original_device_id" != "$modified_device_id" ] && echo "✓" || echo "✗")")
                details+=("  → Other data preserved: ✓")
            else
                status="FAIL"
                errors+=("Field modification verification failed")
            fi
        else
            status="WARNING"
            errors+=("jq not available, cannot test modification")
        fi
    else
        status="FAIL"
        errors+=("Failed to create test file")
    fi
    
    # Clean up
    rm -f "$test_file" "${test_file}.tmp"
    
    # Display results
    for detail in "${details[@]}"; do
        log_detail "$detail"
    done
    
    for error in "${errors[@]}"; do
        log_error "$error"
    done
    
    case "$status" in
        "PASS") log_success "Modification Test: PASS"; ((PASS_COUNT++)) ;;
        "WARNING") log_warning "Modification Test: WARNING"; ((WARNING_COUNT++)) ;;
        "FAIL") log_error "Modification Test: FAIL"; ((FAIL_COUNT++)) ;;
    esac
    
    TEST_RESULTS+=("{\"test\":\"Modification Test\",\"status\":\"$status\",\"details\":$(printf '%s\n' "${details[@]}" | jq -R . | jq -s .),\"errors\":$(printf '%s\n' "${errors[@]}" | jq -R . | jq -s .)}")
}

# Main execution
echo -e "${GREEN}AUG 0.1 Telemetry Testing Suite (Unix)${NC}"
echo -e "${GREEN}=====================================${NC}"
echo -e "${CYAN}Started: $START_TIME${NC}"
echo ""

log_info "Running comprehensive test suite..."
echo ""

# Run tests
test_editor_detection
echo ""
test_dependencies
echo ""
test_configuration
echo ""
test_scripts
echo ""
test_processes
echo ""

if [ "$TEST_MODIFICATION" = true ]; then
    test_modification
    echo ""
fi

# Summary
TOTAL_TESTS=$((PASS_COUNT + WARNING_COUNT + FAIL_COUNT))
END_TIME=$(date '+%Y-%m-%d %H:%M:%S')

echo -e "${GREEN}Test Results Summary${NC}"
echo -e "${GREEN}===================${NC}"
echo -e "${CYAN}  Total Tests: $TOTAL_TESTS${NC}"
echo -e "${GREEN}  Passed: $PASS_COUNT${NC}"
echo -e "${YELLOW}  Warnings: $WARNING_COUNT${NC}"
echo -e "${RED}  Failed: $FAIL_COUNT${NC}"
echo -e "${CYAN}  Completed: $END_TIME${NC}"

# Generate report if requested
if [ "$GENERATE_REPORT" = true ] && command -v jq &> /dev/null; then
    local report_file="TelemetryTestReport_$(date '+%Y%m%d_%H%M%S').json"
    
    cat > "$report_file" << EOF
{
  "testSuite": "AUG 0.1 Telemetry Testing Suite (Unix)",
  "version": "1.0",
  "timestamp": "$START_TIME",
  "endTime": "$END_TIME",
  "summary": {
    "total": $TOTAL_TESTS,
    "passed": $PASS_COUNT,
    "warnings": $WARNING_COUNT,
    "failed": $FAIL_COUNT
  },
  "results": [$(IFS=','; echo "${TEST_RESULTS[*]}")],
  "systemInfo": {
    "os": "$(uname -s)",
    "version": "$(uname -r)",
    "architecture": "$(uname -m)",
    "hostname": "$(hostname)",
    "user": "$(whoami)"
  }
}
EOF
    
    echo ""
    log_success "Detailed report saved to: $report_file"
fi

# Exit with appropriate code
if [ $FAIL_COUNT -gt 0 ]; then
    exit 1
elif [ $WARNING_COUNT -gt 0 ]; then
    exit 2
else
    exit 0
fi
