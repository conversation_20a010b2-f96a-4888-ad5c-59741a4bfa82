#!/usr/bin/env bash
#
# enhanced_id_modifier.sh
#
# Description: Enhanced telemetry ID modifier for multiple code editors
# Supports VS Code, Cursor, VSCodium and other VS Code-based editors
# Version: 0.1.0

set -e  # Exit immediately if a command exits with a non-zero status
set -u  # Treat unset variables as an error

# Script configuration
SCRIPT_VERSION="0.1.0"
SCRIPT_NAME="Enhanced Telemetry ID Modifier"

# Default values
ENHANCED_MODE=false
ALL_EDITORS=false
SILENT_MODE=false
ENCRYPT_BACKUPS=false
LIST_BACKUPS=false
RESTORE_MODE=false
CLEAN_BACKUPS=false
EDITOR_LIST=""
FIELDS_LIST=""
BACKUP_DATE=""
OLDER_THAN=0
LOG_LEVEL="Normal"

# Directory setup
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="$PROJECT_DIR/backups"
LOG_DIR="$PROJECT_DIR/logs"
CONFIG_DIR="$PROJECT_DIR/config"

# Create directories if they don't exist
mkdir -p "$BACKUP_DIR" "$LOG_DIR" "$CONFIG_DIR"

# Text formatting
BOLD="\033[1m"
RED="\033[31m"
GREEN="\033[32m"
YELLOW="\033[33m"
BLUE="\033[34m"
CYAN="\033[36m"
GRAY="\033[90m"
RESET="\033[0m"

# Logging functions
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local log_entry="[$timestamp] [$level] $message"
    
    # Console output based on log level and silent mode
    if [ "$SILENT_MODE" = false ]; then
        case "$level" in
            "ERROR")
                echo -e "${RED}$log_entry${RESET}" >&2
                ;;
            "WARNING")
                echo -e "${YELLOW}$log_entry${RESET}"
                ;;
            "SUCCESS")
                echo -e "${GREEN}$log_entry${RESET}"
                ;;
            "INFO")
                if [ "$LOG_LEVEL" != "Minimal" ]; then
                    echo -e "${CYAN}$log_entry${RESET}"
                fi
                ;;
            "VERBOSE")
                if [ "$LOG_LEVEL" = "Verbose" ]; then
                    echo -e "${GRAY}$log_entry${RESET}"
                fi
                ;;
            *)
                echo "$log_entry"
                ;;
        esac
    fi
    
    # File logging
    local log_file="$LOG_DIR/enhanced_id_modifier_$(date '+%Y%m%d').log"
    echo "$log_entry" >> "$log_file"
}

log_info() { log_message "INFO" "$1"; }
log_success() { log_message "SUCCESS" "$1"; }
log_warning() { log_message "WARNING" "$1"; }
log_error() { log_message "ERROR" "$1"; }
log_verbose() { log_message "VERBOSE" "$1"; }

# Show help message
show_help() {
    echo -e "${GREEN}$SCRIPT_NAME v$SCRIPT_VERSION${RESET}"
    echo ""
    echo "Description: Advanced telemetry ID modification for multiple code editors"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --enhanced          Enable enhanced mode with all features"
    echo "  --all               Process all detected editors"
    echo "  --editor <names>    Specify editors (vscode,cursor,vscodium)"
    echo "  --fields <fields>   Specify fields to modify"
    echo "  --silent            Run in silent mode"
    echo "  --encrypt-backups   Encrypt backup files"
    echo "  --list-backups      List all backup files"
    echo "  --restore           Restore from backup"
    echo "  --clean-backups     Clean old backup files"
    echo "  --backup-date <date> Specify backup date for restore"
    echo "  --older-than <days> Clean backups older than specified days"
    echo "  --log-level <level> Set log level (Minimal,Normal,Verbose)"
    echo "  --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --enhanced"
    echo "  $0 --editor 'vscode,cursor'"
    echo "  $0 --all --encrypt-backups"
    echo ""
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --enhanced)
                ENHANCED_MODE=true
                shift
                ;;
            --all)
                ALL_EDITORS=true
                shift
                ;;
            --editor)
                EDITOR_LIST="$2"
                shift 2
                ;;
            --fields)
                FIELDS_LIST="$2"
                shift 2
                ;;
            --silent)
                SILENT_MODE=true
                shift
                ;;
            --encrypt-backups)
                ENCRYPT_BACKUPS=true
                shift
                ;;
            --list-backups)
                LIST_BACKUPS=true
                shift
                ;;
            --restore)
                RESTORE_MODE=true
                shift
                ;;
            --clean-backups)
                CLEAN_BACKUPS=true
                shift
                ;;
            --backup-date)
                BACKUP_DATE="$2"
                shift 2
                ;;
            --older-than)
                OLDER_THAN="$2"
                shift 2
                ;;
            --log-level)
                LOG_LEVEL="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    # Check for required tools
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi
    
    if ! command -v hexdump &> /dev/null; then
        missing_deps+=("hexdump")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        log_info "Please install the missing dependencies:"
        log_info "  On macOS: brew install ${missing_deps[*]}"
        log_info "  On Ubuntu/Debian: sudo apt install ${missing_deps[*]}"
        log_info "  On Fedora/RHEL: sudo dnf install ${missing_deps[*]}"
        exit 1
    fi
}

# Editor configurations
declare -A EDITOR_CONFIGS
setup_editor_configs() {
    local os_name=$(uname -s)
    
    case "$os_name" in
        Darwin)  # macOS
            EDITOR_CONFIGS["vscode"]="$HOME/Library/Application Support/Code/User/globalStorage/storage.json"
            EDITOR_CONFIGS["vscode-insiders"]="$HOME/Library/Application Support/Code - Insiders/User/globalStorage/storage.json"
            EDITOR_CONFIGS["cursor"]="$HOME/Library/Application Support/Cursor/User/globalStorage/storage.json"
            EDITOR_CONFIGS["vscodium"]="$HOME/Library/Application Support/VSCodium/User/globalStorage/storage.json"
            EDITOR_CONFIGS["code-oss"]="$HOME/Library/Application Support/Code - OSS/User/globalStorage/storage.json"
            ;;
        Linux)
            EDITOR_CONFIGS["vscode"]="$HOME/.config/Code/User/globalStorage/storage.json"
            EDITOR_CONFIGS["vscode-insiders"]="$HOME/.config/Code - Insiders/User/globalStorage/storage.json"
            EDITOR_CONFIGS["cursor"]="$HOME/.config/Cursor/User/globalStorage/storage.json"
            EDITOR_CONFIGS["vscodium"]="$HOME/.config/VSCodium/User/globalStorage/storage.json"
            EDITOR_CONFIGS["code-oss"]="$HOME/.config/Code - OSS/User/globalStorage/storage.json"
            ;;
        MINGW*|MSYS*|CYGWIN*)  # Windows
            local appdata="${APPDATA:-}"
            if [ -z "$appdata" ]; then
                log_error "APPDATA environment variable not found"
                exit 1
            fi
            EDITOR_CONFIGS["vscode"]="$appdata/Code/User/globalStorage/storage.json"
            EDITOR_CONFIGS["vscode-insiders"]="$appdata/Code - Insiders/User/globalStorage/storage.json"
            EDITOR_CONFIGS["cursor"]="$appdata/Cursor/User/globalStorage/storage.json"
            EDITOR_CONFIGS["vscodium"]="$appdata/VSCodium/User/globalStorage/storage.json"
            EDITOR_CONFIGS["code-oss"]="$appdata/Code - OSS/User/globalStorage/storage.json"
            ;;
        *)
            log_error "Unsupported operating system: $os_name"
            exit 1
            ;;
    esac
}

# Enhanced ID generation functions
generate_enhanced_machine_id() {
    log_verbose "Generating enhanced machine ID..."
    
    # Generate 32 bytes of random data and convert to hex
    hexdump -n 32 -v -e '1/1 "%02x"' /dev/urandom
}

generate_enhanced_device_id() {
    log_verbose "Generating enhanced device ID..."
    
    # Generate UUID v4
    if command -v uuidgen &> /dev/null; then
        uuidgen | tr '[:upper:]' '[:lower:]'
    else
        # Fallback method using /dev/urandom
        local uuid=$(hexdump -n 16 -v -e '1/1 "%02x"' /dev/urandom)
        echo "${uuid:0:8}-${uuid:8:4}-4${uuid:13:3}-${uuid:16:4}-${uuid:20:12}"
    fi
}

generate_session_id() {
    log_verbose "Generating session ID..."
    generate_enhanced_device_id
}

generate_random_timestamp() {
    log_verbose "Generating random timestamp..."
    
    # Generate a random date within the last year
    local now=$(date +%s)
    local year_ago=$((now - 31536000))  # 365 days in seconds
    local random_time=$((year_ago + RANDOM % 31536000))
    
    date -d "@$random_time" -Iseconds 2>/dev/null || date -r "$random_time" -Iseconds 2>/dev/null || date -Iseconds
}

generate_random_boolean() {
    [ $((RANDOM % 2)) -eq 1 ] && echo "true" || echo "false"
}

# Generate ID based on type
generate_id_by_type() {
    local type="$1"

    case "$type" in
        "hex64")
            generate_enhanced_machine_id
            ;;
        "uuid4")
            generate_enhanced_device_id
            ;;
        "timestamp")
            generate_random_timestamp
            ;;
        "boolean")
            generate_random_boolean
            ;;
        *)
            generate_enhanced_device_id
            ;;
    esac
}

# Detect installed editors
detect_installed_editors() {
    log_info "Detecting installed editors..."

    local installed_editors=()

    for editor in "${!EDITOR_CONFIGS[@]}"; do
        local storage_path="${EDITOR_CONFIGS[$editor]}"
        if [ -f "$storage_path" ]; then
            installed_editors+=("$editor")
            log_success "Found $editor at: $storage_path"
        else
            log_verbose "$editor not found"
        fi
    done

    printf '%s\n' "${installed_editors[@]}"
}

# Check if editor processes are running
check_editor_processes() {
    local editors=("$@")
    local running_processes=()

    # Define process names for each editor
    declare -A PROCESS_NAMES
    PROCESS_NAMES["vscode"]="code"
    PROCESS_NAMES["vscode-insiders"]="code-insiders"
    PROCESS_NAMES["cursor"]="cursor"
    PROCESS_NAMES["vscodium"]="codium"
    PROCESS_NAMES["code-oss"]="code-oss"

    for editor in "${editors[@]}"; do
        local process_name="${PROCESS_NAMES[$editor]:-$editor}"
        if pgrep -f "$process_name" > /dev/null 2>&1; then
            running_processes+=("$editor")
        fi
    done

    printf '%s\n' "${running_processes[@]}"
}

# Create enhanced backup
create_enhanced_backup() {
    local file_path="$1"
    local editor_name="$2"

    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_filename="${editor_name}_storage_${timestamp}.json"
    local backup_path="$BACKUP_DIR/$backup_filename"

    if cp "$file_path" "$backup_path"; then
        log_success "Created backup: $backup_path"

        # Optionally encrypt backup
        if [ "$ENCRYPT_BACKUPS" = true ]; then
            log_info "Backup encryption enabled (placeholder)"
            # Simple encryption implementation would go here
        fi

        echo "$backup_path"
        return 0
    else
        log_error "Failed to create backup"
        return 1
    fi
}

# Enhanced telemetry fields configuration
setup_telemetry_fields() {
    # Define all possible telemetry fields that can be modified
    declare -gA TELEMETRY_FIELDS

    # Core telemetry identifiers
    TELEMETRY_FIELDS["telemetry.machineId"]="hex64:Machine unique identifier:true"
    TELEMETRY_FIELDS["telemetry.devDeviceId"]="uuid4:Device identifier:true"
    TELEMETRY_FIELDS["telemetry.sessionId"]="uuid4:Session identifier:false"
    TELEMETRY_FIELDS["telemetry.sqmId"]="uuid4:Software Quality Metrics ID:false"

    # Installation and usage tracking
    TELEMETRY_FIELDS["telemetry.firstSessionDate"]="timestamp:First session date:false"
    TELEMETRY_FIELDS["telemetry.lastSessionDate"]="timestamp:Last session date:false"
    TELEMETRY_FIELDS["telemetry.isNewAppInstall"]="boolean:New app install flag:false"
    TELEMETRY_FIELDS["telemetry.optIn"]="boolean:Telemetry opt-in status:false"

    # Extended tracking identifiers
    TELEMETRY_FIELDS["telemetry.instanceId"]="uuid4:Application instance ID:false"
    TELEMETRY_FIELDS["telemetry.installationId"]="uuid4:Installation unique ID:false"
    TELEMETRY_FIELDS["telemetry.userId"]="uuid4:User identifier:false"
    TELEMETRY_FIELDS["telemetry.accountId"]="uuid4:Account identifier:false"

    # Hardware and system tracking
    TELEMETRY_FIELDS["telemetry.hardwareId"]="hex64:Hardware fingerprint:false"
    TELEMETRY_FIELDS["telemetry.systemId"]="hex64:System identifier:false"
    TELEMETRY_FIELDS["telemetry.platformId"]="uuid4:Platform identifier:false"
    TELEMETRY_FIELDS["telemetry.architectureId"]="hex32:Architecture identifier:false"

    # Network and location tracking
    TELEMETRY_FIELDS["telemetry.networkId"]="uuid4:Network identifier:false"
    TELEMETRY_FIELDS["telemetry.locationId"]="uuid4:Location identifier:false"
    TELEMETRY_FIELDS["telemetry.timezoneId"]="uuid4:Timezone identifier:false"
    TELEMETRY_FIELDS["telemetry.localeId"]="uuid4:Locale identifier:false"

    # Usage pattern tracking
    TELEMETRY_FIELDS["telemetry.usageId"]="uuid4:Usage pattern identifier:false"
    TELEMETRY_FIELDS["telemetry.behaviorId"]="uuid4:Behavior pattern identifier:false"
    TELEMETRY_FIELDS["telemetry.workflowId"]="uuid4:Workflow identifier:false"
    TELEMETRY_FIELDS["telemetry.projectId"]="uuid4:Project identifier:false"

    # Performance and diagnostics
    TELEMETRY_FIELDS["telemetry.performanceId"]="uuid4:Performance tracking ID:false"
    TELEMETRY_FIELDS["telemetry.diagnosticId"]="uuid4:Diagnostic identifier:false"
    TELEMETRY_FIELDS["telemetry.crashId"]="uuid4:Crash report identifier:false"
    TELEMETRY_FIELDS["telemetry.errorId"]="uuid4:Error tracking identifier:false"

    # Extension and plugin tracking
    TELEMETRY_FIELDS["telemetry.extensionId"]="uuid4:Extension usage identifier:false"
    TELEMETRY_FIELDS["telemetry.pluginId"]="uuid4:Plugin identifier:false"
    TELEMETRY_FIELDS["telemetry.themeId"]="uuid4:Theme usage identifier:false"
    TELEMETRY_FIELDS["telemetry.settingsId"]="uuid4:Settings identifier:false"

    # Analytics and metrics
    TELEMETRY_FIELDS["telemetry.analyticsId"]="uuid4:Analytics identifier:false"
    TELEMETRY_FIELDS["telemetry.metricsId"]="uuid4:Metrics collection ID:false"
    TELEMETRY_FIELDS["telemetry.experimentId"]="uuid4:A/B testing identifier:false"
    TELEMETRY_FIELDS["telemetry.cohortId"]="uuid4:User cohort identifier:false"

    # Privacy and consent tracking
    TELEMETRY_FIELDS["telemetry.consentId"]="uuid4:Consent tracking ID:false"
    TELEMETRY_FIELDS["telemetry.privacyId"]="uuid4:Privacy settings ID:false"
    TELEMETRY_FIELDS["telemetry.gdprId"]="uuid4:GDPR compliance ID:false"
    TELEMETRY_FIELDS["telemetry.ccpaId"]="uuid4:CCPA compliance ID:false"

    # Microsoft/GitHub specific
    TELEMETRY_FIELDS["telemetry.microsoftId"]="uuid4:Microsoft account ID:false"
    TELEMETRY_FIELDS["telemetry.githubId"]="uuid4:GitHub account ID:false"
    TELEMETRY_FIELDS["telemetry.azureId"]="uuid4:Azure identifier:false"
    TELEMETRY_FIELDS["telemetry.officeId"]="uuid4:Office integration ID:false"

    # Development and debugging
    TELEMETRY_FIELDS["telemetry.debugId"]="uuid4:Debug session identifier:false"
    TELEMETRY_FIELDS["telemetry.buildId"]="uuid4:Build identifier:false"
    TELEMETRY_FIELDS["telemetry.deployId"]="uuid4:Deployment identifier:false"
    TELEMETRY_FIELDS["telemetry.testId"]="uuid4:Testing identifier:false"

    # Additional hex32 type for shorter identifiers
    TELEMETRY_FIELDS["telemetry.shortId"]="hex32:Short identifier:false"
    TELEMETRY_FIELDS["telemetry.quickId"]="hex16:Quick identifier:false"
    TELEMETRY_FIELDS["telemetry.tempId"]="hex8:Temporary identifier:false"
}

# Generate hex IDs of different lengths
generate_hex_id() {
    local length="$1"
    local bytes=$((length / 2))
    hexdump -n "$bytes" -v -e '1/1 "%02x"' /dev/urandom
}

# Enhanced ID generation with more types
generate_id_by_type_enhanced() {
    local type="$1"

    case "$type" in
        "hex64")
            generate_enhanced_machine_id
            ;;
        "hex32")
            generate_hex_id 32
            ;;
        "hex16")
            generate_hex_id 16
            ;;
        "hex8")
            generate_hex_id 8
            ;;
        "uuid4")
            generate_enhanced_device_id
            ;;
        "timestamp")
            generate_random_timestamp
            ;;
        "boolean")
            generate_random_boolean
            ;;
        *)
            generate_enhanced_device_id
            ;;
    esac
}

# Parse telemetry field configuration
parse_field_config() {
    local field="$1"
    local config="${TELEMETRY_FIELDS[$field]}"

    if [ -z "$config" ]; then
        echo "uuid4:Unknown field:false"
        return
    fi

    echo "$config"
}

# Get field type from configuration
get_field_type() {
    local field="$1"
    local config=$(parse_field_config "$field")
    echo "$config" | cut -d':' -f1
}

# Get field description from configuration
get_field_description() {
    local field="$1"
    local config=$(parse_field_config "$field")
    echo "$config" | cut -d':' -f2
}

# Check if field is required
is_field_required() {
    local field="$1"
    local config=$(parse_field_config "$field")
    local required=$(echo "$config" | cut -d':' -f3)
    [ "$required" = "true" ]
}

# Modify telemetry fields in storage file
modify_telemetry_fields() {
    local storage_path="$1"
    local editor_name="$2"
    shift 2
    local fields_to_modify=("$@")

    log_info "Modifying telemetry fields for $editor_name..."

    # Read and validate JSON
    local content
    if ! content=$(cat "$storage_path"); then
        log_error "Failed to read storage file"
        return 1
    fi

    if ! echo "$content" | jq . > /dev/null 2>&1; then
        log_error "Storage file is not valid JSON"
        return 1
    fi

    # Determine which fields to modify
    local fields_to_process=()
    if [ ${#fields_to_modify[@]} -gt 0 ]; then
        fields_to_process=("${fields_to_modify[@]}")
    elif [ "$ENHANCED_MODE" = true ]; then
        # In enhanced mode, modify all available fields
        for field in "${!TELEMETRY_FIELDS[@]}"; do
            fields_to_process+=("$field")
        done
    else
        # Default mode - only basic fields
        fields_to_process=("telemetry.machineId" "telemetry.devDeviceId")
    fi

    # Build jq command to update all fields at once
    local jq_updates=()
    local modified_fields=()

    for field in "${fields_to_process[@]}"; do
        if [[ -n "${TELEMETRY_FIELDS[$field]:-}" ]]; then
            local field_type=$(get_field_type "$field")
            local new_value=$(generate_id_by_type_enhanced "$field_type")

            # Escape field name and value for jq
            local escaped_field=$(printf '%s' "$field" | sed 's/"/\\"/g')
            local escaped_value=$(printf '%s' "$new_value" | sed 's/"/\\"/g')

            jq_updates+=(".\"$escaped_field\" = \"$escaped_value\"")
            modified_fields+=("$field")

            log_success "Generated $field: $new_value"
        fi
    done

    if [ ${#jq_updates[@]} -eq 0 ]; then
        log_warning "No valid fields to modify"
        return 1
    fi

    # Apply all updates at once
    local jq_command=$(IFS=' | '; echo "${jq_updates[*]}")
    local updated_content

    if ! updated_content=$(echo "$content" | jq "$jq_command"); then
        log_error "Failed to update JSON content"
        return 1
    fi

    # Write updated content back to file
    if ! echo "$updated_content" > "$storage_path"; then
        log_error "Failed to write updated content to file"
        return 1
    fi

    log_success "Successfully modified ${#modified_fields[@]} telemetry fields"
    printf '%s\n' "${modified_fields[@]}"
}

# Process a single editor
process_editor() {
    local editor_key="$1"
    shift
    local fields_to_modify=("$@")

    local storage_path="${EDITOR_CONFIGS[$editor_key]}"

    log_info "Processing $editor_key..."

    # Check if storage file exists
    if [ ! -f "$storage_path" ]; then
        log_warning "$editor_key storage file not found: $storage_path"
        return 1
    fi

    # Create backup
    local backup_path
    if ! backup_path=$(create_enhanced_backup "$storage_path" "$editor_key"); then
        log_error "Skipping $editor_key due to backup failure"
        return 1
    fi

    # Modify telemetry fields
    local modified_fields
    if modified_fields=$(modify_telemetry_fields "$storage_path" "$editor_key" "${fields_to_modify[@]}"); then
        local field_count=$(echo "$modified_fields" | wc -l)
        log_success "Successfully processed $editor_key ($field_count fields modified)"
        return 0
    else
        log_error "Failed to process $editor_key"
        return 1
    fi
}

# List backup files
list_backups() {
    log_info "Listing backup files..."

    if [ ! -d "$BACKUP_DIR" ] || [ -z "$(ls -A "$BACKUP_DIR" 2>/dev/null)" ]; then
        log_warning "No backup files found"
        return
    fi

    echo ""
    echo -e "${GREEN}Backup Files:${RESET}"
    echo -e "${GREEN}=============${RESET}"

    # List files sorted by modification time (newest first)
    find "$BACKUP_DIR" -name "*.json" -type f -exec ls -la {} \; | sort -k6,7 -r | while read -r line; do
        local filename=$(echo "$line" | awk '{print $NF}')
        local size=$(echo "$line" | awk '{print $5}')
        local date=$(echo "$line" | awk '{print $6, $7, $8}')
        local basename=$(basename "$filename")

        echo -e "${CYAN}$basename${RESET} - $date - ${size} bytes"
    done

    echo ""
}

# Clean old backup files
clean_old_backups() {
    local days_old="$1"

    if [ "$days_old" -le 0 ]; then
        log_error "Invalid days parameter for backup cleanup"
        return 1
    fi

    log_info "Cleaning backups older than $days_old days..."

    local old_backups
    old_backups=$(find "$BACKUP_DIR" -name "*.json" -type f -mtime +"$days_old" 2>/dev/null)

    if [ -z "$old_backups" ]; then
        log_info "No old backups found to clean"
        return 0
    fi

    local count=0
    while IFS= read -r backup_file; do
        if rm "$backup_file"; then
            log_success "Removed old backup: $(basename "$backup_file")"
            ((count++))
        else
            log_error "Failed to remove backup: $(basename "$backup_file")"
        fi
    done <<< "$old_backups"

    log_success "Cleaned $count old backup files"
}

# Restore from backup
restore_from_backup() {
    local editor_key="$1"
    local backup_date="$2"

    log_info "Restoring $editor_key from backup..."

    # Find backup file
    local backup_pattern="${editor_key}_storage_"
    if [ -n "$backup_date" ]; then
        backup_pattern="${backup_pattern}${backup_date}*.json"
    else
        backup_pattern="${backup_pattern}*.json"
    fi

    local backup_files
    backup_files=$(find "$BACKUP_DIR" -name "$backup_pattern" -type f | sort -r)

    if [ -z "$backup_files" ]; then
        log_error "No backup files found for $editor_key"
        return 1
    fi

    local latest_backup=$(echo "$backup_files" | head -n1)
    local storage_path="${EDITOR_CONFIGS[$editor_key]}"

    if cp "$latest_backup" "$storage_path"; then
        log_success "Successfully restored from backup: $(basename "$latest_backup")"
        return 0
    else
        log_error "Failed to restore from backup"
        return 1
    fi
}

# Show detailed field information
show_field_info() {
    echo ""
    echo -e "${GREEN}Available Telemetry Fields:${RESET}"
    echo -e "${GREEN}===========================${RESET}"

    # Group fields by category
    declare -A field_categories
    field_categories["Core"]="telemetry.machineId telemetry.devDeviceId telemetry.sessionId telemetry.sqmId"
    field_categories["Installation"]="telemetry.firstSessionDate telemetry.lastSessionDate telemetry.isNewAppInstall telemetry.optIn"
    field_categories["Extended"]="telemetry.instanceId telemetry.installationId telemetry.userId telemetry.accountId"
    field_categories["Hardware"]="telemetry.hardwareId telemetry.systemId telemetry.platformId telemetry.architectureId"
    field_categories["Network"]="telemetry.networkId telemetry.locationId telemetry.timezoneId telemetry.localeId"
    field_categories["Usage"]="telemetry.usageId telemetry.behaviorId telemetry.workflowId telemetry.projectId"
    field_categories["Performance"]="telemetry.performanceId telemetry.diagnosticId telemetry.crashId telemetry.errorId"
    field_categories["Extensions"]="telemetry.extensionId telemetry.pluginId telemetry.themeId telemetry.settingsId"
    field_categories["Analytics"]="telemetry.analyticsId telemetry.metricsId telemetry.experimentId telemetry.cohortId"
    field_categories["Privacy"]="telemetry.consentId telemetry.privacyId telemetry.gdprId telemetry.ccpaId"
    field_categories["Microsoft"]="telemetry.microsoftId telemetry.githubId telemetry.azureId telemetry.officeId"
    field_categories["Development"]="telemetry.debugId telemetry.buildId telemetry.deployId telemetry.testId"
    field_categories["Short IDs"]="telemetry.shortId telemetry.quickId telemetry.tempId"

    for category in "${!field_categories[@]}"; do
        echo ""
        echo -e "${YELLOW}$category Fields:${RESET}"
        echo -e "${YELLOW}$(printf '%.0s-' $(seq 1 $((${#category} + 8))))${RESET}"

        for field in ${field_categories[$category]}; do
            if [[ -n "${TELEMETRY_FIELDS[$field]:-}" ]]; then
                local field_type=$(get_field_type "$field")
                local description=$(get_field_description "$field")
                local required_marker=""
                if is_field_required "$field"; then
                    required_marker=" ${RED}(Required)${RESET}"
                fi

                echo -e "  ${CYAN}$field${RESET} [$field_type] - $description$required_marker"
            fi
        done
    done

    echo ""
}

# Main execution function
main() {
    log_info "Starting $SCRIPT_NAME v$SCRIPT_VERSION"

    # Setup configurations
    setup_editor_configs
    setup_telemetry_fields

    # Handle special operations first
    if [ "$LIST_BACKUPS" = true ]; then
        list_backups
        return 0
    fi

    if [ "$CLEAN_BACKUPS" = true ] && [ "$OLDER_THAN" -gt 0 ]; then
        clean_old_backups "$OLDER_THAN"
        return 0
    fi

    if [ "$RESTORE_MODE" = true ]; then
        if [ -n "$EDITOR_LIST" ]; then
            IFS=',' read -ra editor_array <<< "$EDITOR_LIST"
            for editor in "${editor_array[@]}"; do
                editor=$(echo "$editor" | tr '[:upper:]' '[:lower:]' | xargs)
                restore_from_backup "$editor" "$BACKUP_DATE"
            done
        else
            log_error "Please specify editor(s) to restore with --editor parameter"
            return 1
        fi
        return 0
    fi

    # Check dependencies
    check_dependencies

    # Detect installed editors
    local installed_editors
    mapfile -t installed_editors < <(detect_installed_editors)

    if [ ${#installed_editors[@]} -eq 0 ]; then
        log_error "No supported editors found on this system"
        log_info "Supported editors: ${!EDITOR_CONFIGS[*]}"
        return 1
    fi

    # Determine which editors to process
    local editors_to_process=()
    if [ -n "$EDITOR_LIST" ]; then
        IFS=',' read -ra editor_array <<< "$EDITOR_LIST"
        for editor in "${editor_array[@]}"; do
            editor=$(echo "$editor" | tr '[:upper:]' '[:lower:]' | xargs)
            if [[ " ${installed_editors[*]} " =~ " $editor " ]]; then
                editors_to_process+=("$editor")
            else
                log_warning "Editor '$editor' not found or not installed"
            fi
        done
    elif [ "$ALL_EDITORS" = true ]; then
        editors_to_process=("${installed_editors[@]}")
    else
        # Default to vscode if available, otherwise first found editor
        if [[ " ${installed_editors[*]} " =~ " vscode " ]]; then
            editors_to_process=("vscode")
        else
            editors_to_process=("${installed_editors[0]}")
        fi
    fi

    if [ ${#editors_to_process[@]} -eq 0 ]; then
        log_error "No valid editors specified or found"
        return 1
    fi

    # Check for running processes
    local running_processes
    mapfile -t running_processes < <(check_editor_processes "${editors_to_process[@]}")

    if [ ${#running_processes[@]} -gt 0 ]; then
        log_warning "Warning: The following editors are currently running:"
        for process in "${running_processes[@]}"; do
            log_warning "  - $process"
        done
        log_warning "Please close these editors before proceeding"

        if [ "$SILENT_MODE" = false ]; then
            echo -n "Continue anyway? (y/N): "
            read -r response
            if [[ ! "$response" =~ ^[Yy]$ ]]; then
                log_info "Operation cancelled by user"
                return 0
            fi
        fi
    fi

    # Parse fields to modify
    local fields_to_modify=()
    if [ -n "$FIELDS_LIST" ]; then
        IFS=',' read -ra field_array <<< "$FIELDS_LIST"
        for field in "${field_array[@]}"; do
            field=$(echo "$field" | xargs)
            # Add telemetry prefix if not present
            if [[ ! "$field" =~ ^telemetry\. ]]; then
                field="telemetry.$field"
            fi
            fields_to_modify+=("$field")
        done
    fi

    # Show field information if in verbose mode
    if [ "$LOG_LEVEL" = "Verbose" ]; then
        show_field_info
    fi

    # Process each editor
    local success_count=0
    local total_count=${#editors_to_process[@]}

    log_info "Processing $total_count editor(s)..."

    for editor in "${editors_to_process[@]}"; do
        if process_editor "$editor" "${fields_to_modify[@]}"; then
            ((success_count++))
        fi
    done

    # Summary
    log_info "Processing complete: $success_count/$total_count editors successfully modified"

    if [ "$success_count" -gt 0 ]; then
        log_success "Telemetry IDs have been successfully modified"
        log_info "Please restart the affected editors for changes to take effect"

        # Show backup information
        if [ "$LOG_LEVEL" != "Minimal" ]; then
            log_info "Backup files created in: $BACKUP_DIR"
            log_info "Use --restore option to revert changes if needed"
        fi
    else
        log_error "No editors were successfully processed"
        return 1
    fi

    return 0
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    parse_arguments "$@"

    # Execute main function
    main
    exit $?
fi
