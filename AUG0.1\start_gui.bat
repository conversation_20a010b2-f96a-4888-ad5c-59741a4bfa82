@echo off
title AUG 0.1 GUI Launcher

echo Starting AUG 0.1 Enhanced Telemetry ID Modifier GUI...
echo.

python --version
if errorlevel 1 (
    echo Error: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

python -c "import psutil" 2>nul
if errorlevel 1 (
    echo Installing psutil...
    pip install psutil
)

echo.
echo Launching GUI application...
python gui_launcher_simple.py

if errorlevel 1 (
    echo.
    echo GUI application failed to start.
    echo Please check the error messages above.
    pause
)
