# install_enhanced.ps1
#
# AUG 0.1 Enhanced Installation Script
# Sets up the enhanced telemetry ID modifier environment

param(
    [switch]$Help,
    [switch]$SetupOnly,
    [switch]$RunAfterInstall,
    [string]$Mode = "Standard"
)

# Show help
if ($Help) {
    Write-Host "AUG 0.1 Enhanced Installation Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\install_enhanced.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -SetupOnly        Only setup directories and files"
    Write-Host "  -RunAfterInstall  Run the modifier after installation"
    Write-Host "  -Mode <mode>      Set operation mode (Quick/Standard/Enhanced)"
    Write-Host "  -Help             Show this help message"
    Write-Host ""
    exit 0
}

# Color functions
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

# Main installation function
function Install-EnhancedModifier {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Green
    Write-Host "║                AUG 0.1 Enhanced Installation                ║" -ForegroundColor Green
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Green
    Write-Host ""
    
    Write-Info "Starting AUG 0.1 Enhanced Telemetry ID Modifier installation..."
    
    # Check PowerShell version
    $psVersion = $PSVersionTable.PSVersion
    Write-Info "PowerShell version: $psVersion"
    
    if ($psVersion.Major -lt 5) {
        Write-Error "PowerShell 5.0 or higher is required"
        return $false
    }
    
    # Check execution policy
    $executionPolicy = Get-ExecutionPolicy
    Write-Info "Current execution policy: $executionPolicy"
    
    if ($executionPolicy -eq "Restricted") {
        Write-Warning "Execution policy is Restricted. Attempting to set RemoteSigned for CurrentUser..."
        try {
            Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
            Write-Success "Execution policy updated to RemoteSigned"
        }
        catch {
            Write-Error "Failed to update execution policy. Please run as administrator or manually set execution policy."
            return $false
        }
    }
    
    # Create directory structure
    Write-Info "Creating directory structure..."
    
    $directories = @(
        "scripts",
        "config", 
        "backups",
        "logs",
        "tools",
        "docs"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            try {
                New-Item -ItemType Directory -Path $dir -Force | Out-Null
                Write-Success "Created directory: $dir"
            }
            catch {
                Write-Error "Failed to create directory: $dir"
                return $false
            }
        } else {
            Write-Info "Directory already exists: $dir"
        }
    }
    
    # Verify script files exist
    Write-Info "Verifying script files..."
    
    $requiredFiles = @(
        "scripts\enhanced_id_modifier.ps1",
        "config\telemetry_config.json",
        "OneClick-Enhanced.bat"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-Error "Missing required files:"
        foreach ($file in $missingFiles) {
            Write-Error "  - $file"
        }
        return $false
    }
    
    Write-Success "All required files are present"
    
    # Set script permissions
    Write-Info "Setting script permissions..."
    
    try {
        $scriptFiles = Get-ChildItem -Path "scripts" -Filter "*.ps1"
        foreach ($script in $scriptFiles) {
            Unblock-File -Path $script.FullName -ErrorAction SilentlyContinue
        }
        Write-Success "Script permissions configured"
    }
    catch {
        Write-Warning "Could not set all script permissions. You may need to unblock files manually."
    }
    
    # Detect installed editors
    Write-Info "Detecting installed editors..."
    
    $editorConfigs = @{
        "VS Code" = "$env:APPDATA\Code\User\globalStorage\storage.json"
        "VS Code Insiders" = "$env:APPDATA\Code - Insiders\User\globalStorage\storage.json"
        "Cursor" = "$env:APPDATA\Cursor\User\globalStorage\storage.json"
        "VSCodium" = "$env:APPDATA\VSCodium\User\globalStorage\storage.json"
        "Code - OSS" = "$env:APPDATA\Code - OSS\User\globalStorage\storage.json"
    }
    
    $detectedEditors = @()
    foreach ($editor in $editorConfigs.Keys) {
        $path = $editorConfigs[$editor]
        if (Test-Path $path) {
            $detectedEditors += $editor
            Write-Success "Found: $editor"
        } else {
            Write-Info "Not found: $editor"
        }
    }
    
    if ($detectedEditors.Count -eq 0) {
        Write-Warning "No supported editors detected. Please install VS Code or compatible editor."
    } else {
        Write-Success "Detected $($detectedEditors.Count) supported editor(s)"
    }
    
    # Create initial log entry
    $logDir = "logs"
    $logFile = Join-Path $logDir "installation_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
    
    $logEntry = @"
AUG 0.1 Enhanced Installation Log
================================
Installation Date: $(Get-Date)
PowerShell Version: $psVersion
Execution Policy: $executionPolicy
Detected Editors: $($detectedEditors -join ', ')
Installation Status: Success
"@
    
    try {
        $logEntry | Out-File -FilePath $logFile -Encoding UTF8
        Write-Success "Installation log created: $logFile"
    }
    catch {
        Write-Warning "Could not create installation log"
    }
    
    # Installation complete
    Write-Host ""
    Write-Success "AUG 0.1 Enhanced installation completed successfully!"
    Write-Host ""
    
    # Show next steps
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                        Next Steps                           ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
    Write-Info "1. Close all code editors before running the modifier"
    Write-Info "2. Run 'OneClick-Enhanced.bat' for the interactive interface"
    Write-Info "3. Or use PowerShell: .\scripts\enhanced_id_modifier.ps1 -Help"
    Write-Info "4. Check the documentation in '使用指南_增强版.md'"
    Write-Host ""
    
    # Show detected editors summary
    if ($detectedEditors.Count -gt 0) {
        Write-Host "📊 Detected Editors:" -ForegroundColor Green
        foreach ($editor in $detectedEditors) {
            Write-Host "  ✓ $editor" -ForegroundColor Green
        }
        Write-Host ""
    }
    
    # Show available modes
    Write-Host "🎯 Available Operation Modes:" -ForegroundColor Yellow
    Write-Host "  • Quick Mode: Basic privacy protection (2-3 fields)" -ForegroundColor Yellow
    Write-Host "  • Standard Mode: Balanced protection (10-15 fields)" -ForegroundColor Yellow  
    Write-Host "  • Enhanced Mode: Maximum protection (50+ fields)" -ForegroundColor Yellow
    Write-Host "  • Custom Mode: User-defined selection" -ForegroundColor Yellow
    Write-Host ""
    
    return $true
}

# Run after installation if requested
function Invoke-PostInstallation {
    if ($RunAfterInstall) {
        Write-Info "Running post-installation modifier..."
        
        switch ($Mode.ToLower()) {
            "quick" {
                Write-Info "Running in Quick mode..."
                & ".\scripts\enhanced_id_modifier.ps1" -All -Fields "machineId,deviceId"
            }
            "standard" {
                Write-Info "Running in Standard mode..."
                & ".\scripts\enhanced_id_modifier.ps1" -All -Fields "machineId,deviceId,sessionId,instanceId"
            }
            "enhanced" {
                Write-Info "Running in Enhanced mode..."
                & ".\scripts\enhanced_id_modifier.ps1" -Enhanced -All
            }
            default {
                Write-Info "Running in Standard mode (default)..."
                & ".\scripts\enhanced_id_modifier.ps1" -All -Fields "machineId,deviceId,sessionId,instanceId"
            }
        }
    }
}

# Main execution
try {
    if (Install-EnhancedModifier) {
        if (-not $SetupOnly) {
            Invoke-PostInstallation
        }
        
        Write-Host "🎉 Installation completed successfully!" -ForegroundColor Green
        Write-Host "   Ready to enhance your privacy protection!" -ForegroundColor Green
        Write-Host ""
    } else {
        Write-Error "Installation failed. Please check the error messages above."
        exit 1
    }
}
catch {
    Write-Error "Installation failed with error: $($_.Exception.Message)"
    exit 1
}
