#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语言管理器模块
支持中英文双语界面切换
"""

import json
import os
from typing import Dict, Any

class LanguageManager:
    """语言管理器类"""
    
    def __init__(self):
        """初始化语言管理器"""
        self.current_language = "zh_CN"
        self.translations = {}
        self.load_translations()
        
    def load_translations(self):
        """加载翻译文件"""
        # 中文翻译
        self.translations["zh_CN"] = {
            # 主窗口
            "main_title": "AUG 0.1 增强版遥测ID修改器",
            "ready": "就绪",
            "running": "运行中",
            "completed": "完成",
            "error": "错误",
            
            # 菜单
            "menu_file": "文件",
            "menu_operation": "操作",
            "menu_tools": "工具",
            "menu_help": "帮助",
            "menu_import_config": "导入配置",
            "menu_export_config": "导出配置",
            "menu_exit": "退出",
            "menu_detect_editors": "检测编辑器",
            "menu_backup_manager": "备份管理",
            "menu_run_enhanced": "运行增强模式",
            "menu_run_standard": "运行标准模式",
            "menu_log_viewer": "日志查看器",
            "menu_config_editor": "配置编辑器",
            "menu_system_info": "系统信息",
            "menu_help_guide": "使用指南",
            "menu_about": "关于",
            
            # 标签页
            "tab_dashboard": "🏠 仪表板",
            "tab_editors": "📝 编辑器",
            "tab_fields": "🔧 字段",
            "tab_operations": "⚙️ 操作",
            "tab_logs": "📋 日志",
            
            # 仪表板
            "welcome_title": "欢迎使用 AUG 0.1",
            "welcome_description": """AUG 0.1 Enhanced Telemetry ID Modifier - 图形界面版本

这是一个专业的隐私保护工具，用于修改代码编辑器的遥测标识符，
保护您的开发隐私和系统信息不被过度收集。

支持的编辑器：VS Code、Cursor、VSCodium、Code-OSS 等
支持的字段：50+ 种不同类型的遥测标识符
操作模式：增强、标准、快速、自定义四种模式""",
            "quick_operations": "快速操作",
            "detect_editors": "🔍 检测编辑器",
            "quick_modify": "⚡ 快速修改",
            "enhanced_protection": "🛡️ 增强保护",
            "backup_management": "📁 备份管理",
            "view_logs": "📋 查看日志",
            "settings": "⚙️ 设置",
            
            # 编辑器相关
            "editors_detected": "检测到的编辑器",
            "editor_vscode": "Visual Studio Code",
            "editor_cursor": "Cursor Editor",
            "editor_vscodium": "VSCodium",
            "editor_vscode_insiders": "VS Code Insiders",
            "editor_code_oss": "Code - OSS",
            "editor_status_installed": "已安装",
            "editor_status_not_found": "未找到",
            "editor_status_running": "运行中",
            
            # 操作模式
            "mode_enhanced": "增强模式",
            "mode_standard": "标准模式",
            "mode_quick": "快速模式",
            "mode_custom": "自定义模式",
            "mode_enhanced_desc": "修改所有50+遥测字段，最大隐私保护",
            "mode_standard_desc": "修改10-15个常用字段，平衡保护",
            "mode_quick_desc": "只修改2-3个核心字段，快速操作",
            "mode_custom_desc": "用户完全自定义选择",
            
            # 字段类别
            "fields_core": "核心标识符",
            "fields_installation": "安装追踪",
            "fields_user": "用户追踪",
            "fields_hardware": "硬件系统",
            "fields_network": "网络位置",
            "fields_usage": "使用行为",
            "fields_performance": "性能诊断",
            "fields_ai": "AI和Copilot",
            "fields_microsoft": "Microsoft生态",
            "fields_privacy": "隐私合规",
            
            # 按钮和操作
            "button_start": "开始",
            "button_stop": "停止",
            "button_cancel": "取消",
            "button_ok": "确定",
            "button_apply": "应用",
            "button_reset": "重置",
            "button_browse": "浏览",
            "button_refresh": "刷新",
            "button_backup": "备份",
            "button_restore": "恢复",
            
            # 状态消息
            "status_detecting": "正在检测编辑器...",
            "status_modifying": "正在修改遥测字段...",
            "status_backing_up": "正在创建备份...",
            "status_completed": "操作完成",
            "status_failed": "操作失败",
            
            # 对话框
            "dialog_confirm": "确认",
            "dialog_warning": "警告",
            "dialog_error": "错误",
            "dialog_info": "信息",
            "confirm_exit": "确定要退出 AUG 0.1 吗？",
            "confirm_operation": "确定要执行此操作吗？",
            "warning_editors_running": "检测到编辑器正在运行，建议先关闭编辑器",
            
            # 其他
            "language": "语言",
            "version": "版本",
            "author": "作者",
            "license": "许可证",
            "backup_created": "备份已创建",
            "operation_successful": "操作成功",
            "no_editors_found": "未找到支持的编辑器",
        }
        
        # 英文翻译
        self.translations["en_US"] = {
            # Main window
            "main_title": "AUG 0.1 Enhanced Telemetry ID Modifier",
            "ready": "Ready",
            "running": "Running",
            "completed": "Completed",
            "error": "Error",
            
            # Menu
            "menu_file": "File",
            "menu_operation": "Operation",
            "menu_tools": "Tools",
            "menu_help": "Help",
            "menu_import_config": "Import Config",
            "menu_export_config": "Export Config",
            "menu_exit": "Exit",
            "menu_detect_editors": "Detect Editors",
            "menu_backup_manager": "Backup Manager",
            "menu_run_enhanced": "Run Enhanced Mode",
            "menu_run_standard": "Run Standard Mode",
            "menu_log_viewer": "Log Viewer",
            "menu_config_editor": "Config Editor",
            "menu_system_info": "System Info",
            "menu_help_guide": "Help Guide",
            "menu_about": "About",
            
            # Tabs
            "tab_dashboard": "🏠 Dashboard",
            "tab_editors": "📝 Editors",
            "tab_fields": "🔧 Fields",
            "tab_operations": "⚙️ Operations",
            "tab_logs": "📋 Logs",
            
            # Dashboard
            "welcome_title": "Welcome to AUG 0.1",
            "welcome_description": """AUG 0.1 Enhanced Telemetry ID Modifier - GUI Version

A professional privacy protection tool for modifying code editor telemetry identifiers,
protecting your development privacy and system information from excessive collection.

Supported Editors: VS Code, Cursor, VSCodium, Code-OSS, etc.
Supported Fields: 50+ different types of telemetry identifiers
Operation Modes: Enhanced, Standard, Quick, Custom modes""",
            "quick_operations": "Quick Operations",
            "detect_editors": "🔍 Detect Editors",
            "quick_modify": "⚡ Quick Modify",
            "enhanced_protection": "🛡️ Enhanced Protection",
            "backup_management": "📁 Backup Management",
            "view_logs": "📋 View Logs",
            "settings": "⚙️ Settings",
            
            # Editor related
            "editors_detected": "Detected Editors",
            "editor_vscode": "Visual Studio Code",
            "editor_cursor": "Cursor Editor",
            "editor_vscodium": "VSCodium",
            "editor_vscode_insiders": "VS Code Insiders",
            "editor_code_oss": "Code - OSS",
            "editor_status_installed": "Installed",
            "editor_status_not_found": "Not Found",
            "editor_status_running": "Running",
            
            # Operation modes
            "mode_enhanced": "Enhanced Mode",
            "mode_standard": "Standard Mode",
            "mode_quick": "Quick Mode",
            "mode_custom": "Custom Mode",
            "mode_enhanced_desc": "Modify all 50+ telemetry fields, maximum privacy protection",
            "mode_standard_desc": "Modify 10-15 common fields, balanced protection",
            "mode_quick_desc": "Modify only 2-3 core fields, quick operation",
            "mode_custom_desc": "Fully customizable user selection",
            
            # Field categories
            "fields_core": "Core Identifiers",
            "fields_installation": "Installation Tracking",
            "fields_user": "User Tracking",
            "fields_hardware": "Hardware System",
            "fields_network": "Network Location",
            "fields_usage": "Usage Behavior",
            "fields_performance": "Performance Diagnostics",
            "fields_ai": "AI and Copilot",
            "fields_microsoft": "Microsoft Ecosystem",
            "fields_privacy": "Privacy Compliance",
            
            # Buttons and operations
            "button_start": "Start",
            "button_stop": "Stop",
            "button_cancel": "Cancel",
            "button_ok": "OK",
            "button_apply": "Apply",
            "button_reset": "Reset",
            "button_browse": "Browse",
            "button_refresh": "Refresh",
            "button_backup": "Backup",
            "button_restore": "Restore",
            
            # Status messages
            "status_detecting": "Detecting editors...",
            "status_modifying": "Modifying telemetry fields...",
            "status_backing_up": "Creating backup...",
            "status_completed": "Operation completed",
            "status_failed": "Operation failed",
            
            # Dialogs
            "dialog_confirm": "Confirm",
            "dialog_warning": "Warning",
            "dialog_error": "Error",
            "dialog_info": "Information",
            "confirm_exit": "Are you sure you want to exit AUG 0.1?",
            "confirm_operation": "Are you sure you want to perform this operation?",
            "warning_editors_running": "Editors are running, recommend closing them first",
            
            # Others
            "language": "Language",
            "version": "Version",
            "author": "Author",
            "license": "License",
            "backup_created": "Backup created",
            "operation_successful": "Operation successful",
            "no_editors_found": "No supported editors found",
        }
        
    def set_language(self, language_code: str):
        """设置当前语言"""
        if language_code in self.translations:
            self.current_language = language_code
            
    def get_text(self, key: str, default: str = None) -> str:
        """获取翻译文本"""
        if self.current_language in self.translations:
            return self.translations[self.current_language].get(key, default or key)
        return default or key
        
    def get_current_language(self) -> str:
        """获取当前语言"""
        return self.current_language
        
    def get_available_languages(self) -> list:
        """获取可用语言列表"""
        return list(self.translations.keys())
