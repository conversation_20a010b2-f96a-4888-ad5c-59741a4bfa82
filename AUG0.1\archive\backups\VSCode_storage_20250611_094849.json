﻿{
    "telemetry.sqmId":  "{BE94E2B4-D7FA-4B04-ABF3-C92CEE367F88}",
    "backupWorkspaces":  {
                             "workspaces":  [

                                            ],
                             "folders":  [
                                             {
                                                 "folderUri":  "file:///d%3A/AIGC-dm/Cross-border%20E-commerce%20Website%20Project/%E6%B8%85%E7%90%86/augment-vip/AUG0.1"
                                             }
                                         ],
                             "emptyWindows":  [
                                                  {
                                                      "backupFolder":  "1749605881683"
                                                  }
                                              ]
                         },
    "windowControlHeight":  35,
    "profileAssociations":  {
                                "workspaces":  {
                                                   "file:///d%3A/AIGC-dm/Cross-border%20E-commerce%20Website%20Project/%E6%B8%85%E7%90%86/augment-vip/AUG0.1":  "__default__profile__"
                                               },
                                "emptyWindows":  {
                                                     "1749605881683":  "__default__profile__"
                                                 }
                            },
    "theme":  "vs-dark",
    "themeBackground":  "#1f1f1f",
    "windowSplash":  {
                         "zoomLevel":  0,
                         "baseTheme":  "vs-dark",
                         "colorInfo":  {
                                           "foreground":  "#cccccc",
                                           "background":  "#1f1f1f",
                                           "editorBackground":  "#1f1f1f",
                                           "titleBarBackground":  "#181818",
                                           "titleBarBorder":  "#2b2b2b",
                                           "activityBarBackground":  "#181818",
                                           "activityBarBorder":  "#2b2b2b",
                                           "sideBarBackground":  "#181818",
                                           "sideBarBorder":  "#2b2b2b",
                                           "statusBarBackground":  "#181818",
                                           "statusBarBorder":  "#2b2b2b",
                                           "statusBarNoFolderBackground":  "#1f1f1f"
                                       },
                         "layoutInfo":  {
                                            "sideBarSide":  "left",
                                            "editorPartMinWidth":  220,
                                            "titleBarHeight":  35,
                                            "activityBarWidth":  48,
                                            "sideBarWidth":  275,
                                            "auxiliarySideBarWidth":  579,
                                            "statusBarHeight":  22,
                                            "windowBorder":  false
                                        }
                     },
    "windowsState":  {
                         "lastActiveWindow":  {
                                                  "folder":  "file:///d%3A/AIGC-dm/Cross-border%20E-commerce%20Website%20Project/%E6%B8%85%E7%90%86/augment-vip/AUG0.1",
                                                  "backupPath":  "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\Backups\\76bf2bfa6278c017db4e0d71fddd9028",
                                                  "uiState":  {
                                                                  "mode":  0,
                                                                  "x":  254,
                                                                  "y":  134,
                                                                  "width":  1202,
                                                                  "height":  802
                                                              }
                                              },
                         "openedWindows":  [

                                           ]
                     },
    "windowSplashWorkspaceOverride":  {
                                          "layoutInfo":  {
                                                             "sideBarWidth":  275,
                                                             "auxiliaryBarWidth":  579,
                                                             "workspaces":  {
                                                                                "03988105a30cb1019599c338aefd4b3c":  {
                                                                                                                         "sideBarVisible":  true,
                                                                                                                         "auxiliaryBarVisible":  true
                                                                                                                     }
                                                                            }
                                                         }
                                      },
    "telemetry.machineId":  "0443d82c3bf8140074dc7e40b2e2a984ae6000ceb6f5b8cd1431d3fa67e4c50c",
    "telemetry.devDeviceId":  "d8741823-14d9-400c-80e5-c80f548e25c8"
}
