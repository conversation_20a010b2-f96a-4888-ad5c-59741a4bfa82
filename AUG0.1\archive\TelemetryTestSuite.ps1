# TelemetryTestSuite.ps1
# Comprehensive testing suite for AUG 0.1 Enhanced Telemetry ID Modifier
# Version: 1.0
# Author: AI Analysis System

param(
    [switch]$Verbose,
    [switch]$GenerateReport,
    [switch]$TestModification,
    [string]$LogLevel = "Normal"
)

# Initialize
$ErrorActionPreference = "Continue"
$TestResults = @()
$StartTime = Get-Date

Write-Host "AUG 0.1 Telemetry Testing Suite" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green
Write-Host "Started: $($StartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
Write-Host ""

# Test 1: Editor Detection and Analysis
function Test-EditorDetection {
    Write-Host "Test 1: Editor Detection and Telemetry Analysis" -ForegroundColor Yellow
    
    $result = @{
        TestName = "Editor Detection"
        Status = "PASS"
        Details = @()
        Errors = @()
        Data = @{}
    }
    
    $editors = @{
        'VSCode' = "$env:APPDATA\Code\User\globalStorage\storage.json"
        'VSCodeInsiders' = "$env:APPDATA\Code - Insiders\User\globalStorage\storage.json"
        'Cursor' = "$env:APPDATA\Cursor\User\globalStorage\storage.json"
        'VSCodium' = "$env:APPDATA\VSCodium\User\globalStorage\storage.json"
        'CodeOSS' = "$env:APPDATA\Code - OSS\User\globalStorage\storage.json"
    }
    
    $detectedEditors = 0
    $totalTelemetryFields = 0
    
    foreach ($editor in $editors.Keys) {
        $path = $editors[$editor]
        if (Test-Path $path) {
            $detectedEditors++
            $result.Details += "✓ $editor detected at: $path"
            
            try {
                $content = Get-Content $path -Raw | ConvertFrom-Json
                $telemetryFields = $content.PSObject.Properties | Where-Object { $_.Name -like "telemetry.*" }
                $fieldCount = $telemetryFields.Count
                $totalTelemetryFields += $fieldCount
                
                $result.Details += "  → Current telemetry fields: $fieldCount"
                $result.Data[$editor] = @{
                    Path = $path
                    FieldCount = $fieldCount
                    Fields = $telemetryFields.Name
                    FileSize = (Get-Item $path).Length
                }
                
                if ($Verbose) {
                    foreach ($field in $telemetryFields) {
                        $value = if ($field.Value.Length -gt 50) { 
                            $field.Value.Substring(0, 47) + "..." 
                        } else { 
                            $field.Value 
                        }
                        $result.Details += "    $($field.Name): $value"
                    }
                }
            }
            catch {
                $result.Errors += "Failed to parse JSON for $editor`: $($_.Exception.Message)"
                $result.Status = "WARNING"
            }
        } else {
            $result.Details += "✗ $editor not found"
        }
    }
    
    $result.Details += "Summary: $detectedEditors editors detected, $totalTelemetryFields total telemetry fields"
    
    if ($detectedEditors -eq 0) {
        $result.Status = "FAIL"
        $result.Errors += "No supported editors found on this system"
    }
    
    return $result
}

# Test 2: Configuration Validation
function Test-Configuration {
    Write-Host "Test 2: Configuration File Validation" -ForegroundColor Yellow
    
    $result = @{
        TestName = "Configuration Validation"
        Status = "PASS"
        Details = @()
        Errors = @()
        Data = @{}
    }
    
    $configFiles = @{
        'Main Config' = "config\telemetry_config.json"
        'Extended Fields' = "config\extended_telemetry_fields.json"
    }
    
    foreach ($configName in $configFiles.Keys) {
        $configPath = $configFiles[$configName]
        if (Test-Path $configPath) {
            $result.Details += "✓ $configName found: $configPath"
            
            try {
                $configContent = Get-Content $configPath -Raw | ConvertFrom-Json
                $result.Details += "  → Valid JSON format"
                
                # Analyze configuration content
                if ($configName -eq 'Main Config') {
                    $editorCount = $configContent.editors.PSObject.Properties.Count
                    $fieldCount = $configContent.telemetry_fields.PSObject.Properties.Count
                    $result.Details += "  → Configured editors: $editorCount"
                    $result.Details += "  → Configured fields: $fieldCount"
                    $result.Data['MainConfig'] = @{
                        EditorCount = $editorCount
                        FieldCount = $fieldCount
                    }
                }
                
                if ($configName -eq 'Extended Fields') {
                    $extendedFieldCount = 0
                    foreach ($category in $configContent.extended_fields.PSObject.Properties) {
                        $extendedFieldCount += $category.Value.PSObject.Properties.Count
                    }
                    $result.Details += "  → Extended fields: $extendedFieldCount"
                    $result.Data['ExtendedConfig'] = @{
                        ExtendedFieldCount = $extendedFieldCount
                    }
                }
            }
            catch {
                $result.Errors += "Invalid JSON in $configName`: $($_.Exception.Message)"
                $result.Status = "FAIL"
            }
        } else {
            $result.Errors += "$configName not found: $configPath"
            $result.Status = "WARNING"
        }
    }
    
    return $result
}

# Test 3: Backup System Validation
function Test-BackupSystem {
    Write-Host "Test 3: Backup System Validation" -ForegroundColor Yellow
    
    $result = @{
        TestName = "Backup System"
        Status = "PASS"
        Details = @()
        Errors = @()
        Data = @{}
    }
    
    $backupDir = ".\backups"
    if (Test-Path $backupDir) {
        $backups = Get-ChildItem $backupDir -Filter "*.json" | Sort-Object LastWriteTime -Descending
        $result.Details += "✓ Backup directory exists: $backupDir"
        $result.Details += "  → Total backups: $($backups.Count)"
        
        if ($backups.Count -gt 0) {
            $recentBackups = $backups | Select-Object -First 5
            $result.Details += "  → Recent backups:"
            foreach ($backup in $recentBackups) {
                $age = (Get-Date) - $backup.LastWriteTime
                $ageStr = if ($age.Days -gt 0) { "$($age.Days)d" } elseif ($age.Hours -gt 0) { "$($age.Hours)h" } else { "$($age.Minutes)m" }
                $result.Details += "    $($backup.Name) ($ageStr ago, $([math]::Round($backup.Length/1KB, 1))KB)"
            }
            
            # Test backup integrity
            $testBackup = $backups[0]
            try {
                $backupContent = Get-Content $testBackup.FullName -Raw | ConvertFrom-Json
                $result.Details += "  → Latest backup JSON is valid"
            }
            catch {
                $result.Errors += "Latest backup has invalid JSON: $($_.Exception.Message)"
                $result.Status = "WARNING"
            }
        }
        
        $result.Data['BackupSystem'] = @{
            BackupCount = $backups.Count
            BackupDir = $backupDir
            LatestBackup = if ($backups.Count -gt 0) { $backups[0].Name } else { $null }
        }
    } else {
        $result.Errors += "Backup directory not found: $backupDir"
        $result.Status = "FAIL"
    }
    
    return $result
}

# Test 4: Script Functionality
function Test-ScriptFunctionality {
    Write-Host "Test 4: Script Functionality Validation" -ForegroundColor Yellow
    
    $result = @{
        TestName = "Script Functionality"
        Status = "PASS"
        Details = @()
        Errors = @()
        Data = @{}
    }
    
    $scripts = @{
        'PowerShell Script' = "scripts\enhanced_id_modifier.ps1"
        'Bash Script' = "scripts\enhanced_id_modifier.sh"
        'Batch Interface' = "OneClick-Enhanced.bat"
    }
    
    foreach ($scriptName in $scripts.Keys) {
        $scriptPath = $scripts[$scriptName]
        if (Test-Path $scriptPath) {
            $result.Details += "✓ $scriptName found: $scriptPath"
            
            $fileInfo = Get-Item $scriptPath
            $result.Details += "  → Size: $([math]::Round($fileInfo.Length/1KB, 1))KB"
            $result.Details += "  → Modified: $($fileInfo.LastWriteTime.ToString('yyyy-MM-dd HH:mm'))"
            
            # Test PowerShell script syntax
            if ($scriptPath.EndsWith('.ps1')) {
                try {
                    $null = [System.Management.Automation.PSParser]::Tokenize((Get-Content $scriptPath -Raw), [ref]$null)
                    $result.Details += "  → PowerShell syntax is valid"
                }
                catch {
                    $result.Errors += "PowerShell syntax error in $scriptName`: $($_.Exception.Message)"
                    $result.Status = "WARNING"
                }
            }
        } else {
            $result.Errors += "$scriptName not found: $scriptPath"
            $result.Status = "WARNING"
        }
    }
    
    return $result
}

# Test 5: Process Detection
function Test-ProcessDetection {
    Write-Host "Test 5: Process Detection" -ForegroundColor Yellow
    
    $result = @{
        TestName = "Process Detection"
        Status = "PASS"
        Details = @()
        Errors = @()
        Data = @{}
    }
    
    $processes = @("Code", "Cursor", "VSCodium", "code-oss")
    $runningProcesses = @()
    
    foreach ($processName in $processes) {
        $running = Get-Process -Name $processName -ErrorAction SilentlyContinue
        if ($running) {
            $runningProcesses += $processName
            $result.Details += "⚠ $processName is running (PID: $($running.Id))"
        } else {
            $result.Details += "✓ $processName not running"
        }
    }
    
    $result.Data['RunningProcesses'] = $runningProcesses
    
    if ($runningProcesses.Count -gt 0) {
        $result.Details += "Warning: $($runningProcesses.Count) editor process(es) detected"
        $result.Details += "Recommendation: Close editors before running telemetry modifications"
        $result.Status = "WARNING"
    }
    
    return $result
}

# Test 6: Telemetry Field Modification (Optional)
function Test-TelemetryModification {
    Write-Host "Test 6: Telemetry Field Modification (Safe Test)" -ForegroundColor Yellow
    
    $result = @{
        TestName = "Telemetry Modification"
        Status = "PASS"
        Details = @()
        Errors = @()
        Data = @{}
    }
    
    # Create a test file to avoid modifying real editor data
    $testFile = "test_telemetry_storage.json"
    $testData = @{
        "telemetry.machineId" = "test-machine-id-12345"
        "telemetry.devDeviceId" = "test-device-id-67890"
        "telemetry.sessionId" = "test-session-id-abcdef"
        "otherData" = "should-remain-unchanged"
        "settings" = @{
            "theme" = "dark"
            "fontSize" = 14
        }
    }
    
    try {
        # Create test file
        $testData | ConvertTo-Json -Depth 3 | Set-Content $testFile -Encoding UTF8
        $result.Details += "✓ Created test storage file"
        
        # Simulate field modification
        $content = Get-Content $testFile -Raw | ConvertFrom-Json
        $originalMachineId = $content."telemetry.machineId"
        $originalDeviceId = $content."telemetry.devDeviceId"
        
        # Generate new IDs (simulating the script's behavior)
        $newMachineId = -join ((1..64) | ForEach-Object { '{0:x}' -f (Get-Random -Maximum 16) })
        $newDeviceId = [System.Guid]::NewGuid().ToString().ToLower()
        
        $content."telemetry.machineId" = $newMachineId
        $content."telemetry.devDeviceId" = $newDeviceId
        
        $content | ConvertTo-Json -Depth 3 | Set-Content $testFile -Encoding UTF8
        $result.Details += "✓ Modified telemetry fields"
        
        # Verify modification
        $modifiedContent = Get-Content $testFile -Raw | ConvertFrom-Json
        
        if ($modifiedContent."telemetry.machineId" -eq $newMachineId -and 
            $modifiedContent."telemetry.devDeviceId" -eq $newDeviceId -and
            $modifiedContent.otherData -eq "should-remain-unchanged" -and
            $modifiedContent.settings.theme -eq "dark") {
            
            $result.Details += "✓ Field modification successful"
            $result.Details += "  → Machine ID changed: $($originalMachineId -ne $newMachineId)"
            $result.Details += "  → Device ID changed: $($originalDeviceId -ne $newDeviceId)"
            $result.Details += "  → Other data preserved: ✓"
            
            $result.Data['ModificationTest'] = @{
                Success = $true
                OriginalMachineId = $originalMachineId
                NewMachineId = $newMachineId
                OriginalDeviceId = $originalDeviceId
                NewDeviceId = $newDeviceId
            }
        } else {
            $result.Status = "FAIL"
            $result.Errors += "Field modification verification failed"
        }
    }
    catch {
        $result.Status = "FAIL"
        $result.Errors += "Telemetry modification test failed: $($_.Exception.Message)"
    }
    finally {
        # Clean up test file
        if (Test-Path $testFile) {
            Remove-Item $testFile -ErrorAction SilentlyContinue
        }
    }
    
    return $result
}

# Run all tests
Write-Host "Running comprehensive test suite..." -ForegroundColor Cyan
Write-Host ""

$TestResults += Test-EditorDetection
$TestResults += Test-Configuration
$TestResults += Test-BackupSystem
$TestResults += Test-ScriptFunctionality
$TestResults += Test-ProcessDetection

if ($TestModification) {
    $TestResults += Test-TelemetryModification
}

# Display results
Write-Host ""
Write-Host "Test Results Summary" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green

$passCount = 0
$warningCount = 0
$failCount = 0

foreach ($test in $TestResults) {
    $color = switch ($test.Status) {
        "PASS" { "Green"; $passCount++ }
        "WARNING" { "Yellow"; $warningCount++ }
        "FAIL" { "Red"; $failCount++ }
    }
    
    Write-Host "$($test.TestName): $($test.Status)" -ForegroundColor $color
    
    if ($Verbose -or $test.Status -ne "PASS") {
        foreach ($detail in $test.Details) {
            Write-Host "  $detail" -ForegroundColor Cyan
        }
        foreach ($error in $test.Errors) {
            Write-Host "  ! $error" -ForegroundColor Red
        }
    }
    Write-Host ""
}

$totalTests = $TestResults.Count
$endTime = Get-Date
$duration = $endTime - $StartTime

Write-Host "Final Summary:" -ForegroundColor Green
Write-Host "  Total Tests: $totalTests" -ForegroundColor Cyan
Write-Host "  Passed: $passCount" -ForegroundColor Green
Write-Host "  Warnings: $warningCount" -ForegroundColor Yellow
Write-Host "  Failed: $failCount" -ForegroundColor Red
Write-Host "  Duration: $($duration.TotalSeconds.ToString('F1')) seconds" -ForegroundColor Cyan

if ($GenerateReport) {
    $reportData = @{
        TestSuite = "AUG 0.1 Telemetry Testing Suite"
        Version = "1.0"
        Timestamp = $StartTime.ToString('yyyy-MM-ddTHH:mm:ssZ')
        Duration = $duration.TotalSeconds
        Summary = @{
            Total = $totalTests
            Passed = $passCount
            Warnings = $warningCount
            Failed = $failCount
        }
        Results = $TestResults
        SystemInfo = @{
            OS = [System.Environment]::OSVersion.ToString()
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            MachineName = $env:COMPUTERNAME
            UserName = $env:USERNAME
        }
    }
    
    $reportPath = "TelemetryTestReport_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    $reportData | ConvertTo-Json -Depth 5 | Set-Content $reportPath -Encoding UTF8
    Write-Host ""
    Write-Host "Detailed report saved to: $reportPath" -ForegroundColor Green
}

# Exit with appropriate code
if ($failCount -gt 0) {
    exit 1
} elseif ($warningCount -gt 0) {
    exit 2
} else {
    exit 0
}
