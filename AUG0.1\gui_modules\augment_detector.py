#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment扩展检测器模块
专门检测和清理Augment相关的扩展和数据
"""

import os
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import glob

class AugmentDetector:
    """Augment扩展检测器类"""
    
    def __init__(self):
        """初始化Augment检测器"""
        self.backup_dir = Path("backups/augment_extensions")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Augment相关的扩展标识符
        self.augment_extensions = {
            "Augment.vscode-augment": {
                "name": "Augment",
                "publisher": "Augment",
                "description": "Augment yourself with the best AI pair programmer",
                "company": "Augment Computing"
            },
            "augment.augment": {
                "name": "Augment AI",
                "publisher": "augment",
                "description": "AI-powered coding assistant"
            },
            "augment-computing.augment": {
                "name": "Augment Computing",
                "publisher": "augment-computing",
                "description": "Augment Computing AI assistant"
            }
        }
        
        # 编辑器配置
        self.editor_configs = {
            "vscode": {
                "name": "Visual Studio Code",
                "extensions_path": "%USERPROFILE%\\.vscode\\extensions",
                "settings_path": "%APPDATA%\\Code\\User\\settings.json",
                "storage_path": "%APPDATA%\\Code\\User\\globalStorage",
                "workspace_storage": "%APPDATA%\\Code\\User\\workspaceStorage"
            },
            "cursor": {
                "name": "Cursor Editor", 
                "extensions_path": "%USERPROFILE%\\.cursor\\extensions",
                "settings_path": "%APPDATA%\\Cursor\\User\\settings.json",
                "storage_path": "%APPDATA%\\Cursor\\User\\globalStorage",
                "workspace_storage": "%APPDATA%\\Cursor\\User\\workspaceStorage"
            },
            "vscodium": {
                "name": "VSCodium",
                "extensions_path": "%USERPROFILE%\\.vscodium\\extensions", 
                "settings_path": "%APPDATA%\\VSCodium\\User\\settings.json",
                "storage_path": "%APPDATA%\\VSCodium\\User\\globalStorage",
                "workspace_storage": "%APPDATA%\\VSCodium\\User\\workspaceStorage"
            },
            "vscode_insiders": {
                "name": "VS Code Insiders",
                "extensions_path": "%USERPROFILE%\\.vscode-insiders\\extensions",
                "settings_path": "%APPDATA%\\Code - Insiders\\User\\settings.json", 
                "storage_path": "%APPDATA%\\Code - Insiders\\User\\globalStorage",
                "workspace_storage": "%APPDATA%\\Code - Insiders\\User\\workspaceStorage"
            },
            "code_oss": {
                "name": "Code - OSS",
                "extensions_path": "%USERPROFILE%\\.vscode-oss\\extensions",
                "settings_path": "%APPDATA%\\Code - OSS\\User\\settings.json",
                "storage_path": "%APPDATA%\\Code - OSS\\User\\globalStorage", 
                "workspace_storage": "%APPDATA%\\Code - OSS\\User\\workspaceStorage"
            }
        }
        
    def scan_augment_extensions(self) -> Dict:
        """扫描所有Augment扩展"""
        results = {
            "total_found": 0,
            "editors": {},
            "extensions": [],
            "storage_data": [],
            "settings_references": []
        }
        
        for editor_id, config in self.editor_configs.items():
            editor_result = self._scan_editor_augment(editor_id, config)
            if editor_result["found_extensions"] or editor_result["found_storage"] or editor_result["found_settings"]:
                results["editors"][editor_id] = editor_result
                results["total_found"] += len(editor_result["found_extensions"])
                results["extensions"].extend(editor_result["found_extensions"])
                results["storage_data"].extend(editor_result["found_storage"])
                results["settings_references"].extend(editor_result["found_settings"])
                
        return results
        
    def _scan_editor_augment(self, editor_id: str, config: Dict) -> Dict:
        """扫描单个编辑器的Augment扩展"""
        result = {
            "editor_name": config["name"],
            "found_extensions": [],
            "found_storage": [],
            "found_settings": [],
            "total_size": 0
        }
        
        # 扫描扩展目录
        extensions_path = os.path.expandvars(config["extensions_path"])
        if os.path.exists(extensions_path):
            result["found_extensions"] = self._scan_extensions_directory(extensions_path)
            
        # 扫描存储数据
        storage_path = os.path.expandvars(config["storage_path"])
        if os.path.exists(storage_path):
            result["found_storage"] = self._scan_storage_directory(storage_path, editor_id)
            
        # 扫描设置文件
        settings_path = os.path.expandvars(config["settings_path"])
        if os.path.exists(settings_path):
            result["found_settings"] = self._scan_settings_file(settings_path, editor_id)
            
        # 扫描工作区存储
        workspace_storage = os.path.expandvars(config["workspace_storage"])
        if os.path.exists(workspace_storage):
            workspace_data = self._scan_workspace_storage(workspace_storage, editor_id)
            result["found_storage"].extend(workspace_data)
            
        # 计算总大小
        for ext in result["found_extensions"]:
            result["total_size"] += ext.get("size", 0)
            
        return result
        
    def _scan_extensions_directory(self, extensions_path: str) -> List[Dict]:
        """扫描扩展目录"""
        found_extensions = []
        
        try:
            for item in os.listdir(extensions_path):
                item_path = os.path.join(extensions_path, item)
                
                if os.path.isdir(item_path):
                    # 检查是否为Augment扩展
                    if self._is_augment_extension(item, item_path):
                        ext_info = self._analyze_extension(item, item_path)
                        if ext_info:
                            found_extensions.append(ext_info)
                            
        except Exception as e:
            print(f"扫描扩展目录失败 {extensions_path}: {e}")
            
        return found_extensions
        
    def _is_augment_extension(self, folder_name: str, folder_path: str) -> bool:
        """判断是否为Augment扩展"""
        folder_name_lower = folder_name.lower()
        
        # 检查文件夹名称
        augment_keywords = ["augment", "augment.vscode-augment", "augment-computing"]
        
        for keyword in augment_keywords:
            if keyword in folder_name_lower:
                return True
                
        # 检查package.json文件
        package_json = os.path.join(folder_path, "package.json")
        if os.path.exists(package_json):
            try:
                with open(package_json, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 检查扩展ID、发布者、名称
                publisher = data.get("publisher", "").lower()
                name = data.get("name", "").lower()
                display_name = data.get("displayName", "").lower()
                description = data.get("description", "").lower()
                
                if ("augment" in publisher or "augment" in name or 
                    "augment" in display_name or "augment" in description):
                    return True
                    
            except Exception:
                pass
                
        return False
        
    def _analyze_extension(self, folder_name: str, folder_path: str) -> Optional[Dict]:
        """分析扩展详细信息"""
        try:
            package_json = os.path.join(folder_path, "package.json")
            
            ext_info = {
                "folder_name": folder_name,
                "folder_path": folder_path,
                "size": self._get_directory_size(folder_path),
                "name": "Unknown",
                "publisher": "Unknown",
                "version": "Unknown",
                "description": "",
                "extension_id": "",
                "install_date": self._get_install_date(folder_path)
            }
            
            if os.path.exists(package_json):
                with open(package_json, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                ext_info.update({
                    "name": data.get("displayName", data.get("name", "Unknown")),
                    "publisher": data.get("publisher", "Unknown"),
                    "version": data.get("version", "Unknown"),
                    "description": data.get("description", ""),
                    "extension_id": f"{data.get('publisher', '')}.{data.get('name', '')}"
                })
                
            return ext_info
            
        except Exception as e:
            print(f"分析扩展失败 {folder_path}: {e}")
            return None
            
    def _scan_storage_directory(self, storage_path: str, editor_id: str) -> List[Dict]:
        """扫描存储目录中的Augment数据"""
        found_storage = []
        
        try:
            for item in os.listdir(storage_path):
                item_path = os.path.join(storage_path, item)
                
                if "augment" in item.lower():
                    storage_info = {
                        "type": "global_storage",
                        "editor": editor_id,
                        "name": item,
                        "path": item_path,
                        "size": self._get_directory_size(item_path) if os.path.isdir(item_path) else os.path.getsize(item_path),
                        "last_modified": datetime.fromtimestamp(os.path.getmtime(item_path))
                    }
                    found_storage.append(storage_info)
                    
        except Exception as e:
            print(f"扫描存储目录失败 {storage_path}: {e}")
            
        return found_storage
        
    def _scan_workspace_storage(self, workspace_path: str, editor_id: str) -> List[Dict]:
        """扫描工作区存储中的Augment数据"""
        found_storage = []
        
        try:
            for workspace_dir in os.listdir(workspace_path):
                workspace_dir_path = os.path.join(workspace_path, workspace_dir)
                
                if os.path.isdir(workspace_dir_path):
                    # 检查工作区存储中的Augment数据
                    for item in os.listdir(workspace_dir_path):
                        if "augment" in item.lower():
                            item_path = os.path.join(workspace_dir_path, item)
                            storage_info = {
                                "type": "workspace_storage",
                                "editor": editor_id,
                                "workspace": workspace_dir,
                                "name": item,
                                "path": item_path,
                                "size": self._get_directory_size(item_path) if os.path.isdir(item_path) else os.path.getsize(item_path),
                                "last_modified": datetime.fromtimestamp(os.path.getmtime(item_path))
                            }
                            found_storage.append(storage_info)
                            
        except Exception as e:
            print(f"扫描工作区存储失败 {workspace_path}: {e}")
            
        return found_storage
        
    def _scan_settings_file(self, settings_path: str, editor_id: str) -> List[Dict]:
        """扫描设置文件中的Augment引用"""
        found_settings = []
        
        try:
            if os.path.exists(settings_path):
                with open(settings_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if "augment" in content.lower():
                    # 解析JSON查找具体的Augment设置
                    try:
                        data = json.loads(content)
                        augment_settings = self._find_augment_settings(data)
                        
                        if augment_settings:
                            settings_info = {
                                "type": "settings",
                                "editor": editor_id,
                                "path": settings_path,
                                "augment_settings": augment_settings,
                                "size": os.path.getsize(settings_path),
                                "last_modified": datetime.fromtimestamp(os.path.getmtime(settings_path))
                            }
                            found_settings.append(settings_info)
                            
                    except json.JSONDecodeError:
                        # 如果JSON解析失败，仍然记录文件包含augment
                        settings_info = {
                            "type": "settings",
                            "editor": editor_id,
                            "path": settings_path,
                            "augment_settings": ["文件包含augment关键字但无法解析JSON"],
                            "size": os.path.getsize(settings_path),
                            "last_modified": datetime.fromtimestamp(os.path.getmtime(settings_path))
                        }
                        found_settings.append(settings_info)
                        
        except Exception as e:
            print(f"扫描设置文件失败 {settings_path}: {e}")
            
        return found_settings
        
    def _find_augment_settings(self, data: Dict, prefix: str = "") -> List[str]:
        """递归查找包含augment的设置"""
        augment_settings = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                current_key = f"{prefix}.{key}" if prefix else key
                
                if "augment" in key.lower():
                    augment_settings.append(f"{current_key}: {value}")
                elif isinstance(value, (dict, list)):
                    augment_settings.extend(self._find_augment_settings(value, current_key))
                elif isinstance(value, str) and "augment" in value.lower():
                    augment_settings.append(f"{current_key}: {value}")
                    
        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_key = f"{prefix}[{i}]" if prefix else f"[{i}]"
                if isinstance(item, (dict, list)):
                    augment_settings.extend(self._find_augment_settings(item, current_key))
                elif isinstance(item, str) and "augment" in item.lower():
                    augment_settings.append(f"{current_key}: {item}")
                    
        return augment_settings
        
    def _get_directory_size(self, directory: str) -> int:
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except:
                        pass
        except:
            pass
        return total_size
        
    def _get_install_date(self, folder_path: str) -> datetime:
        """获取扩展安装日期"""
        try:
            return datetime.fromtimestamp(os.path.getctime(folder_path))
        except:
            return datetime.min
            
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"

    def backup_augment_data(self, scan_results: Dict) -> Dict:
        """备份Augment数据"""
        backup_result = {
            "success": False,
            "backup_paths": [],
            "errors": []
        }

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            # 备份扩展
            for extension in scan_results.get("extensions", []):
                try:
                    ext_name = extension.get("folder_name", "unknown")
                    backup_name = f"extension_{ext_name}_{timestamp}"
                    backup_path = self.backup_dir / backup_name

                    shutil.copytree(extension["folder_path"], backup_path)
                    backup_result["backup_paths"].append(str(backup_path))

                except Exception as e:
                    backup_result["errors"].append(f"备份扩展失败 {extension.get('name', 'Unknown')}: {e}")

            # 备份存储数据
            for storage in scan_results.get("storage_data", []):
                try:
                    storage_name = storage.get("name", "unknown")
                    backup_name = f"storage_{storage_name}_{timestamp}"
                    backup_path = self.backup_dir / backup_name

                    if os.path.isdir(storage["path"]):
                        shutil.copytree(storage["path"], backup_path)
                    else:
                        shutil.copy2(storage["path"], backup_path)

                    backup_result["backup_paths"].append(str(backup_path))

                except Exception as e:
                    backup_result["errors"].append(f"备份存储失败 {storage.get('name', 'Unknown')}: {e}")

            # 备份设置文件
            for settings in scan_results.get("settings_references", []):
                try:
                    editor = settings.get("editor", "unknown")
                    backup_name = f"settings_{editor}_{timestamp}.json"
                    backup_path = self.backup_dir / backup_name

                    shutil.copy2(settings["path"], backup_path)
                    backup_result["backup_paths"].append(str(backup_path))

                except Exception as e:
                    backup_result["errors"].append(f"备份设置失败 {settings.get('editor', 'Unknown')}: {e}")

            backup_result["success"] = len(backup_result["backup_paths"]) > 0

        except Exception as e:
            backup_result["errors"].append(f"备份过程失败: {e}")

        return backup_result

    def remove_augment_extensions(self, scan_results: Dict, backup_first: bool = True) -> Dict:
        """移除Augment扩展"""
        removal_result = {
            "success": False,
            "removed_items": [],
            "errors": [],
            "backup_info": None
        }

        try:
            # 先备份
            if backup_first:
                backup_result = self.backup_augment_data(scan_results)
                removal_result["backup_info"] = backup_result

                if not backup_result["success"] and backup_result["errors"]:
                    removal_result["errors"].append("备份失败，取消删除操作")
                    return removal_result

            # 删除扩展
            for extension in scan_results.get("extensions", []):
                try:
                    ext_path = extension["folder_path"]
                    if os.path.exists(ext_path):
                        shutil.rmtree(ext_path)
                        removal_result["removed_items"].append({
                            "type": "extension",
                            "name": extension.get("name", "Unknown"),
                            "path": ext_path
                        })

                except Exception as e:
                    removal_result["errors"].append(f"删除扩展失败 {extension.get('name', 'Unknown')}: {e}")

            # 删除存储数据
            for storage in scan_results.get("storage_data", []):
                try:
                    storage_path = storage["path"]
                    if os.path.exists(storage_path):
                        if os.path.isdir(storage_path):
                            shutil.rmtree(storage_path)
                        else:
                            os.remove(storage_path)

                        removal_result["removed_items"].append({
                            "type": "storage",
                            "name": storage.get("name", "Unknown"),
                            "path": storage_path
                        })

                except Exception as e:
                    removal_result["errors"].append(f"删除存储失败 {storage.get('name', 'Unknown')}: {e}")

            # 清理设置文件中的Augment引用
            for settings in scan_results.get("settings_references", []):
                try:
                    self._clean_settings_file(settings["path"])
                    removal_result["removed_items"].append({
                        "type": "settings",
                        "name": f"设置文件 ({settings.get('editor', 'Unknown')})",
                        "path": settings["path"]
                    })

                except Exception as e:
                    removal_result["errors"].append(f"清理设置失败 {settings.get('editor', 'Unknown')}: {e}")

            removal_result["success"] = len(removal_result["removed_items"]) > 0

        except Exception as e:
            removal_result["errors"].append(f"删除过程失败: {e}")

        return removal_result

    def _clean_settings_file(self, settings_path: str):
        """清理设置文件中的Augment引用"""
        try:
            with open(settings_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 尝试解析JSON
            try:
                data = json.loads(content)
                cleaned_data = self._remove_augment_settings(data)

                # 写回文件
                with open(settings_path, 'w', encoding='utf-8') as f:
                    json.dump(cleaned_data, f, indent=2, ensure_ascii=False)

            except json.JSONDecodeError:
                # 如果不是有效JSON，使用文本替换
                cleaned_content = self._remove_augment_text(content)

                with open(settings_path, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)

        except Exception as e:
            raise Exception(f"清理设置文件失败: {e}")

    def _remove_augment_settings(self, data):
        """递归移除包含augment的设置"""
        if isinstance(data, dict):
            cleaned_data = {}
            for key, value in data.items():
                if "augment" not in key.lower():
                    if isinstance(value, (dict, list)):
                        cleaned_value = self._remove_augment_settings(value)
                        if cleaned_value:  # 只保留非空的值
                            cleaned_data[key] = cleaned_value
                    elif not (isinstance(value, str) and "augment" in value.lower()):
                        cleaned_data[key] = value
            return cleaned_data

        elif isinstance(data, list):
            cleaned_list = []
            for item in data:
                if isinstance(item, (dict, list)):
                    cleaned_item = self._remove_augment_settings(item)
                    if cleaned_item:
                        cleaned_list.append(cleaned_item)
                elif not (isinstance(item, str) and "augment" in item.lower()):
                    cleaned_list.append(item)
            return cleaned_list

        return data

    def _remove_augment_text(self, content: str) -> str:
        """从文本中移除包含augment的行"""
        lines = content.split('\n')
        cleaned_lines = []

        for line in lines:
            if "augment" not in line.lower():
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)
