#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment检测测试脚本
测试Augment扩展检测和清理功能
"""

import os
import sys
import json
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_augment_extension():
    """创建测试Augment扩展"""
    print("🔧 创建测试Augment扩展...")
    
    test_ext_dir = Path("test_extensions")
    test_ext_dir.mkdir(exist_ok=True)
    
    # 创建Augment扩展目录
    augment_ext_dir = test_ext_dir / "augment.vscode-augment-0.477.2"
    augment_ext_dir.mkdir(exist_ok=True)
    
    # 创建package.json
    package_json = {
        "name": "vscode-augment",
        "displayName": "Augment",
        "description": "Augment yourself with the best AI pair programmer",
        "version": "0.477.2",
        "publisher": "Augment",
        "engines": {
            "vscode": "^1.74.0"
        },
        "categories": ["Other"],
        "activationEvents": ["*"],
        "main": "./out/extension.js",
        "contributes": {
            "commands": [
                {
                    "command": "augment.enable",
                    "title": "Enable Augment"
                }
            ]
        }
    }
    
    with open(augment_ext_dir / "package.json", 'w', encoding='utf-8') as f:
        json.dump(package_json, f, indent=2, ensure_ascii=False)
    
    # 创建一些测试文件
    (augment_ext_dir / "README.md").write_text("# Augment Extension\nAI pair programming assistant")
    (augment_ext_dir / "CHANGELOG.md").write_text("# Change Log\n## 0.477.2\n- Bug fixes")
    
    # 创建out目录和文件
    out_dir = augment_ext_dir / "out"
    out_dir.mkdir(exist_ok=True)
    (out_dir / "extension.js").write_text("// Augment extension main file\nconsole.log('Augment loaded');")
    
    print(f"✅ 创建测试扩展: {augment_ext_dir}")
    return str(augment_ext_dir)

def create_test_augment_storage():
    """创建测试Augment存储数据"""
    print("🔧 创建测试Augment存储数据...")
    
    test_storage_dir = Path("test_storage")
    test_storage_dir.mkdir(exist_ok=True)
    
    # 创建全局存储
    global_storage_dir = test_storage_dir / "globalStorage"
    global_storage_dir.mkdir(exist_ok=True)
    
    # 创建Augment存储目录
    augment_storage_dir = global_storage_dir / "augment.vscode-augment"
    augment_storage_dir.mkdir(exist_ok=True)
    
    # 创建存储文件
    storage_data = {
        "user_id": "test_user_123",
        "api_key": "test_api_key",
        "settings": {
            "auto_complete": True,
            "suggestions_enabled": True
        },
        "usage_stats": {
            "completions_accepted": 150,
            "suggestions_shown": 500
        }
    }
    
    with open(augment_storage_dir / "state.json", 'w', encoding='utf-8') as f:
        json.dump(storage_data, f, indent=2, ensure_ascii=False)
    
    # 创建工作区存储
    workspace_storage_dir = test_storage_dir / "workspaceStorage"
    workspace_storage_dir.mkdir(exist_ok=True)
    
    workspace_dir = workspace_storage_dir / "1234567890abcdef"
    workspace_dir.mkdir(exist_ok=True)
    
    augment_workspace_dir = workspace_dir / "augment.vscode-augment"
    augment_workspace_dir.mkdir(exist_ok=True)
    
    workspace_data = {
        "project_context": "test_project",
        "augment_enabled": True,
        "last_used": "2024-06-11T15:00:00Z"
    }
    
    with open(augment_workspace_dir / "workspace.json", 'w', encoding='utf-8') as f:
        json.dump(workspace_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 创建测试存储: {test_storage_dir}")
    return str(test_storage_dir)

def create_test_settings_file():
    """创建包含Augment设置的测试文件"""
    print("🔧 创建测试设置文件...")
    
    test_settings_dir = Path("test_settings")
    test_settings_dir.mkdir(exist_ok=True)
    
    settings_data = {
        "editor.fontSize": 14,
        "workbench.colorTheme": "Dark+",
        "augment.enabled": True,
        "augment.autoComplete": True,
        "augment.apiKey": "test_api_key_12345",
        "extensions.autoUpdate": False,
        "augment.suggestions.enabled": True,
        "files.autoSave": "afterDelay"
    }
    
    settings_file = test_settings_dir / "settings.json"
    with open(settings_file, 'w', encoding='utf-8') as f:
        json.dump(settings_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 创建测试设置: {settings_file}")
    return str(settings_file)

def test_augment_detection():
    """测试Augment检测功能"""
    print("\n🔍 测试Augment检测功能...")
    
    try:
        from gui_modules.augment_detector import AugmentDetector
        
        detector = AugmentDetector()
        
        # 测试扩展检测
        test_ext_path = create_test_augment_extension()
        
        print("测试扩展文件夹检测...")
        is_augment = detector._is_augment_extension("augment.vscode-augment-0.477.2", test_ext_path)
        print(f"  扩展检测结果: {'是Augment扩展' if is_augment else '不是Augment扩展'}")
        
        if is_augment:
            ext_info = detector._analyze_extension("augment.vscode-augment-0.477.2", test_ext_path)
            if ext_info:
                print(f"  扩展名称: {ext_info['name']}")
                print(f"  发布者: {ext_info['publisher']}")
                print(f"  版本: {ext_info['version']}")
                print(f"  大小: {detector.format_size(ext_info['size'])}")
        
        # 测试设置文件检测
        test_settings_path = create_test_settings_file()
        
        print("\n测试设置文件检测...")
        settings_results = detector._scan_settings_file(test_settings_path, "test_editor")
        
        if settings_results:
            for result in settings_results:
                print(f"  发现Augment设置: {len(result['augment_settings'])} 项")
                for setting in result['augment_settings'][:3]:  # 只显示前3个
                    print(f"    {setting}")
        
        return True
        
    except Exception as e:
        print(f"❌ Augment检测测试失败: {e}")
        return False

def test_augment_scanning():
    """测试Augment扫描功能"""
    print("\n🔍 测试Augment扫描功能...")
    
    try:
        from gui_modules.augment_detector import AugmentDetector
        
        detector = AugmentDetector()
        
        # 修改检测器配置以使用测试路径
        original_configs = detector.editor_configs.copy()
        
        # 设置测试路径
        detector.editor_configs = {
            "test_editor": {
                "name": "Test Editor",
                "extensions_path": "test_extensions",
                "settings_path": "test_settings/settings.json",
                "storage_path": "test_storage/globalStorage",
                "workspace_storage": "test_storage/workspaceStorage"
            }
        }
        
        # 执行扫描
        scan_results = detector.scan_augment_extensions()
        
        print(f"✅ 扫描结果:")
        print(f"  总发现数: {scan_results.get('total_found', 0)}")
        print(f"  扩展数量: {len(scan_results.get('extensions', []))}")
        print(f"  存储数据: {len(scan_results.get('storage_data', []))}")
        print(f"  设置引用: {len(scan_results.get('settings_references', []))}")
        
        # 显示详细信息
        for extension in scan_results.get('extensions', []):
            print(f"\n📦 发现扩展: {extension['name']}")
            print(f"   版本: {extension['version']}")
            print(f"   大小: {detector.format_size(extension['size'])}")
            print(f"   路径: {extension['folder_path']}")
        
        for storage in scan_results.get('storage_data', []):
            print(f"\n💾 发现存储: {storage['name']}")
            print(f"   类型: {storage['type']}")
            print(f"   大小: {detector.format_size(storage['size'])}")
            print(f"   路径: {storage['path']}")
        
        for settings in scan_results.get('settings_references', []):
            print(f"\n⚙️ 发现设置: {settings['editor']}")
            print(f"   Augment设置数: {len(settings['augment_settings'])}")
            print(f"   路径: {settings['path']}")
        
        # 恢复原始配置
        detector.editor_configs = original_configs
        
        return len(scan_results.get('extensions', [])) > 0
        
    except Exception as e:
        print(f"❌ Augment扫描测试失败: {e}")
        return False

def test_real_augment_detection():
    """测试真实Augment检测"""
    print("\n🔍 测试真实Augment检测...")
    
    try:
        from gui_modules.augment_detector import AugmentDetector
        
        detector = AugmentDetector()
        
        # 扫描真实的Augment扩展
        scan_results = detector.scan_augment_extensions()
        
        total_found = scan_results.get('total_found', 0)
        print(f"✅ 真实扫描结果: 发现 {total_found} 个Augment相关项目")
        
        if total_found > 0:
            print("\n🚨 发现真实的Augment扩展:")
            
            for extension in scan_results.get('extensions', []):
                print(f"\n📦 扩展: {extension['name']}")
                print(f"   发布者: {extension['publisher']}")
                print(f"   版本: {extension['version']}")
                print(f"   大小: {detector.format_size(extension['size'])}")
                print(f"   路径: {extension['folder_path']}")
                print(f"   安装日期: {extension['install_date']}")
            
            for storage in scan_results.get('storage_data', []):
                print(f"\n💾 存储数据: {storage['name']}")
                print(f"   类型: {storage['type']}")
                print(f"   编辑器: {storage['editor']}")
                print(f"   大小: {detector.format_size(storage['size'])}")
                print(f"   路径: {storage['path']}")
            
            for settings in scan_results.get('settings_references', []):
                print(f"\n⚙️ 设置引用: {settings['editor']}")
                print(f"   Augment设置数: {len(settings['augment_settings'])}")
                print(f"   路径: {settings['path']}")
                
                # 显示部分设置
                for setting in settings['augment_settings'][:3]:
                    print(f"     {setting}")
                if len(settings['augment_settings']) > 3:
                    print(f"     ... 还有 {len(settings['augment_settings']) - 3} 个设置")
        else:
            print("✅ 未发现真实的Augment扩展")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实Augment检测失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    test_dirs = ["test_extensions", "test_storage", "test_settings"]
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            try:
                shutil.rmtree(test_dir)
                print(f"✅ 清理: {test_dir}")
            except Exception as e:
                print(f"⚠️ 清理失败 {test_dir}: {e}")

def main():
    """主测试函数"""
    print("🚀 Augment检测功能测试开始")
    print("=" * 50)
    
    tests = [
        ("Augment检测功能", test_augment_detection),
        ("Augment扫描功能", test_augment_scanning),
        ("真实Augment检测", test_real_augment_detection)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                failed += 1
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            failed += 1
    
    # 清理测试文件
    cleanup_test_files()
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed} 个通过, {failed} 个失败")
    
    if failed == 0:
        print("🎉 Augment检测功能完全正常！")
    elif passed > failed:
        print("✅ Augment检测功能基本正常")
    else:
        print("⚠️ Augment检测功能需要进一步修复")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
