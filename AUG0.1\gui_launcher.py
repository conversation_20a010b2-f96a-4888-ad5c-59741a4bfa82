#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AUG 0.1 Enhanced Telemetry ID Modifier - GUI Version
现代化图形用户界面版本

Author: AUG Team
Version: 0.1.0
License: MIT
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import json
import os
import sys
import subprocess
import threading
import time
from datetime import datetime
from pathlib import Path
import webbrowser

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from gui_modules.language_manager import LanguageManager
from gui_modules.config_manager import ConfigManager
from gui_modules.editor_detector import EditorDetector
from gui_modules.telemetry_modifier import TelemetryModifier
from gui_modules.backup_manager import BackupManager
from gui_modules.log_manager import LogManager

class AUGMainWindow:
    """AUG 0.1 主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        self.setup_managers()
        self.setup_window()
        self.setup_variables()
        self.setup_ui()
        self.setup_bindings()
        self.load_initial_data()
        
    def setup_managers(self):
        """初始化管理器"""
        self.lang_manager = LanguageManager()
        self.config_manager = ConfigManager()
        self.editor_detector = EditorDetector()
        self.telemetry_modifier = TelemetryModifier()
        self.backup_manager = BackupManager()
        self.log_manager = LogManager()
        
    def setup_window(self):
        """设置主窗口"""
        # 使用ttkbootstrap创建现代化主题窗口
        self.root = ttk_bs.Window(
            title="AUG 0.1 Enhanced Telemetry ID Modifier",
            themename="superhero",  # 现代暗色主题
            size=(1200, 800),
            resizable=(True, True)
        )
        
        # 设置窗口图标和属性
        self.root.iconbitmap(default="assets/aug_icon.ico") if os.path.exists("assets/aug_icon.ico") else None
        self.root.minsize(1000, 700)
        
        # 居中显示窗口
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_variables(self):
        """设置变量"""
        self.current_language = tk.StringVar(value="zh_CN")
        self.operation_mode = tk.StringVar(value="enhanced")
        self.selected_editors = []
        self.selected_fields = []
        self.is_operation_running = False
        
    def setup_ui(self):
        """设置用户界面"""
        self.create_menu_bar()
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()
        
    def create_menu_bar(self):
        """创建菜单栏"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # 文件菜单
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 操作菜单
        operation_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="操作", menu=operation_menu)
        operation_menu.add_command(label="检测编辑器", command=self.detect_editors)
        operation_menu.add_command(label="备份管理", command=self.open_backup_manager)
        operation_menu.add_separator()
        operation_menu.add_command(label="运行增强模式", command=lambda: self.run_operation("enhanced"))
        operation_menu.add_command(label="运行标准模式", command=lambda: self.run_operation("standard"))
        
        # 工具菜单
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="日志查看器", command=self.open_log_viewer)
        tools_menu.add_command(label="配置编辑器", command=self.open_config_editor)
        tools_menu.add_command(label="系统信息", command=self.show_system_info)
        
        # 帮助菜单
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用指南", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
        
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(fill=tk.X, padx=5, pady=2)
        
        # 语言切换
        ttk.Label(self.toolbar, text="语言:").pack(side=tk.LEFT, padx=5)
        lang_combo = ttk.Combobox(
            self.toolbar, 
            textvariable=self.current_language,
            values=["zh_CN", "en_US"],
            state="readonly",
            width=8
        )
        lang_combo.pack(side=tk.LEFT, padx=5)
        lang_combo.bind("<<ComboboxSelected>>", self.change_language)
        
        # 分隔符
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # 快速操作按钮
        ttk.Button(
            self.toolbar, 
            text="🔍 检测编辑器", 
            command=self.detect_editors,
            style="success.TButton"
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            self.toolbar, 
            text="⚡ 快速模式", 
            command=lambda: self.run_operation("quick"),
            style="warning.TButton"
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            self.toolbar, 
            text="🛡️ 增强模式", 
            command=lambda: self.run_operation("enhanced"),
            style="primary.TButton"
        ).pack(side=tk.LEFT, padx=2)
        
        # 右侧状态指示器
        self.status_indicator = ttk.Label(
            self.toolbar, 
            text="● 就绪", 
            foreground="green"
        )
        self.status_indicator.pack(side=tk.RIGHT, padx=10)
        
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建主要的Notebook控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建各个标签页
        self.create_dashboard_tab()
        self.create_editors_tab()
        self.create_fields_tab()
        self.create_operations_tab()
        self.create_logs_tab()
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 状态文本
        self.status_text = ttk.Label(
            self.status_bar, 
            text="就绪 - AUG 0.1 Enhanced Telemetry ID Modifier"
        )
        self.status_text.pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(
            self.status_bar, 
            mode='indeterminate',
            length=200
        )
        self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)
        
    def create_dashboard_tab(self):
        """创建仪表板标签页"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="🏠 仪表板")
        
        # 欢迎区域
        welcome_frame = ttk.LabelFrame(dashboard_frame, text="欢迎使用 AUG 0.1", padding=10)
        welcome_frame.pack(fill=tk.X, padx=10, pady=5)
        
        welcome_text = """
AUG 0.1 Enhanced Telemetry ID Modifier - 图形界面版本

这是一个专业的隐私保护工具，用于修改代码编辑器的遥测标识符，
保护您的开发隐私和系统信息不被过度收集。

支持的编辑器：VS Code、Cursor、VSCodium、Code-OSS 等
支持的字段：50+ 种不同类型的遥测标识符
操作模式：增强、标准、快速、自定义四种模式
        """
        
        ttk.Label(welcome_frame, text=welcome_text, justify=tk.LEFT).pack(anchor=tk.W)
        
        # 快速操作区域
        quick_frame = ttk.LabelFrame(dashboard_frame, text="快速操作", padding=10)
        quick_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 创建快速操作按钮网格
        button_frame = ttk.Frame(quick_frame)
        button_frame.pack(fill=tk.X)
        
        # 第一行按钮
        row1 = ttk.Frame(button_frame)
        row1.pack(fill=tk.X, pady=2)
        
        ttk.Button(
            row1, text="🔍 检测编辑器", 
            command=self.detect_editors,
            style="success.TButton",
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row1, text="⚡ 快速修改", 
            command=lambda: self.run_operation("quick"),
            style="warning.TButton",
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row1, text="🛡️ 增强保护", 
            command=lambda: self.run_operation("enhanced"),
            style="primary.TButton",
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
        # 第二行按钮
        row2 = ttk.Frame(button_frame)
        row2.pack(fill=tk.X, pady=2)
        
        ttk.Button(
            row2, text="📁 备份管理", 
            command=self.open_backup_manager,
            style="info.TButton",
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row2, text="📋 查看日志", 
            command=self.open_log_viewer,
            style="secondary.TButton",
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row2, text="⚙️ 设置", 
            command=self.open_config_editor,
            style="dark.TButton",
            width=15
        ).pack(side=tk.LEFT, padx=5)
        
    def create_editors_tab(self):
        """创建编辑器标签页"""
        editors_frame = ttk.Frame(self.notebook)
        self.notebook.add(editors_frame, text="📝 编辑器")

        # 顶部控制区域
        control_frame = ttk.Frame(editors_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(
            control_frame,
            text="🔍 检测编辑器",
            command=self.detect_editors,
            style="success.TButton"
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            control_frame,
            text="🔄 刷新",
            command=self.refresh_editors,
            style="info.TButton"
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            control_frame,
            text="⚠️ 关闭运行中的编辑器",
            command=self.close_running_editors,
            style="warning.TButton"
        ).pack(side=tk.LEFT, padx=5)

        # 编辑器列表区域
        list_frame = ttk.LabelFrame(editors_frame, text="检测到的编辑器", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建Treeview显示编辑器信息
        columns = ("name", "status", "storage", "telemetry_fields", "processes")
        self.editors_tree = ttk.Treeview(list_frame, columns=columns, show="tree headings", height=10)

        # 设置列标题
        self.editors_tree.heading("#0", text="编辑器")
        self.editors_tree.heading("name", text="名称")
        self.editors_tree.heading("status", text="状态")
        self.editors_tree.heading("storage", text="存储文件")
        self.editors_tree.heading("telemetry_fields", text="遥测字段")
        self.editors_tree.heading("processes", text="运行进程")

        # 设置列宽
        self.editors_tree.column("#0", width=50)
        self.editors_tree.column("name", width=150)
        self.editors_tree.column("status", width=100)
        self.editors_tree.column("storage", width=200)
        self.editors_tree.column("telemetry_fields", width=100)
        self.editors_tree.column("processes", width=100)

        # 添加滚动条
        editors_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.editors_tree.yview)
        self.editors_tree.configure(yscrollcommand=editors_scrollbar.set)

        self.editors_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        editors_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 详细信息区域
        details_frame = ttk.LabelFrame(editors_frame, text="详细信息", padding=10)
        details_frame.pack(fill=tk.X, padx=10, pady=5)

        self.editor_details_text = tk.Text(details_frame, height=6, wrap=tk.WORD)
        details_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.editor_details_text.yview)
        self.editor_details_text.configure(yscrollcommand=details_scrollbar.set)

        self.editor_details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        details_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定选择事件
        self.editors_tree.bind("<<TreeviewSelect>>", self.on_editor_select)

    def create_fields_tab(self):
        """创建字段标签页"""
        fields_frame = ttk.Frame(self.notebook)
        self.notebook.add(fields_frame, text="🔧 字段")

        # 左侧：字段类别
        left_frame = ttk.LabelFrame(fields_frame, text="字段类别", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(10, 5), pady=10)

        self.categories_listbox = tk.Listbox(left_frame, width=20)
        self.categories_listbox.pack(fill=tk.BOTH, expand=True)
        self.categories_listbox.bind("<<ListboxSelect>>", self.on_category_select)

        # 右侧：字段详情
        right_frame = ttk.Frame(fields_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 10), pady=10)

        # 字段列表
        fields_list_frame = ttk.LabelFrame(right_frame, text="字段列表", padding=10)
        fields_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # 创建字段Treeview
        field_columns = ("field_name", "type", "description", "privacy_level", "required")
        self.fields_tree = ttk.Treeview(fields_list_frame, columns=field_columns, show="headings", height=12)

        # 设置字段列标题
        self.fields_tree.heading("field_name", text="字段名称")
        self.fields_tree.heading("type", text="类型")
        self.fields_tree.heading("description", text="描述")
        self.fields_tree.heading("privacy_level", text="隐私级别")
        self.fields_tree.heading("required", text="必需")

        # 设置字段列宽
        self.fields_tree.column("field_name", width=200)
        self.fields_tree.column("type", width=80)
        self.fields_tree.column("description", width=250)
        self.fields_tree.column("privacy_level", width=80)
        self.fields_tree.column("required", width=60)

        # 添加字段滚动条
        fields_scrollbar = ttk.Scrollbar(fields_list_frame, orient=tk.VERTICAL, command=self.fields_tree.yview)
        self.fields_tree.configure(yscrollcommand=fields_scrollbar.set)

        self.fields_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        fields_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 字段操作区域
        field_ops_frame = ttk.LabelFrame(right_frame, text="字段操作", padding=10)
        field_ops_frame.pack(fill=tk.X)

        ttk.Button(
            field_ops_frame,
            text="全选",
            command=self.select_all_fields,
            style="success.TButton"
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            field_ops_frame,
            text="全不选",
            command=self.deselect_all_fields,
            style="secondary.TButton"
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            field_ops_frame,
            text="反选",
            command=self.invert_field_selection,
            style="info.TButton"
        ).pack(side=tk.LEFT, padx=5)

    def create_operations_tab(self):
        """创建操作标签页"""
        operations_frame = ttk.Frame(self.notebook)
        self.notebook.add(operations_frame, text="⚙️ 操作")

        # 操作模式选择
        mode_frame = ttk.LabelFrame(operations_frame, text="操作模式", padding=10)
        mode_frame.pack(fill=tk.X, padx=10, pady=5)

        # 模式选择按钮
        modes = [
            ("enhanced", "🛡️ 增强模式", "修改所有50+遥测字段，最大隐私保护", "primary"),
            ("standard", "⚖️ 标准模式", "修改10-15个常用字段，平衡保护", "success"),
            ("quick", "⚡ 快速模式", "只修改2-3个核心字段，快速操作", "warning"),
            ("custom", "🔧 自定义模式", "用户完全自定义选择", "info")
        ]

        for mode_id, title, desc, style in modes:
            mode_btn_frame = ttk.Frame(mode_frame)
            mode_btn_frame.pack(fill=tk.X, pady=2)

            ttk.Radiobutton(
                mode_btn_frame,
                text=title,
                variable=self.operation_mode,
                value=mode_id,
                command=self.on_mode_change
            ).pack(side=tk.LEFT)

            ttk.Label(mode_btn_frame, text=desc, foreground="gray").pack(side=tk.LEFT, padx=(10, 0))

        # 操作控制区域
        control_frame = ttk.LabelFrame(operations_frame, text="操作控制", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # 操作按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)

        self.start_button = ttk.Button(
            button_frame,
            text="🚀 开始操作",
            command=self.start_operation,
            style="success.TButton",
            width=15
        )
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(
            button_frame,
            text="⏹️ 停止操作",
            command=self.stop_operation,
            style="danger.TButton",
            width=15,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(
            button_frame,
            text="📁 打开备份目录",
            command=self.open_backup_directory,
            style="info.TButton",
            width=15
        ).pack(side=tk.LEFT, padx=5)

        # 进度显示区域
        progress_frame = ttk.LabelFrame(operations_frame, text="操作进度", padding=10)
        progress_frame.pack(fill=tk.X, padx=10, pady=5)

        self.operation_progress = ttk.Progressbar(
            progress_frame,
            mode='determinate',
            length=400
        )
        self.operation_progress.pack(fill=tk.X, pady=5)

        self.progress_label = ttk.Label(progress_frame, text="等待操作...")
        self.progress_label.pack()

    def create_logs_tab(self):
        """创建日志标签页"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="📋 日志")

        # 日志控制区域
        log_control_frame = ttk.Frame(logs_frame)
        log_control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(
            log_control_frame,
            text="🔄 刷新日志",
            command=self.refresh_logs,
            style="info.TButton"
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            log_control_frame,
            text="🗑️ 清空日志",
            command=self.clear_logs,
            style="warning.TButton"
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            log_control_frame,
            text="💾 保存日志",
            command=self.save_logs,
            style="success.TButton"
        ).pack(side=tk.LEFT, padx=5)

        # 日志级别过滤
        ttk.Label(log_control_frame, text="日志级别:").pack(side=tk.LEFT, padx=(20, 5))
        self.log_level_var = tk.StringVar(value="ALL")
        log_level_combo = ttk.Combobox(
            log_control_frame,
            textvariable=self.log_level_var,
            values=["ALL", "ERROR", "WARNING", "INFO", "SUCCESS", "VERBOSE"],
            state="readonly",
            width=10
        )
        log_level_combo.pack(side=tk.LEFT, padx=5)
        log_level_combo.bind("<<ComboboxSelected>>", self.filter_logs)

        # 日志显示区域
        log_display_frame = ttk.LabelFrame(logs_frame, text="实时日志", padding=10)
        log_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.log_text = tk.Text(
            log_display_frame,
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg="#1e1e1e",
            fg="#ffffff",
            insertbackground="#ffffff"
        )

        log_scrollbar = ttk.Scrollbar(log_display_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 配置日志颜色标签
        self.log_text.tag_configure("ERROR", foreground="#ff6b6b")
        self.log_text.tag_configure("WARNING", foreground="#ffa500")
        self.log_text.tag_configure("INFO", foreground="#74c0fc")
        self.log_text.tag_configure("SUCCESS", foreground="#51cf66")
        self.log_text.tag_configure("VERBOSE", foreground="#868e96")

    def setup_bindings(self):
        """设置事件绑定"""
        # 键盘快捷键
        self.root.bind("<Control-q>", lambda e: self.on_closing())
        self.root.bind("<F5>", lambda e: self.detect_editors())
        self.root.bind("<Control-r>", lambda e: self.refresh_all())

    def load_initial_data(self):
        """加载初始数据"""
        # 设置日志回调
        self.log_manager.add_log_callback(self.on_log_message)

        # 设置遥测修改器回调
        self.telemetry_modifier.set_log_callback(self.on_log_message)
        self.telemetry_modifier.set_progress_callback(self.on_progress_update)

        # 初始化界面数据
        self.load_field_categories()
        self.detect_editors()

        # 记录启动日志
        self.log_manager.info("AUG 0.1 GUI 启动成功")

    def load_field_categories(self):
        """加载字段类别"""
        categories = self.config_manager.get_field_categories()

        # 清空现有项目
        self.categories_listbox.delete(0, tk.END)

        # 添加类别
        for category in categories:
            display_name = self.get_category_display_name(category)
            self.categories_listbox.insert(tk.END, display_name)

    def get_category_display_name(self, category: str) -> str:
        """获取类别显示名称"""
        category_names = {
            "core_identifiers": "核心标识符",
            "installation_tracking": "安装追踪",
            "user_tracking": "用户追踪",
            "hardware_system": "硬件系统",
            "network_location": "网络位置",
            "usage_behavior": "使用行为",
            "performance_diagnostics": "性能诊断",
            "ai_copilot": "AI和Copilot",
            "microsoft_ecosystem": "Microsoft生态",
            "privacy_compliance": "隐私合规"
        }
        return category_names.get(category, category)

    # 事件处理方法
    def change_language(self, event=None):
        """切换语言"""
        new_language = self.current_language.get()
        self.lang_manager.set_language(new_language)
        self.config_manager.set_gui_config("window.language", new_language)

        # 更新界面文本
        self.update_interface_text()
        self.log_manager.info(f"语言已切换到: {new_language}")

    def update_interface_text(self):
        """更新界面文本"""
        # 这里可以添加界面文本更新逻辑
        # 由于tkinter的限制，完整的动态语言切换比较复杂
        # 可以考虑重启应用或者只更新部分文本
        pass

    def detect_editors(self):
        """检测编辑器"""
        self.status_indicator.config(text="● 检测中", foreground="orange")
        self.progress_bar.start()

        def worker():
            try:
                self.log_manager.info("开始检测编辑器...")
                detection_results = self.editor_detector.detect_all_editors()

                # 在主线程中更新UI
                self.root.after(0, lambda: self.update_editors_display(detection_results))

            except Exception as e:
                self.log_manager.error(f"检测编辑器失败: {str(e)}")
                self.root.after(0, lambda: self.on_detection_error(str(e)))

        threading.Thread(target=worker, daemon=True).start()

    def update_editors_display(self, detection_results: dict):
        """更新编辑器显示"""
        # 清空现有项目
        for item in self.editors_tree.get_children():
            self.editors_tree.delete(item)

        # 添加检测结果
        for editor_id, info in detection_results.items():
            icon = info.get("icon", "⚪")
            name = info.get("display_name", info.get("name", editor_id))
            status = self.get_status_text(info.get("status", "unknown"))
            storage = "✓" if info.get("storage_exists", False) else "✗"
            fields = str(info.get("telemetry_fields", 0))
            processes = str(len(info.get("processes", [])))

            # 根据状态设置颜色标签
            tags = []
            if info.get("running", False):
                tags.append("running")
            elif info.get("installed", False):
                tags.append("installed")
            else:
                tags.append("not_found")

            self.editors_tree.insert("", tk.END,
                                   text=icon,
                                   values=(name, status, storage, fields, processes),
                                   tags=tags)

        # 配置标签颜色
        self.editors_tree.tag_configure("running", foreground="#ffa500")
        self.editors_tree.tag_configure("installed", foreground="#51cf66")
        self.editors_tree.tag_configure("not_found", foreground="#868e96")

        # 更新状态
        installed_count = sum(1 for info in detection_results.values() if info.get("installed", False))
        running_count = sum(1 for info in detection_results.values() if info.get("running", False))

        self.status_indicator.config(
            text=f"● 检测完成 ({installed_count}个已安装, {running_count}个运行中)",
            foreground="green"
        )
        self.progress_bar.stop()

        self.log_manager.success(f"编辑器检测完成: {installed_count}个已安装, {running_count}个运行中")

    def get_status_text(self, status: str) -> str:
        """获取状态文本"""
        status_map = {
            "installed": "已安装",
            "running": "运行中",
            "not_found": "未找到",
            "unknown": "未知"
        }
        return status_map.get(status, status)

    def on_detection_error(self, error_message: str):
        """检测错误处理"""
        self.status_indicator.config(text="● 检测失败", foreground="red")
        self.progress_bar.stop()
        messagebox.showerror("检测失败", f"编辑器检测失败:\n{error_message}")

    def refresh_editors(self):
        """刷新编辑器列表"""
        self.detect_editors()

    def close_running_editors(self):
        """关闭运行中的编辑器"""
        running_editors = self.editor_detector.get_running_editors()

        if not running_editors:
            messagebox.showinfo("信息", "没有检测到运行中的编辑器")
            return

        editor_names = [self.editor_detector.get_editor_info(eid).get("name", eid) for eid in running_editors]

        if messagebox.askyesno("确认", f"确定要关闭以下编辑器吗？\n\n{chr(10).join(editor_names)}"):
            success_count = 0
            for editor_id in running_editors:
                if self.editor_detector.terminate_editor_processes(editor_id):
                    success_count += 1

            if success_count > 0:
                self.log_manager.success(f"成功关闭 {success_count} 个编辑器")
                # 刷新显示
                self.detect_editors()
            else:
                self.log_manager.error("关闭编辑器失败")

    def on_editor_select(self, event):
        """编辑器选择事件"""
        selection = self.editors_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.editors_tree.item(item, "values")

        if values:
            editor_name = values[0]
            # 查找对应的编辑器ID
            detection_results = self.editor_detector.detect_all_editors()
            editor_info = None

            for editor_id, info in detection_results.items():
                if info.get("display_name") == editor_name or info.get("name") == editor_name:
                    editor_info = info
                    break

            if editor_info:
                self.display_editor_details(editor_info)

    def display_editor_details(self, editor_info: dict):
        """显示编辑器详细信息"""
        self.editor_details_text.delete(1.0, tk.END)

        details = f"""编辑器: {editor_info.get('name', 'Unknown')}
状态: {self.get_status_text(editor_info.get('status', 'unknown'))}
存储文件: {editor_info.get('storage_path', 'N/A')}
文件大小: {editor_info.get('storage_size', 0)} 字节
最后修改: {datetime.fromtimestamp(editor_info.get('last_modified', 0)).strftime('%Y-%m-%d %H:%M:%S') if editor_info.get('last_modified') else 'N/A'}
遥测字段: {editor_info.get('telemetry_fields', 0)} 个
运行进程: {len(editor_info.get('processes', []))} 个
安装路径: {editor_info.get('install_path', 'N/A')}
"""

        self.editor_details_text.insert(1.0, details)

    def on_category_select(self, event):
        """字段类别选择事件"""
        selection = self.categories_listbox.curselection()
        if not selection:
            return

        index = selection[0]
        category_display = self.categories_listbox.get(index)

        # 查找实际的类别名称
        categories = self.config_manager.get_field_categories()
        if index < len(categories):
            category = categories[index]
            self.load_fields_by_category(category)

    def load_fields_by_category(self, category: str):
        """根据类别加载字段"""
        # 清空现有项目
        for item in self.fields_tree.get_children():
            self.fields_tree.delete(item)

        # 获取字段
        fields = self.config_manager.get_fields_by_category(category)

        for field_name, field_info in fields.items():
            field_type = field_info.get("type", "unknown")
            description = field_info.get("description", "")
            privacy_level = field_info.get("privacy_level", "medium")
            required = "是" if field_info.get("required", False) else "否"

            self.fields_tree.insert("", tk.END,
                                  values=(field_name, field_type, description, privacy_level, required))

    def select_all_fields(self):
        """全选字段"""
        for item in self.fields_tree.get_children():
            self.fields_tree.selection_add(item)

    def deselect_all_fields(self):
        """全不选字段"""
        self.fields_tree.selection_remove(self.fields_tree.selection())

    def invert_field_selection(self):
        """反选字段"""
        all_items = self.fields_tree.get_children()
        selected_items = self.fields_tree.selection()

        # 取消当前选择
        self.fields_tree.selection_remove(selected_items)

        # 选择未选中的项目
        for item in all_items:
            if item not in selected_items:
                self.fields_tree.selection_add(item)

    def on_mode_change(self):
        """操作模式改变事件"""
        mode = self.operation_mode.get()
        self.log_manager.info(f"操作模式已切换到: {mode}")

        # 根据模式更新界面
        if mode == "custom":
            # 自定义模式下启用字段选择
            self.notebook.tab(2, state="normal")  # 字段标签页
        else:
            # 其他模式下可以禁用字段选择或显示预设字段
            pass

    def start_operation(self):
        """开始操作"""
        if self.is_operation_running:
            messagebox.showwarning("警告", "操作正在进行中，请等待完成")
            return

        # 获取选择的编辑器
        selected_editors = self.get_selected_editors()
        if not selected_editors:
            messagebox.showerror("错误", "请先选择要操作的编辑器")
            return

        # 获取操作模式
        mode = self.operation_mode.get()

        # 获取选择的字段（自定义模式）
        selected_fields = []
        if mode == "custom":
            selected_fields = self.get_selected_fields()
            if not selected_fields:
                messagebox.showerror("错误", "自定义模式下请选择要修改的字段")
                return

        # 验证操作
        errors, warnings = self.telemetry_modifier.validate_operation(mode, selected_editors, selected_fields)

        if errors:
            messagebox.showerror("错误", "操作验证失败:\n" + "\n".join(errors))
            return

        if warnings:
            if not messagebox.askyesno("警告", "发现以下警告，是否继续?\n" + "\n".join(warnings)):
                return

        # 显示操作摘要
        summary = self.telemetry_modifier.get_operation_summary(mode, selected_editors, selected_fields)
        summary_text = f"""操作摘要:
模式: {mode}
编辑器: {summary['editor_count']} 个
字段: {summary['field_count']} 个
预计时间: {summary['estimated_time']} 秒

确定要执行此操作吗？"""

        if not messagebox.askyesno("确认操作", summary_text):
            return

        # 开始操作
        self.is_operation_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.operation_progress.config(value=0)
        self.progress_label.config(text="正在准备操作...")

        # 异步执行操作
        self.telemetry_modifier.modify_telemetry_async(mode, selected_editors, selected_fields)

    def stop_operation(self):
        """停止操作"""
        if not self.is_operation_running:
            return

        if messagebox.askyesno("确认", "确定要停止当前操作吗？"):
            self.telemetry_modifier.stop_operation()
            self.on_operation_stopped()

    def on_operation_stopped(self):
        """操作停止处理"""
        self.is_operation_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.operation_progress.config(value=0)
        self.progress_label.config(text="操作已停止")

    def get_selected_editors(self) -> list:
        """获取选择的编辑器"""
        selected = []

        # 从编辑器树中获取选择的项目
        for item in self.editors_tree.selection():
            values = self.editors_tree.item(item, "values")
            if values:
                editor_name = values[0]
                # 查找对应的编辑器ID
                detection_results = self.editor_detector.detect_all_editors()
                for editor_id, info in detection_results.items():
                    if (info.get("display_name") == editor_name or
                        info.get("name") == editor_name):
                        if info.get("installed", False):
                            selected.append(editor_id)
                        break

        # 如果没有选择，返回所有已安装的编辑器
        if not selected:
            selected = self.editor_detector.get_installed_editors()

        return selected

    def get_selected_fields(self) -> list:
        """获取选择的字段"""
        selected = []

        for item in self.fields_tree.selection():
            values = self.fields_tree.item(item, "values")
            if values:
                field_name = values[0]
                selected.append(field_name)

        return selected

    def on_progress_update(self, progress: int, status: str):
        """进度更新回调"""
        self.root.after(0, lambda: self._update_progress_ui(progress, status))

    def _update_progress_ui(self, progress: int, status: str):
        """更新进度UI"""
        self.operation_progress.config(value=progress)
        self.progress_label.config(text=status)

        if progress >= 100:
            self.on_operation_completed()

    def on_operation_completed(self):
        """操作完成处理"""
        self.is_operation_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

        # 刷新编辑器显示
        self.detect_editors()

        messagebox.showinfo("完成", "遥测ID修改操作已完成！\n\n请重启相关编辑器以应用更改。")

    def on_log_message(self, level: str, message: str):
        """日志消息回调"""
        self.root.after(0, lambda: self._add_log_message(level, message))

    def _add_log_message(self, level: str, message: str):
        """添加日志消息到UI"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_line = f"[{timestamp}] [{level}] {message}\n"

        # 添加到日志文本框
        self.log_text.insert(tk.END, log_line, level)
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > 1000:
            self.log_text.delete(1.0, "100.0")

    def refresh_logs(self):
        """刷新日志"""
        self.log_text.delete(1.0, tk.END)

        # 读取日志文件
        log_lines = self.log_manager.read_log_file(500)  # 最近500行

        for line in log_lines:
            # 解析日志级别
            level = "INFO"
            if "[ERROR]" in line:
                level = "ERROR"
            elif "[WARNING]" in line:
                level = "WARNING"
            elif "[SUCCESS]" in line:
                level = "SUCCESS"
            elif "[VERBOSE]" in line:
                level = "VERBOSE"

            self.log_text.insert(tk.END, line, level)

        self.log_text.see(tk.END)

    def clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空所有日志吗？"):
            self.log_text.delete(1.0, tk.END)
            self.log_manager.clear_log_file()

    def save_logs(self):
        """保存日志"""
        from tkinter import filedialog

        file_path = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".log",
            filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            level_filter = self.log_level_var.get()
            if self.log_manager.export_logs(file_path, level_filter):
                messagebox.showinfo("成功", f"日志已保存到: {file_path}")
            else:
                messagebox.showerror("失败", "保存日志文件失败")

    def filter_logs(self, event=None):
        """过滤日志"""
        # 重新加载日志并应用过滤器
        self.refresh_logs()

    # 菜单事件处理方法
    def import_config(self):
        """导入配置"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="导入配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            if self.config_manager.import_config(file_path):
                messagebox.showinfo("成功", "配置导入成功！\n\n请重启应用以应用新配置。")
                self.log_manager.success(f"配置已从 {file_path} 导入")
            else:
                messagebox.showerror("失败", "配置导入失败")

    def export_config(self):
        """导出配置"""
        from tkinter import filedialog

        file_path = filedialog.asksaveasfilename(
            title="导出配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            if self.config_manager.export_config(file_path):
                messagebox.showinfo("成功", f"配置已导出到: {file_path}")
                self.log_manager.success(f"配置已导出到 {file_path}")
            else:
                messagebox.showerror("失败", "配置导出失败")

    def open_backup_manager(self):
        """打开备份管理器"""
        BackupManagerWindow(self.root, self.backup_manager, self.log_manager)

    def open_log_viewer(self):
        """打开日志查看器"""
        LogViewerWindow(self.root, self.log_manager)

    def open_config_editor(self):
        """打开配置编辑器"""
        ConfigEditorWindow(self.root, self.config_manager, self.log_manager)

    def show_system_info(self):
        """显示系统信息"""
        SystemInfoWindow(self.root, self.editor_detector)

    def show_help(self):
        """显示帮助"""
        HelpWindow(self.root)

    def show_about(self):
        """显示关于"""
        about_text = """AUG 0.1 Enhanced Telemetry ID Modifier
图形用户界面版本

版本: 0.1.0
作者: AUG Team
许可证: MIT License

这是一个专业的隐私保护工具，用于修改代码编辑器的遥测标识符，
保护您的开发隐私和系统信息不被过度收集。

支持的编辑器:
• Visual Studio Code
• Cursor Editor
• VSCodium
• VS Code Insiders
• Code - OSS

功能特性:
• 50+ 种遥测字段修改
• 4种操作模式
• 自动备份和恢复
• 实时日志监控
• 多语言界面

© 2024 AUG Team. All rights reserved."""

        messagebox.showinfo("关于 AUG 0.1", about_text)

    def open_backup_directory(self):
        """打开备份目录"""
        import subprocess
        import platform

        backup_dir = Path("backups")
        backup_dir.mkdir(exist_ok=True)

        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(backup_dir)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(backup_dir)])
            else:  # Linux
                subprocess.run(["xdg-open", str(backup_dir)])
        except Exception as e:
            self.log_manager.error(f"打开备份目录失败: {e}")
            messagebox.showerror("错误", f"无法打开备份目录: {e}")

    def refresh_all(self):
        """刷新所有数据"""
        self.detect_editors()
        self.load_field_categories()
        self.refresh_logs()
        self.log_manager.info("所有数据已刷新")

    def run_operation(self, mode: str):
        """运行指定模式的操作"""
        self.operation_mode.set(mode)
        self.start_operation()

    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """关闭应用程序"""
        if messagebox.askokcancel("退出", "确定要退出 AUG 0.1 吗？"):
            self.log_manager.info("AUG 0.1 GUI 正在关闭...")
            self.root.destroy()


# 辅助窗口类（简化版本，完整版本需要更多代码）
class BackupManagerWindow:
    """备份管理器窗口"""
    def __init__(self, parent, backup_manager, log_manager):
        self.backup_manager = backup_manager
        self.log_manager = log_manager

        self.window = tk.Toplevel(parent)
        self.window.title("备份管理器")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()

        # 简化的备份管理界面
        ttk.Label(self.window, text="备份管理器", font=("Arial", 16)).pack(pady=10)
        ttk.Label(self.window, text="此功能正在开发中...").pack(pady=20)

        ttk.Button(self.window, text="关闭", command=self.window.destroy).pack(pady=10)


class LogViewerWindow:
    """日志查看器窗口"""
    def __init__(self, parent, log_manager):
        self.log_manager = log_manager

        self.window = tk.Toplevel(parent)
        self.window.title("日志查看器")
        self.window.geometry("900x700")
        self.window.transient(parent)
        self.window.grab_set()

        # 简化的日志查看界面
        ttk.Label(self.window, text="日志查看器", font=("Arial", 16)).pack(pady=10)
        ttk.Label(self.window, text="此功能正在开发中...").pack(pady=20)

        ttk.Button(self.window, text="关闭", command=self.window.destroy).pack(pady=10)


class ConfigEditorWindow:
    """配置编辑器窗口"""
    def __init__(self, parent, config_manager, log_manager):
        self.config_manager = config_manager
        self.log_manager = log_manager

        self.window = tk.Toplevel(parent)
        self.window.title("配置编辑器")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()

        # 简化的配置编辑界面
        ttk.Label(self.window, text="配置编辑器", font=("Arial", 16)).pack(pady=10)
        ttk.Label(self.window, text="此功能正在开发中...").pack(pady=20)

        ttk.Button(self.window, text="关闭", command=self.window.destroy).pack(pady=10)


class SystemInfoWindow:
    """系统信息窗口"""
    def __init__(self, parent, editor_detector):
        self.editor_detector = editor_detector

        self.window = tk.Toplevel(parent)
        self.window.title("系统信息")
        self.window.geometry("600x500")
        self.window.transient(parent)
        self.window.grab_set()

        # 简化的系统信息界面
        ttk.Label(self.window, text="系统信息", font=("Arial", 16)).pack(pady=10)
        ttk.Label(self.window, text="此功能正在开发中...").pack(pady=20)

        ttk.Button(self.window, text="关闭", command=self.window.destroy).pack(pady=10)


class HelpWindow:
    """帮助窗口"""
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("使用帮助")
        self.window.geometry("700x600")
        self.window.transient(parent)
        self.window.grab_set()

        # 简化的帮助界面
        ttk.Label(self.window, text="AUG 0.1 使用帮助", font=("Arial", 16)).pack(pady=10)

        help_text = """
AUG 0.1 Enhanced Telemetry ID Modifier 使用指南

1. 检测编辑器
   - 点击"检测编辑器"按钮自动扫描系统中的代码编辑器
   - 支持 VS Code、Cursor、VSCodium 等主流编辑器

2. 选择操作模式
   - 增强模式: 修改所有50+遥测字段，最大隐私保护
   - 标准模式: 修改10-15个常用字段，平衡保护
   - 快速模式: 只修改2-3个核心字段，快速操作
   - 自定义模式: 用户完全自定义选择

3. 执行操作
   - 选择要操作的编辑器和字段
   - 点击"开始操作"执行修改
   - 操作完成后重启编辑器以应用更改

4. 备份管理
   - 所有操作都会自动创建备份
   - 可以通过备份管理器恢复原始设置

5. 日志监控
   - 实时查看操作日志
   - 支持日志级别过滤和导出

注意事项:
- 建议在操作前关闭所有编辑器
- 定期清理旧备份文件
- 如有问题请查看日志获取详细信息
        """

        text_widget = tk.Text(self.window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.insert(1.0, help_text)
        text_widget.config(state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Button(self.window, text="关闭", command=self.window.destroy).pack(pady=10)

if __name__ == "__main__":
    try:
        app = AUGMainWindow()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"应用程序启动失败：{str(e)}")
