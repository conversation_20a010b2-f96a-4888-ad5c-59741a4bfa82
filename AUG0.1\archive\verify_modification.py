"""
验证Augment ID修改是否生效
"""

import os
import json
import sys
import argparse
from datetime import datetime

def load_storage_file(file_path):
    """加载存储文件并返回JSON内容"""
    try:
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在: {file_path}")
            return None
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = json.load(f)
        return content
    except Exception as e:
        print(f"错误: 无法读取文件 {file_path}: {str(e)}")
        return None

def load_backup_file(backup_path):
    """加载备份文件并返回JSON内容"""
    try:
        if not os.path.exists(backup_path):
            print(f"错误: 备份文件不存在: {backup_path}")
            return None
            
        with open(backup_path, 'r', encoding='utf-8') as f:
            content = json.load(f)
        return content
    except Exception as e:
        print(f"错误: 无法读取备份文件 {backup_path}: {str(e)}")
        return None

def compare_telemetry_ids(current, backup):
    """比较当前文件和备份文件中的遥测ID"""
    if not current or not backup:
        return False, []
        
    changes = []
    modified = False
    
    # 检查常见的遥测字段
    telemetry_fields = [
        "telemetry.machineId",
        "telemetry.devDeviceId",
        "telemetry.sessionId",
        "telemetry.sqmId"
    ]
    
    for field in telemetry_fields:
        if field in current and field in backup:
            if current[field] != backup[field]:
                changes.append({
                    "field": field,
                    "old_value": backup[field],
                    "new_value": current[field]
                })
                modified = True
    
    return modified, changes

def verify_editor(editor_name):
    """验证特定编辑器的修改是否生效"""
    # 确定存储文件路径
    if editor_name == "VSCode":
        storage_path = os.path.expandvars("%APPDATA%\\Code\\User\\globalStorage\\storage.json")
    elif editor_name == "Cursor":
        storage_path = os.path.expandvars("%APPDATA%\\Cursor\\User\\globalStorage\\storage.json")
    elif editor_name == "VSCodium":
        storage_path = os.path.expandvars("%APPDATA%\\VSCodium\\User\\globalStorage\\storage.json")
    elif editor_name == "VSCodeInsiders":
        storage_path = os.path.expandvars("%APPDATA%\\Code - Insiders\\User\\globalStorage\\storage.json")
    else:
        print(f"不支持的编辑器: {editor_name}")
        return False
    
    # 查找最新的备份文件
    backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backups")
    if not os.path.exists(backup_dir):
        print(f"错误: 备份目录不存在: {backup_dir}")
        return False
        
    backup_files = [f for f in os.listdir(backup_dir) if f.startswith(f"{editor_name}_") and f.endswith('.json')]
    if not backup_files:
        print(f"错误: 未找到 {editor_name} 的备份文件")
        return False
        
    # 按时间排序，获取最新的备份
    backup_files.sort(reverse=True)
    latest_backup = os.path.join(backup_dir, backup_files[0])
    
    print(f"验证 {editor_name} 的修改:")
    print(f"- 当前文件: {storage_path}")
    print(f"- 备份文件: {latest_backup}")
    
    # 加载文件
    current_data = load_storage_file(storage_path)
    backup_data = load_backup_file(latest_backup)
    
    # 比较ID
    modified, changes = compare_telemetry_ids(current_data, backup_data)
    
    if modified:
        print(f"\n✓ {editor_name} 的遥测ID已成功修改!")
        print("\n修改详情:")
        for change in changes:
            print(f"  {change['field']}:")
            print(f"    旧值: {change['old_value']}")
            print(f"    新值: {change['new_value']}")
        return True
    else:
        print(f"\n✗ {editor_name} 的遥测ID未发生变化!")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="验证Augment ID修改是否生效")
    parser.add_argument("--editor", "-e", help="要验证的编辑器 (VSCode, Cursor, VSCodium, VSCodeInsiders)")
    args = parser.parse_args()
    
    print("Augment ID修改验证工具")
    print("=" * 50)
    
    if args.editor:
        verify_editor(args.editor)
    else:
        # 验证所有可能的编辑器
        editors = ["VSCode", "Cursor", "VSCodium", "VSCodeInsiders"]
        results = []
        
        for editor in editors:
            result = verify_editor(editor)
            results.append((editor, result))
            print("\n" + "-" * 50 + "\n")
        
        # 显示总结
        print("\n验证结果总结:")
        print("=" * 50)
        for editor, result in results:
            status = "成功" if result else "失败或未修改"
            print(f"{editor}: {status}")

if __name__ == "__main__":
    main()