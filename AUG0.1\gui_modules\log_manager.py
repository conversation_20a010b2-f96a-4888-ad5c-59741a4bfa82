#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理器模块
管理应用程序日志记录和显示
"""

import os
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Callable
import threading
import queue

class LogManager:
    """日志管理器类"""
    
    def __init__(self):
        """初始化日志管理器"""
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        self.log_file = self.log_dir / f"gui_log_{datetime.now().strftime('%Y%m%d')}.log"
        self.log_queue = queue.Queue()
        self.log_callbacks = []
        
        # 设置日志记录器
        self.setup_logger()
        
        # 启动日志处理线程
        self.log_thread = threading.Thread(target=self._log_worker, daemon=True)
        self.log_thread.start()
        
    def setup_logger(self):
        """设置日志记录器"""
        # 创建日志记录器
        self.logger = logging.getLogger('AUG_GUI')
        self.logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 设置格式
        formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # 添加自定义处理器用于GUI显示
        gui_handler = GuiLogHandler(self.log_queue)
        gui_handler.setLevel(logging.DEBUG)
        gui_handler.setFormatter(formatter)
        self.logger.addHandler(gui_handler)
        
    def add_log_callback(self, callback: Callable[[str, str], None]):
        """添加日志回调函数"""
        self.log_callbacks.append(callback)
        
    def remove_log_callback(self, callback: Callable[[str, str], None]):
        """移除日志回调函数"""
        if callback in self.log_callbacks:
            self.log_callbacks.remove(callback)
            
    def _log_worker(self):
        """日志处理工作线程"""
        while True:
            try:
                log_record = self.log_queue.get(timeout=1)
                if log_record is None:
                    break
                    
                # 通知所有回调函数
                for callback in self.log_callbacks:
                    try:
                        callback(log_record['level'], log_record['message'])
                    except Exception as e:
                        print(f"日志回调函数出错: {e}")
                        
            except queue.Empty:
                continue
            except Exception as e:
                print(f"日志处理线程出错: {e}")
                
    def log(self, level: str, message: str):
        """记录日志"""
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL,
            'SUCCESS': logging.INFO,  # 自定义级别映射到INFO
            'VERBOSE': logging.DEBUG   # 自定义级别映射到DEBUG
        }
        
        log_level = level_map.get(level.upper(), logging.INFO)
        self.logger.log(log_level, message)
        
    def debug(self, message: str):
        """记录调试日志"""
        self.log('DEBUG', message)
        
    def info(self, message: str):
        """记录信息日志"""
        self.log('INFO', message)
        
    def warning(self, message: str):
        """记录警告日志"""
        self.log('WARNING', message)
        
    def error(self, message: str):
        """记录错误日志"""
        self.log('ERROR', message)
        
    def success(self, message: str):
        """记录成功日志"""
        self.log('SUCCESS', message)
        
    def verbose(self, message: str):
        """记录详细日志"""
        self.log('VERBOSE', message)
        
    def read_log_file(self, lines: int = 1000) -> List[str]:
        """读取日志文件"""
        try:
            if not self.log_file.exists():
                return []
                
            with open(self.log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                
            # 返回最后N行
            return all_lines[-lines:] if len(all_lines) > lines else all_lines
            
        except Exception as e:
            print(f"读取日志文件失败: {e}")
            return []
            
    def clear_log_file(self):
        """清空日志文件"""
        try:
            if self.log_file.exists():
                self.log_file.unlink()
            self.setup_logger()  # 重新设置日志记录器
            self.info("日志文件已清空")
        except Exception as e:
            self.error(f"清空日志文件失败: {e}")
            
    def get_log_files(self) -> List[Dict]:
        """获取所有日志文件列表"""
        log_files = []
        
        try:
            for log_file in self.log_dir.glob("*.log"):
                file_info = {
                    "name": log_file.name,
                    "path": str(log_file),
                    "size": log_file.stat().st_size,
                    "created": datetime.fromtimestamp(log_file.stat().st_ctime),
                    "modified": datetime.fromtimestamp(log_file.stat().st_mtime)
                }
                log_files.append(file_info)
                
        except Exception as e:
            self.error(f"获取日志文件列表失败: {e}")
            
        # 按修改时间排序
        log_files.sort(key=lambda x: x["modified"], reverse=True)
        return log_files
        
    def export_logs(self, output_path: str, level_filter: str = None) -> bool:
        """导出日志"""
        try:
            lines = self.read_log_file()
            
            # 过滤日志级别
            if level_filter and level_filter != "ALL":
                filtered_lines = []
                for line in lines:
                    if f"[{level_filter}]" in line:
                        filtered_lines.append(line)
                lines = filtered_lines
                
            with open(output_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
                
            self.info(f"日志已导出到: {output_path}")
            return True
            
        except Exception as e:
            self.error(f"导出日志失败: {e}")
            return False
            
    def get_log_statistics(self) -> Dict:
        """获取日志统计信息"""
        try:
            lines = self.read_log_file()
            
            stats = {
                "total_lines": len(lines),
                "levels": {
                    "DEBUG": 0,
                    "INFO": 0,
                    "WARNING": 0,
                    "ERROR": 0,
                    "SUCCESS": 0,
                    "VERBOSE": 0
                },
                "file_size": self.log_file.stat().st_size if self.log_file.exists() else 0,
                "last_modified": datetime.fromtimestamp(self.log_file.stat().st_mtime) if self.log_file.exists() else None
            }
            
            # 统计各级别日志数量
            for line in lines:
                for level in stats["levels"]:
                    if f"[{level}]" in line:
                        stats["levels"][level] += 1
                        break
                        
            return stats
            
        except Exception as e:
            self.error(f"获取日志统计失败: {e}")
            return {}
            
    def cleanup_old_logs(self, days: int = 30) -> int:
        """清理旧日志文件"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = 0
            
            for log_file in self.log_dir.glob("*.log"):
                if log_file == self.log_file:
                    continue  # 不删除当前日志文件
                    
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_date:
                    log_file.unlink()
                    deleted_count += 1
                    
            self.info(f"清理了 {deleted_count} 个旧日志文件")
            return deleted_count
            
        except Exception as e:
            self.error(f"清理旧日志文件失败: {e}")
            return 0
            
    def search_logs(self, keyword: str, level_filter: str = None) -> List[str]:
        """搜索日志"""
        try:
            lines = self.read_log_file()
            results = []
            
            for line in lines:
                # 级别过滤
                if level_filter and level_filter != "ALL":
                    if f"[{level_filter}]" not in line:
                        continue
                        
                # 关键词搜索
                if keyword.lower() in line.lower():
                    results.append(line)
                    
            return results
            
        except Exception as e:
            self.error(f"搜索日志失败: {e}")
            return []


class GuiLogHandler(logging.Handler):
    """GUI日志处理器"""
    
    def __init__(self, log_queue: queue.Queue):
        super().__init__()
        self.log_queue = log_queue
        
    def emit(self, record):
        """发送日志记录"""
        try:
            # 格式化日志消息
            message = self.format(record)
            
            # 确定日志级别
            level = record.levelname
            if 'SUCCESS' in message or '成功' in message:
                level = 'SUCCESS'
            elif 'VERBOSE' in message or record.levelno < logging.INFO:
                level = 'VERBOSE'
                
            # 放入队列
            self.log_queue.put({
                'level': level,
                'message': message,
                'timestamp': datetime.fromtimestamp(record.created)
            })
            
        except Exception:
            self.handleError(record)
