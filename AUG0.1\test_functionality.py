#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AUG 0.1 功能测试脚本
验证所有功能是否真实有效
"""

import os
import sys
import json
import sqlite3
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from gui_modules.language_manager import LanguageManager
        from gui_modules.config_manager import ConfigManager
        from gui_modules.editor_detector import EditorDetector
        from gui_modules.telemetry_modifier import TelemetryModifier
        from gui_modules.backup_manager import BackupManager
        from gui_modules.log_manager import LogManager
        from gui_modules.database_manager import DatabaseManager
        from gui_modules.workspace_manager import WorkspaceManager
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n🔍 测试配置管理器...")
    
    try:
        from gui_modules.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试字段类别获取
        categories = config_manager.get_field_categories()
        print(f"✅ 字段类别数量: {len(categories)}")
        
        # 测试字段获取
        for category in categories:
            fields = config_manager.get_fields_by_category(category)
            print(f"  📁 {category}: {len(fields)} 个字段")
            
        return True
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_editor_detector():
    """测试编辑器检测器"""
    print("\n🔍 测试编辑器检测器...")
    
    try:
        from gui_modules.editor_detector import EditorDetector
        
        detector = EditorDetector()
        
        # 测试编辑器检测
        results = detector.detect_all_editors()
        print(f"✅ 检测到 {len(results)} 个编辑器配置")
        
        installed_count = 0
        running_count = 0
        
        for editor_id, info in results.items():
            status = info.get("status", "unknown")
            print(f"  📝 {info.get('name', editor_id)}: {status}")
            
            if info.get("installed", False):
                installed_count += 1
            if info.get("running", False):
                running_count += 1
                
        print(f"✅ 已安装: {installed_count} 个, 运行中: {running_count} 个")
        return True
    except Exception as e:
        print(f"❌ 编辑器检测器测试失败: {e}")
        return False

def test_database_manager():
    """测试数据库管理器"""
    print("\n🔍 测试数据库管理器...")
    
    try:
        from gui_modules.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 创建测试数据库
        test_db_path = "test_database.db"
        
        with sqlite3.connect(test_db_path) as conn:
            cursor = conn.cursor()
            
            # 创建测试表
            cursor.execute("""
                CREATE TABLE test_table (
                    id INTEGER PRIMARY KEY,
                    name TEXT,
                    data TEXT
                )
            """)
            
            # 插入测试数据
            cursor.execute("INSERT INTO test_table (name, data) VALUES (?, ?)", 
                         ("test1", "normal data"))
            cursor.execute("INSERT INTO test_table (name, data) VALUES (?, ?)", 
                         ("test2", "augment related data"))
            cursor.execute("INSERT INTO test_table (name, data) VALUES (?, ?)", 
                         ("test3", "telemetry info"))
            
            conn.commit()
        
        # 测试数据库分析
        db_info = db_manager._analyze_database(test_db_path)
        if db_info:
            print(f"✅ 数据库分析成功")
            print(f"  📊 总记录数: {db_info.get('total_records', 0)}")
            print(f"  🔍 Augment记录: {db_info.get('augment_records', 0)}")
            print(f"  📋 表数量: {len(db_info.get('tables', []))}")
        
        # 测试备份功能
        backup_path = db_manager.backup_database(test_db_path)
        if backup_path and os.path.exists(backup_path):
            print(f"✅ 数据库备份成功: {backup_path}")
            os.remove(backup_path)  # 清理备份文件
            
            # 清理info文件
            info_file = Path(backup_path).with_suffix('.info')
            if info_file.exists():
                info_file.unlink()
        
        # 测试清理功能
        result = db_manager.clean_augment_records(test_db_path)
        if result["success"]:
            print(f"✅ Augment记录清理成功: {result['deleted_records']} 条")
        
        # 清理测试文件
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            
        return True
    except Exception as e:
        print(f"❌ 数据库管理器测试失败: {e}")
        return False

def test_workspace_manager():
    """测试工作区管理器"""
    print("\n🔍 测试工作区管理器...")
    
    try:
        from gui_modules.workspace_manager import WorkspaceManager
        
        ws_manager = WorkspaceManager()
        
        # 测试工作区扫描
        workspaces = ws_manager.scan_workspaces()
        print(f"✅ 扫描到 {len(workspaces)} 个工作区")
        
        for ws in workspaces[:3]:  # 只显示前3个
            print(f"  💾 {ws.get('editor_name', 'Unknown')}: {ws.get('workspace_name', 'Unknown')}")
            
        # 测试摘要信息
        summary = ws_manager.get_workspace_summary()
        print(f"✅ 工作区摘要:")
        print(f"  📊 总数: {summary.get('total_workspaces', 0)}")
        print(f"  💾 总大小: {ws_manager.format_size(summary.get('total_size', 0))}")
        
        return True
    except Exception as e:
        print(f"❌ 工作区管理器测试失败: {e}")
        return False

def test_telemetry_modifier():
    """测试遥测修改器"""
    print("\n🔍 测试遥测修改器...")
    
    try:
        from gui_modules.telemetry_modifier import TelemetryModifier
        
        modifier = TelemetryModifier()
        
        # 测试ID生成
        machine_id = modifier.generate_machine_id()
        device_id = modifier.generate_device_id()
        
        print(f"✅ 机器ID生成: {machine_id[:16]}...")
        print(f"✅ 设备ID生成: {device_id}")
        
        # 测试字段获取
        quick_fields = modifier.get_default_fields_by_mode("quick")
        enhanced_fields = modifier.get_default_fields_by_mode("enhanced")
        
        print(f"✅ 快速模式字段: {len(quick_fields)} 个")
        print(f"✅ 增强模式字段: {len(enhanced_fields)} 个")
        
        # 测试操作验证
        errors, warnings = modifier.validate_operation("quick", ["vscode"])
        print(f"✅ 操作验证: {len(errors)} 个错误, {len(warnings)} 个警告")
        
        return True
    except Exception as e:
        print(f"❌ 遥测修改器测试失败: {e}")
        return False

def test_backup_manager():
    """测试备份管理器"""
    print("\n🔍 测试备份管理器...")
    
    try:
        from gui_modules.backup_manager import BackupManager
        
        backup_manager = BackupManager()
        
        # 创建测试文件
        test_file = "test_storage.json"
        test_data = {
            "telemetry.machineId": "test_machine_id",
            "telemetry.deviceId": "test_device_id"
        }
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2)
        
        # 测试备份创建
        backup_path = backup_manager.create_backup(test_file, "test_editor")
        if backup_path and os.path.exists(backup_path):
            print(f"✅ 备份创建成功: {backup_path}")
            
            # 测试备份列表
            backups = backup_manager.list_backups("test_editor")
            print(f"✅ 备份列表: {len(backups)} 个备份")
            
            # 测试备份信息
            backup_info = backup_manager.get_backup_info(backup_path)
            if backup_info:
                print(f"✅ 备份信息获取成功")
                print(f"  📊 文件大小: {backup_info.get('file_size', 0)} 字节")
                print(f"  🔒 完整性: {'正常' if backup_info.get('integrity_ok', False) else '异常'}")
            
            # 清理测试文件
            backup_manager.delete_backup(backup_path)
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
            
        return True
    except Exception as e:
        print(f"❌ 备份管理器测试失败: {e}")
        return False

def test_log_manager():
    """测试日志管理器"""
    print("\n🔍 测试日志管理器...")
    
    try:
        from gui_modules.log_manager import LogManager
        
        log_manager = LogManager()
        
        # 测试日志记录
        log_manager.info("测试信息日志")
        log_manager.success("测试成功日志")
        log_manager.warning("测试警告日志")
        log_manager.error("测试错误日志")
        
        print("✅ 日志记录功能正常")
        
        # 测试日志统计
        stats = log_manager.get_log_statistics()
        print(f"✅ 日志统计:")
        print(f"  📊 总行数: {stats.get('total_lines', 0)}")
        print(f"  💾 文件大小: {stats.get('file_size', 0)} 字节")
        
        # 测试日志文件列表
        log_files = log_manager.get_log_files()
        print(f"✅ 日志文件: {len(log_files)} 个")
        
        return True
    except Exception as e:
        print(f"❌ 日志管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 AUG 0.1 功能测试开始")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置管理器", test_config_manager),
        ("编辑器检测器", test_editor_detector),
        ("数据库管理器", test_database_manager),
        ("工作区管理器", test_workspace_manager),
        ("遥测修改器", test_telemetry_modifier),
        ("备份管理器", test_backup_manager),
        ("日志管理器", test_log_manager)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed} 个通过, {failed} 个失败")
    
    if failed == 0:
        print("🎉 所有功能测试通过！")
    else:
        print("⚠️ 部分功能需要修复")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
