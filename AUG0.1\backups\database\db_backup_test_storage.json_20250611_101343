{"telemetry.machineId": "test-machine-id-12345", "telemetry.devDeviceId": "test-device-id-67890", "telemetry.sessionId": "test-session-id-abcdef", "telemetry.sqmId": "test-sqm-id-123456", "telemetry.firstSessionDate": "2024-01-01T00:00:00.000Z", "telemetry.lastSessionDate": "2024-06-11T12:00:00.000Z", "telemetry.isNewAppInstall": true, "telemetry.optIn": true, "augment.userId": "test-augment-user-id", "augment.sessionData": "test-augment-session-data", "workspaceId": "test-workspace-id-789", "workspace.recentFiles": ["file1.txt", "file2.txt"], "cache.recentProjects": ["project1", "project2"], "cache.tempData": "temporary-cache-data", "normalSetting": "this-should-remain", "user.preferences": {"theme": "dark", "fontSize": 14, "telemetry.enabled": true, "augment.feature": "enabled"}, "extensions": {"installed": ["ext1", "ext2"], "telemetry.tracking": "enabled", "augment.extension": "test-data"}}