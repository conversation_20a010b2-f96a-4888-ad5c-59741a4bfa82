import tkinter as tk
from tkinter import ttk, messagebox
import os
import json
import uuid
import shutil
import glob
import time
from datetime import datetime

class AugmentLauncher:
    """精简版AUG启动器，用于替换原庞大的启动器.py。"""

    def __init__(self, root):
        self.root = root
        self.root.title("AUG 0.2 - Augment工具")
        self.root.geometry("900x600")
        self.root.minsize(800, 500)
        
        # 编辑器配置
        self.editor_paths = {
            "VSCode": r"%APPDATA%\Code\User\globalStorage\state.json",
            "VSCode Insiders": r"%APPDATA%\Code - Insiders\User\globalStorage\state.json",
            "Cursor": r"%APPDATA%\Cursor\User\globalStorage\state.json",
            "VSCodium": r"%APPDATA%\VSCodium\User\globalStorage\state.json",
            "Code - OSS": r"%APPDATA%\Code - OSS\User\globalStorage\state.json",
        }
        
        # 维护状态 - 移动到这里，确保在create_interface之前
        self.status_var = tk.StringVar(value="就绪")
        
        # 设置主题和样式
        self.setup_styles()
        
        # 创建界面框架
        self.create_interface()
        
    def setup_styles(self):
        """设置界面样式"""
        self.style = ttk.Style()
        try:
            self.style.theme_use("clam")  # 使用一个跨平台兼容的主题
        except tk.TclError:
            pass  # 忽略主题不可用错误
            
        # 配置基础颜色
        bg_color = "#f5f5f5"
        accent_color = "#2563eb"
        self.root.configure(bg=bg_color)
        
        # 配置按钮样式
        self.style.configure("TButton", font=("Microsoft YaHei UI", 10))
        self.style.configure("Accent.TButton", background=accent_color)
        
    def create_interface(self):
        """创建主界面"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(
            title_frame, 
            text="AUG - Augment ID 修改工具",
            font=("Microsoft YaHei UI", 16, "bold")
        )
        title_label.pack(side=tk.LEFT)
        
        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 修改标签页
        modify_tab = self.create_modify_tab(notebook)
        notebook.add(modify_tab, text="ID修改")
        
        # 数据库标签页
        database_tab = self.create_database_tab(notebook)
        notebook.add(database_tab, text="数据库")
        
        # 状态栏
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_modify_tab(self, parent):
        """创建ID修改标签页"""
        tab = ttk.Frame(parent, padding=10)
        
        # 编辑器选择区域
        editor_frame = ttk.LabelFrame(tab, text="编辑器选择", padding=10)
        editor_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 保存编辑器选择状态的变量
        self.editor_vars = {}
        
        # 添加编辑器选项
        for editor_name in self.editor_paths.keys():
            var = tk.BooleanVar(value=False)
            self.editor_vars[editor_name] = var
            ttk.Checkbutton(
                editor_frame, 
                text=editor_name,
                variable=var
            ).pack(anchor=tk.W, padx=5, pady=2)
            
        # 自动检测按钮
        ttk.Button(
            editor_frame,
            text="自动检测编辑器",
            command=self.detect_editors
        ).pack(anchor=tk.W, padx=5, pady=(10, 5))
        
        # 模式选择区域
        mode_frame = ttk.LabelFrame(tab, text="模式选择", padding=10)
        mode_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # 模式选择变量
        self.mode_var = tk.StringVar(value="standard")
        
        # 添加模式选项
        modes = {
            "standard": "标准模式（修改基本ID字段）",
            "enhanced": "增强模式（修改所有字段+删除遥测数据）",
            "quick": "快速模式（仅修改关键ID）"
        }
        
        for value, text in modes.items():
            ttk.Radiobutton(
                mode_frame,
                text=text,
                value=value,
                variable=self.mode_var
            ).pack(anchor=tk.W, padx=5, pady=2)
            
        # 操作按钮区域
        actions_frame = ttk.Frame(tab, padding=(0, 10))
        actions_frame.pack(fill=tk.X)
        
        ttk.Button(
            actions_frame,
            text="开始修改",
            command=self.start_modification,
            style="Accent.TButton"
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            actions_frame,
            text="帮助",
            command=self.show_help
        ).pack(side=tk.LEFT, padx=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(tab, text="操作日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        return tab
    
    def create_database_tab(self, parent):
        """创建数据库标签页"""
        tab = ttk.Frame(parent, padding=10)
        
        # 扫描按钮区域
        scan_frame = ttk.Frame(tab)
        scan_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(
            scan_frame,
            text="扫描数据库",
            command=self.scan_databases
        ).pack(side=tk.LEFT, padx=5)
        
        # 数据库列表
        tree_frame = ttk.Frame(tab)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ("路径", "编辑器", "大小")
        self.db_tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            height=10
        )
        
        # 设置列标题和宽度
        for col in columns:
            self.db_tree.heading(col, text=col)
            width = 300 if col == "路径" else 100
            self.db_tree.column(col, width=width)
            
        # 添加滚动条
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.db_tree.yview)
        self.db_tree.configure(yscrollcommand=scrollbar.set)
        
        self.db_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        return tab
    
    # --- 功能方法 ---
    def detect_editors(self):
        """检测已安装的编辑器"""
        self.add_log("正在检测编辑器...")
        found = 0
        
        for editor, path in self.editor_paths.items():
            expanded_path = os.path.expandvars(path)
            exists = os.path.exists(expanded_path)
            self.editor_vars[editor].set(exists)
            
            if exists:
                self.add_log(f"找到: {editor} ({expanded_path})")
                found += 1
            else:
                self.add_log(f"未找到: {editor}")
                
        self.add_log(f"检测完成，共找到 {found} 个编辑器")
        self.update_status(f"检测到 {found} 个编辑器")
    
    def scan_databases(self):
        """扫描数据库文件"""
        # 清空现有项目
        for item in self.db_tree.get_children():
            self.db_tree.delete(item)
            
        total = 0
        for editor, pattern in self.editor_paths.items():
            expanded = os.path.expandvars(pattern)
            # 使用 glob 支持通配符
            matches = glob.glob(expanded)
            
            for file_path in matches:
                if os.path.isfile(file_path):
                    size_str = self.format_size(os.path.getsize(file_path))
                    self.db_tree.insert("", tk.END, values=(file_path, editor, size_str))
                    total += 1
                    
        self.update_status(f"扫描完成，共找到 {total} 个数据库文件")
    
    def start_modification(self):
        """启动ID修改流程"""
        # 获取选择的编辑器
        selected = [name for name, var in self.editor_vars.items() if var.get()]
        if not selected:
            messagebox.showwarning("未选择编辑器", "请至少选择一个编辑器。")
            return
            
        # 获取选择的模式
        mode = self.mode_var.get()
        self.add_log(f"使用模式: {mode}")
        self.add_log(f"选择的编辑器: {', '.join(selected)}")
        
        # 开始处理
        success = []
        failed = []
        
        for editor in selected:
            self.add_log(f"处理编辑器: {editor}")
            file_path = os.path.expandvars(self.editor_paths[editor])
            
            if not os.path.isfile(file_path):
                failed.append((editor, "文件不存在"))
                self.add_log(f"错误: {file_path} 不存在")
                continue
                
            try:
                # 创建备份
                self.add_log(f"创建备份: {file_path}")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_dir = os.path.join(os.getcwd(), "backups")
                os.makedirs(backup_dir, exist_ok=True)
                
                backup_file = os.path.join(
                    backup_dir, 
                    f"{editor.replace(' ', '')}_state_{timestamp}.json"
                )
                shutil.copy2(file_path, backup_file)
                self.add_log(f"备份已保存: {backup_file}")
                
                # 读取JSON
                with open(file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    
                # 修改字段
                changed = self.modify_json(data, mode)
                
                if changed:
                    # 写回文件
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(data, f, ensure_ascii=False, indent=4)
                    success.append(editor)
                    self.add_log(f"成功修改: {editor}")
                else:
                    failed.append((editor, "未找到可修改字段"))
                    self.add_log(f"警告: {editor} 未找到可修改字段")
                    
            except Exception as e:
                failed.append((editor, str(e)))
                self.add_log(f"错误: {editor} 处理失败: {str(e)}")
                
        # 结果反馈
        if success:
            messagebox.showinfo("完成", f"已成功修改: {', '.join(success)}")
        if failed:
            errors = "\n".join(f"{e}: {reason}" for e, reason in failed)
            messagebox.showwarning("部分失败", f"以下编辑器处理失败:\n{errors}")
            
        self.update_status(f"完成修改: {len(success)}成功, {len(failed)}失败")
    
    def modify_json(self, data, mode):
        """根据模式修改JSON数据"""
        changed = False
        
        # 生成新ID
        new_id = str(uuid.uuid4())
        
        # 递归检索和修改
        changed |= self._recursive_modify(data, mode, new_id)
        
        return changed
        
    def _recursive_modify(self, obj, mode, new_id):
        """递归修改JSON对象"""
        changed = False
        
        # 核心字段列表
        core_fields = {
            "machineId", 
            "deviceId", 
            "sessionId",
            "telemetry.machineId",
            "telemetry.deviceId", 
            "telemetry.sessionId"
        }
        
        # 扩展字段列表 (增强模式)
        extended_fields = {
            "sqmId", 
            "telemetry.sqmId", 
            "telemetryId",
            "devDeviceId",
            "telemetry.devDeviceId"
        }
        
        if isinstance(obj, dict):
            # 处理字典
            for key in list(obj.keys()):
                # 检查当前键是否匹配
                if key in core_fields:
                    obj[key] = new_id
                    self.add_log(f"修改字段: {key}")
                    changed = True
                elif mode != "quick" and key in extended_fields:
                    obj[key] = new_id
                    self.add_log(f"修改扩展字段: {key}")
                    changed = True
                elif mode == "enhanced" and ("telemetry" in key.lower()):
                    del obj[key]
                    self.add_log(f"删除遥测字段: {key}")
                    changed = True
                elif isinstance(obj[key], (dict, list)):
                    # 递归处理嵌套结构
                    changed |= self._recursive_modify(obj[key], mode, new_id)
        elif isinstance(obj, list):
            # 处理列表
            for item in obj:
                if isinstance(item, (dict, list)):
                    changed |= self._recursive_modify(item, mode, new_id)
                    
        return changed
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """使用指南:

1. 选择要修改的编辑器 (可点击"自动检测编辑器")
2. 选择操作模式:
   - 标准模式: 修改基本ID字段
   - 增强模式: 修改所有字段并删除遥测数据
   - 快速模式: 仅修改核心ID字段
3. 点击"开始修改"按钮

数据库标签页可以扫描和查看存储文件。

注意: 操作前会自动创建备份。"""

        messagebox.showinfo("帮助", help_text)
    
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(message)
        
    @staticmethod
    def format_size(size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def main():
    try:
        root = tk.Tk()
        app = AugmentLauncher(root)
        root.mainloop()
    except Exception as e:
        # 在控制台输出错误
        print(f"程序发生错误: {e}")
        # 尝试显示错误对话框
        try:
            messagebox.showerror("程序错误", f"启动过程中发生错误:\n\n{str(e)}\n\n请联系开发者获取帮助。")
        except:
            pass
        raise


if __name__ == "__main__":
    main() 