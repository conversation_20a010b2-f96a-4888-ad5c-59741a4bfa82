{"version": "0.1.0", "description": "Enhanced Telemetry ID Configuration", "editors": {"vscode": {"name": "Visual Studio Code", "enabled": true, "paths": {"windows": "%APPDATA%/Code/User/globalStorage/storage.json", "macos": "~/Library/Application Support/Code/User/globalStorage/storage.json", "linux": "~/.config/Code/User/globalStorage/storage.json"}, "processes": ["code", "Code.exe"], "priority": 1}, "vscode-insiders": {"name": "Visual Studio Code Insiders", "enabled": true, "paths": {"windows": "%APPDATA%/Code - Insiders/User/globalStorage/storage.json", "macos": "~/Library/Application Support/Code - Insiders/User/globalStorage/storage.json", "linux": "~/.config/Code - Insiders/User/globalStorage/storage.json"}, "processes": ["code-insiders", "Code - Insiders.exe"], "priority": 2}, "cursor": {"name": "Cursor Editor", "enabled": true, "paths": {"windows": "%APPDATA%/Cursor/User/globalStorage/storage.json", "macos": "~/Library/Application Support/Cursor/User/globalStorage/storage.json", "linux": "~/.config/Cursor/User/globalStorage/storage.json"}, "processes": ["cursor", "Cursor.exe"], "priority": 3}, "vscodium": {"name": "VSCodium", "enabled": true, "paths": {"windows": "%APPDATA%/VSCodium/User/globalStorage/storage.json", "macos": "~/Library/Application Support/VSCodium/User/globalStorage/storage.json", "linux": "~/.config/VSCodium/User/globalStorage/storage.json"}, "processes": ["codium", "VSCodium.exe"], "priority": 4}, "code-oss": {"name": "Code - OSS", "enabled": true, "paths": {"windows": "%APPDATA%/Code - OSS/User/globalStorage/storage.json", "macos": "~/Library/Application Support/Code - OSS/User/globalStorage/storage.json", "linux": "~/.config/Code - OSS/User/globalStorage/storage.json"}, "processes": ["code-oss", "code-oss.exe"], "priority": 5}}, "telemetry_fields": {"core_identifiers": {"telemetry.machineId": {"type": "hex64", "description": "Machine unique identifier", "required": true, "category": "core", "privacy_level": "high"}, "telemetry.devDeviceId": {"type": "uuid4", "description": "Device identifier", "required": true, "category": "core", "privacy_level": "high"}, "telemetry.sessionId": {"type": "uuid4", "description": "Session identifier", "required": false, "category": "core", "privacy_level": "medium"}, "telemetry.sqmId": {"type": "uuid4", "description": "Software Quality Metrics ID", "required": false, "category": "core", "privacy_level": "medium"}}, "installation_tracking": {"telemetry.firstSessionDate": {"type": "timestamp", "description": "First session date", "required": false, "category": "installation", "privacy_level": "low"}, "telemetry.lastSessionDate": {"type": "timestamp", "description": "Last session date", "required": false, "category": "installation", "privacy_level": "low"}, "telemetry.isNewAppInstall": {"type": "boolean", "description": "New app install flag", "required": false, "category": "installation", "privacy_level": "low"}, "telemetry.optIn": {"type": "boolean", "description": "Telemetry opt-in status", "required": false, "category": "installation", "privacy_level": "high"}, "telemetry.instanceId": {"type": "uuid4", "description": "Application instance ID", "required": false, "category": "installation", "privacy_level": "medium"}, "telemetry.installationId": {"type": "uuid4", "description": "Installation unique ID", "required": false, "category": "installation", "privacy_level": "high"}}, "user_tracking": {"telemetry.userId": {"type": "uuid4", "description": "User identifier", "required": false, "category": "user", "privacy_level": "high"}, "telemetry.accountId": {"type": "uuid4", "description": "Account identifier", "required": false, "category": "user", "privacy_level": "high"}, "telemetry.profileId": {"type": "uuid4", "description": "User profile identifier", "required": false, "category": "user", "privacy_level": "high"}, "telemetry.workspaceId": {"type": "uuid4", "description": "Workspace identifier", "required": false, "category": "user", "privacy_level": "medium"}}, "hardware_system": {"telemetry.hardwareId": {"type": "hex64", "description": "Hardware fingerprint", "required": false, "category": "hardware", "privacy_level": "high"}, "telemetry.systemId": {"type": "hex64", "description": "System identifier", "required": false, "category": "hardware", "privacy_level": "high"}, "telemetry.platformId": {"type": "uuid4", "description": "Platform identifier", "required": false, "category": "hardware", "privacy_level": "medium"}, "telemetry.architectureId": {"type": "hex32", "description": "Architecture identifier", "required": false, "category": "hardware", "privacy_level": "low"}, "telemetry.cpuId": {"type": "hex64", "description": "CPU identifier", "required": false, "category": "hardware", "privacy_level": "high"}, "telemetry.memoryId": {"type": "hex32", "description": "Memory configuration ID", "required": false, "category": "hardware", "privacy_level": "medium"}}, "network_location": {"telemetry.networkId": {"type": "uuid4", "description": "Network identifier", "required": false, "category": "network", "privacy_level": "high"}, "telemetry.locationId": {"type": "uuid4", "description": "Location identifier", "required": false, "category": "network", "privacy_level": "high"}, "telemetry.timezoneId": {"type": "uuid4", "description": "Timezone identifier", "required": false, "category": "network", "privacy_level": "medium"}, "telemetry.localeId": {"type": "uuid4", "description": "Locale identifier", "required": false, "category": "network", "privacy_level": "medium"}, "telemetry.ipId": {"type": "hex32", "description": "IP address hash identifier", "required": false, "category": "network", "privacy_level": "high"}, "telemetry.dnsId": {"type": "hex32", "description": "DNS configuration identifier", "required": false, "category": "network", "privacy_level": "medium"}}, "usage_behavior": {"telemetry.usageId": {"type": "uuid4", "description": "Usage pattern identifier", "required": false, "category": "usage", "privacy_level": "medium"}, "telemetry.behaviorId": {"type": "uuid4", "description": "Behavior pattern identifier", "required": false, "category": "usage", "privacy_level": "medium"}, "telemetry.workflowId": {"type": "uuid4", "description": "Workflow identifier", "required": false, "category": "usage", "privacy_level": "medium"}, "telemetry.projectId": {"type": "uuid4", "description": "Project identifier", "required": false, "category": "usage", "privacy_level": "medium"}, "telemetry.activityId": {"type": "uuid4", "description": "Activity tracking identifier", "required": false, "category": "usage", "privacy_level": "medium"}, "telemetry.interactionId": {"type": "uuid4", "description": "User interaction identifier", "required": false, "category": "usage", "privacy_level": "medium"}}, "performance_diagnostics": {"telemetry.performanceId": {"type": "uuid4", "description": "Performance tracking ID", "required": false, "category": "performance", "privacy_level": "low"}, "telemetry.diagnosticId": {"type": "uuid4", "description": "Diagnostic identifier", "required": false, "category": "performance", "privacy_level": "low"}, "telemetry.crashId": {"type": "uuid4", "description": "Crash report identifier", "required": false, "category": "performance", "privacy_level": "medium"}, "telemetry.errorId": {"type": "uuid4", "description": "Error tracking identifier", "required": false, "category": "performance", "privacy_level": "medium"}, "telemetry.memoryId": {"type": "hex32", "description": "Memory usage identifier", "required": false, "category": "performance", "privacy_level": "low"}, "telemetry.startupId": {"type": "uuid4", "description": "Startup performance identifier", "required": false, "category": "performance", "privacy_level": "low"}}}, "id_generation": {"hex64": {"length": 64, "format": "hexadecimal", "entropy_sources": ["crypto", "timestamp", "random"]}, "hex32": {"length": 32, "format": "hexadecimal", "entropy_sources": ["crypto", "random"]}, "hex16": {"length": 16, "format": "hexadecimal", "entropy_sources": ["random"]}, "hex8": {"length": 8, "format": "hexadecimal", "entropy_sources": ["random"]}, "uuid4": {"format": "uuid_v4", "entropy_sources": ["crypto", "timestamp"]}, "timestamp": {"format": "iso8601", "randomize_range": "1_year"}, "boolean": {"format": "boolean", "distribution": "random"}}, "security": {"create_backups": true, "backup_encryption": false, "verify_integrity": true, "secure_delete": false, "log_operations": true, "require_confirmation": false}, "privacy_levels": {"high": {"description": "Highly identifying information", "default_action": "randomize", "requires_explicit_consent": true}, "medium": {"description": "Moderately identifying information", "default_action": "randomize", "requires_explicit_consent": false}, "low": {"description": "Low identifying information", "default_action": "optional", "requires_explicit_consent": false}}}