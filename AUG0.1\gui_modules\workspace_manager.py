#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作区管理器模块
管理编辑器工作区存储的扫描、清理和备份
"""

import os
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import glob

class WorkspaceManager:
    """工作区管理器类"""
    
    def __init__(self):
        """初始化工作区管理器"""
        self.backup_dir = Path("backups/workspace")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 编辑器工作区路径配置
        self.editor_configs = {
            "vscode": {
                "name": "Visual Studio Code",
                "workspace_storage": "%APPDATA%\\Code\\User\\workspaceStorage",
                "global_storage": "%APPDATA%\\Code\\User\\globalStorage",
                "recent_files": "%APPDATA%\\Code\\User\\globalStorage\\storage.json"
            },
            "cursor": {
                "name": "Cursor Editor",
                "workspace_storage": "%APPDATA%\\Cursor\\User\\workspaceStorage",
                "global_storage": "%APPDATA%\\Cursor\\User\\globalStorage",
                "recent_files": "%APPDATA%\\Cursor\\User\\globalStorage\\storage.json"
            },
            "vscodium": {
                "name": "VSCodium",
                "workspace_storage": "%APPDATA%\\VSCodium\\User\\workspaceStorage",
                "global_storage": "%APPDATA%\\VSCodium\\User\\globalStorage",
                "recent_files": "%APPDATA%\\VSCodium\\User\\globalStorage\\storage.json"
            },
            "vscode_insiders": {
                "name": "VS Code Insiders",
                "workspace_storage": "%APPDATA%\\Code - Insiders\\User\\workspaceStorage",
                "global_storage": "%APPDATA%\\Code - Insiders\\User\\globalStorage",
                "recent_files": "%APPDATA%\\Code - Insiders\\User\\globalStorage\\storage.json"
            },
            "code_oss": {
                "name": "Code - OSS",
                "workspace_storage": "%APPDATA%\\Code - OSS\\User\\workspaceStorage",
                "global_storage": "%APPDATA%\\Code - OSS\\User\\globalStorage",
                "recent_files": "%APPDATA%\\Code - OSS\\User\\globalStorage\\storage.json"
            }
        }
        
    def scan_workspaces(self) -> List[Dict]:
        """扫描所有编辑器的工作区"""
        workspaces = []
        
        for editor_id, config in self.editor_configs.items():
            editor_workspaces = self._scan_editor_workspaces(editor_id, config)
            workspaces.extend(editor_workspaces)
            
        return workspaces
        
    def _scan_editor_workspaces(self, editor_id: str, config: Dict) -> List[Dict]:
        """扫描单个编辑器的工作区"""
        workspaces = []
        
        # 扫描工作区存储目录
        workspace_storage_path = os.path.expandvars(config["workspace_storage"])
        if os.path.exists(workspace_storage_path):
            workspaces.extend(self._scan_workspace_storage(editor_id, config["name"], workspace_storage_path))
            
        # 扫描全局存储
        global_storage_path = os.path.expandvars(config["global_storage"])
        if os.path.exists(global_storage_path):
            global_info = self._analyze_global_storage(editor_id, config["name"], global_storage_path)
            if global_info:
                workspaces.append(global_info)
                
        return workspaces
        
    def _scan_workspace_storage(self, editor_id: str, editor_name: str, storage_path: str) -> List[Dict]:
        """扫描工作区存储目录"""
        workspaces = []
        
        try:
            for workspace_dir in os.listdir(storage_path):
                workspace_path = os.path.join(storage_path, workspace_dir)
                
                if os.path.isdir(workspace_path):
                    workspace_info = self._analyze_workspace_directory(
                        editor_id, editor_name, workspace_path, workspace_dir
                    )
                    if workspace_info:
                        workspaces.append(workspace_info)
                        
        except Exception as e:
            print(f"扫描工作区存储失败 {storage_path}: {e}")
            
        return workspaces
        
    def _analyze_workspace_directory(self, editor_id: str, editor_name: str, 
                                   workspace_path: str, workspace_id: str) -> Optional[Dict]:
        """分析工作区目录"""
        try:
            # 获取目录信息
            total_size = self._get_directory_size(workspace_path)
            file_count = self._count_files_in_directory(workspace_path)
            last_accessed = self._get_last_access_time(workspace_path)
            
            # 尝试获取工作区名称
            workspace_name = self._get_workspace_name(workspace_path)
            
            return {
                "editor_id": editor_id,
                "editor_name": editor_name,
                "workspace_id": workspace_id,
                "workspace_name": workspace_name,
                "workspace_path": workspace_path,
                "type": "workspace_storage",
                "size": total_size,
                "file_count": file_count,
                "last_accessed": last_accessed
            }
            
        except Exception as e:
            print(f"分析工作区目录失败 {workspace_path}: {e}")
            return None
            
    def _analyze_global_storage(self, editor_id: str, editor_name: str, storage_path: str) -> Optional[Dict]:
        """分析全局存储"""
        try:
            total_size = self._get_directory_size(storage_path)
            file_count = self._count_files_in_directory(storage_path)
            last_accessed = self._get_last_access_time(storage_path)
            
            return {
                "editor_id": editor_id,
                "editor_name": editor_name,
                "workspace_id": "global_storage",
                "workspace_name": "全局存储",
                "workspace_path": storage_path,
                "type": "global_storage",
                "size": total_size,
                "file_count": file_count,
                "last_accessed": last_accessed
            }
            
        except Exception as e:
            print(f"分析全局存储失败 {storage_path}: {e}")
            return None
            
    def _get_workspace_name(self, workspace_path: str) -> str:
        """获取工作区名称"""
        try:
            # 查找workspace.json文件
            workspace_json = os.path.join(workspace_path, "workspace.json")
            if os.path.exists(workspace_json):
                with open(workspace_json, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if 'folder' in data:
                        return os.path.basename(data['folder'])
                        
            # 如果没有找到，使用目录名
            return os.path.basename(workspace_path)
            
        except:
            return "未知工作区"
            
    def _get_directory_size(self, directory: str) -> int:
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except:
                        pass
        except:
            pass
        return total_size
        
    def _count_files_in_directory(self, directory: str) -> int:
        """统计目录中的文件数量"""
        file_count = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                file_count += len(filenames)
        except:
            pass
        return file_count
        
    def _get_last_access_time(self, path: str) -> datetime:
        """获取最后访问时间"""
        try:
            stat = os.stat(path)
            return datetime.fromtimestamp(stat.st_atime)
        except:
            return datetime.min
            
    def backup_workspace(self, workspace_info: Dict) -> Optional[str]:
        """备份工作区"""
        try:
            workspace_path = workspace_info["workspace_path"]
            editor_name = workspace_info["editor_name"]
            workspace_id = workspace_info["workspace_id"]
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{editor_name}_{workspace_id}_{timestamp}"
            backup_path = self.backup_dir / backup_filename
            
            if os.path.isdir(workspace_path):
                shutil.copytree(workspace_path, backup_path)
            else:
                shutil.copy2(workspace_path, backup_path)
                
            # 创建备份信息文件
            info_file = backup_path.with_suffix('.info')
            backup_info = {
                "original_path": workspace_path,
                "editor_name": editor_name,
                "workspace_id": workspace_id,
                "backup_time": timestamp,
                "backup_size": self._get_directory_size(str(backup_path))
            }
            
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
                
            return str(backup_path)
            
        except Exception as e:
            print(f"备份工作区失败: {e}")
            return None
            
    def clean_recent_files(self, editor_id: str) -> Dict:
        """清理最近文件记录"""
        result = {"success": False, "cleaned_items": 0, "error": None}
        
        try:
            config = self.editor_configs.get(editor_id)
            if not config:
                result["error"] = f"未知的编辑器: {editor_id}"
                return result
                
            recent_files_path = os.path.expandvars(config["recent_files"])
            
            if os.path.exists(recent_files_path):
                with open(recent_files_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 清理最近文件相关的键
                recent_keys = [
                    'openedPathsList', 'recentlyOpenedPathsList', 'history.recentlyOpenedPathsList',
                    'workbench.action.files.newUntitledFile', 'workbench.action.quickOpen'
                ]
                
                cleaned_count = 0
                for key in recent_keys:
                    if key in data:
                        del data[key]
                        cleaned_count += 1
                        
                # 保存修改后的文件
                with open(recent_files_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                    
                result["success"] = True
                result["cleaned_items"] = cleaned_count
                
        except Exception as e:
            result["error"] = str(e)
            
        return result
        
    def clean_workspace_storage(self, workspace_info: Dict) -> bool:
        """清理工作区存储"""
        try:
            workspace_path = workspace_info["workspace_path"]
            
            if os.path.exists(workspace_path):
                if os.path.isdir(workspace_path):
                    shutil.rmtree(workspace_path)
                else:
                    os.remove(workspace_path)
                return True
                
        except Exception as e:
            print(f"清理工作区失败: {e}")
            
        return False
        
    def get_workspace_summary(self) -> Dict:
        """获取工作区摘要信息"""
        workspaces = self.scan_workspaces()
        
        summary = {
            "total_workspaces": len(workspaces),
            "total_size": sum(ws.get("size", 0) for ws in workspaces),
            "editors": {},
            "oldest_workspace": None,
            "newest_workspace": None
        }
        
        if workspaces:
            # 按编辑器分组统计
            for workspace in workspaces:
                editor = workspace.get("editor_name", "Unknown")
                if editor not in summary["editors"]:
                    summary["editors"][editor] = {"count": 0, "size": 0}
                summary["editors"][editor]["count"] += 1
                summary["editors"][editor]["size"] += workspace.get("size", 0)
                
            # 找到最新和最旧的工作区
            workspaces_with_time = [ws for ws in workspaces if ws.get("last_accessed")]
            if workspaces_with_time:
                summary["oldest_workspace"] = min(workspaces_with_time, 
                                                key=lambda x: x["last_accessed"])
                summary["newest_workspace"] = max(workspaces_with_time, 
                                                key=lambda x: x["last_accessed"])
                
        return summary
        
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"

    def backup_workspace(self, workspace_path: str) -> Optional[str]:
        """备份单个工作区"""
        try:
            if not os.path.exists(workspace_path):
                return None

            workspace_name = os.path.basename(workspace_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"workspace_{workspace_name}_{timestamp}.backup"
            backup_path = self.backup_dir / backup_filename

            # 创建备份
            if os.path.isfile(workspace_path):
                shutil.copy2(workspace_path, backup_path)
            else:
                shutil.copytree(workspace_path, backup_path, dirs_exist_ok=True)

            # 创建备份信息文件
            info_file = backup_path.with_suffix('.info')
            backup_info = {
                "original_path": workspace_path,
                "backup_time": timestamp,
                "backup_size": self._get_path_size(str(backup_path))
            }

            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)

            return str(backup_path)

        except Exception as e:
            print(f"备份工作区失败: {e}")
            return None

    def clean_workspace_data(self, workspace_path: str) -> Dict:
        """清理工作区数据"""
        result = {
            "success": False,
            "cleaned_files": 0,
            "cleaned_size": 0,
            "errors": []
        }

        try:
            if not os.path.exists(workspace_path):
                result["errors"].append(f"工作区路径不存在: {workspace_path}")
                return result

            cleaned_files = 0
            cleaned_size = 0

            # 清理工作区中的特定文件
            cleanup_patterns = [
                "state.json",           # 状态文件
                "storage.json",         # 存储文件
                "settings.json",        # 设置文件
                "*.log",               # 日志文件
                "*.tmp",               # 临时文件
                "cache/*",             # 缓存文件
                "logs/*",              # 日志目录
                "temp/*"               # 临时目录
            ]

            if os.path.isdir(workspace_path):
                # 遍历工作区目录
                for root, dirs, files in os.walk(workspace_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        file_name = file.lower()

                        # 检查是否需要清理
                        should_clean = False

                        # 检查文件名模式
                        if any(pattern.replace("*", "") in file_name for pattern in cleanup_patterns if "*" not in pattern):
                            should_clean = True

                        # 检查扩展名
                        if file_name.endswith(('.log', '.tmp', '.cache')):
                            should_clean = True

                        # 检查是否包含augment
                        if 'augment' in file_name:
                            should_clean = True

                        if should_clean:
                            try:
                                file_size = os.path.getsize(file_path)
                                os.remove(file_path)
                                cleaned_files += 1
                                cleaned_size += file_size
                            except Exception as e:
                                result["errors"].append(f"删除文件失败 {file_path}: {e}")

                # 清理空目录
                for root, dirs, files in os.walk(workspace_path, topdown=False):
                    for dir_name in dirs:
                        dir_path = os.path.join(root, dir_name)
                        try:
                            if not os.listdir(dir_path):  # 空目录
                                os.rmdir(dir_path)
                        except:
                            pass

            result["success"] = True
            result["cleaned_files"] = cleaned_files
            result["cleaned_size"] = cleaned_size

        except Exception as e:
            result["errors"].append(f"工作区清理失败: {e}")

        return result

    def _get_path_size(self, path: str) -> int:
        """获取路径大小"""
        total_size = 0
        try:
            if os.path.isfile(path):
                return os.path.getsize(path)
            elif os.path.isdir(path):
                for dirpath, dirnames, filenames in os.walk(path):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(filepath)
                        except:
                            continue
        except:
            pass
        return total_size
