#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编辑器检测器模块
检测系统中安装的代码编辑器
"""

import os
import psutil
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import subprocess

class EditorDetector:
    """编辑器检测器类"""
    
    def __init__(self):
        """初始化编辑器检测器"""
        self.editor_configs = {
            "vscode": {
                "name": "Visual Studio Code",
                "display_name": "VS Code",
                "paths": {
                    "windows": os.path.expandvars("%APPDATA%\\Code\\User\\globalStorage\\storage.json"),
                    "macos": "~/Library/Application Support/Code/User/globalStorage/storage.json",
                    "linux": "~/.config/Code/User/globalStorage/storage.json"
                },
                "processes": ["Code.exe", "code"],
                "install_paths": [
                    "%LOCALAPPDATA%\\Programs\\Microsoft VS Code\\Code.exe",
                    "%PROGRAMFILES%\\Microsoft VS Code\\Code.exe",
                    "%PROGRAMFILES(X86)%\\Microsoft VS Code\\Code.exe"
                ],
                "icon": "🔵"
            },
            "vscode_insiders": {
                "name": "Visual Studio Code Insiders",
                "display_name": "VS Code Insiders",
                "paths": {
                    "windows": os.path.expandvars("%APPDATA%\\Code - Insiders\\User\\globalStorage\\storage.json"),
                    "macos": "~/Library/Application Support/Code - Insiders/User/globalStorage/storage.json",
                    "linux": "~/.config/Code - Insiders/User/globalStorage/storage.json"
                },
                "processes": ["Code - Insiders.exe", "code-insiders"],
                "install_paths": [
                    "%LOCALAPPDATA%\\Programs\\Microsoft VS Code Insiders\\Code - Insiders.exe",
                    "%PROGRAMFILES%\\Microsoft VS Code Insiders\\Code - Insiders.exe"
                ],
                "icon": "🟠"
            },
            "cursor": {
                "name": "Cursor Editor",
                "display_name": "Cursor",
                "paths": {
                    "windows": os.path.expandvars("%APPDATA%\\Cursor\\User\\globalStorage\\storage.json"),
                    "macos": "~/Library/Application Support/Cursor/User/globalStorage/storage.json",
                    "linux": "~/.config/Cursor/User/globalStorage/storage.json"
                },
                "processes": ["Cursor.exe", "cursor"],
                "install_paths": [
                    "%LOCALAPPDATA%\\Programs\\Cursor\\Cursor.exe",
                    "%PROGRAMFILES%\\Cursor\\Cursor.exe"
                ],
                "icon": "🟣"
            },
            "vscodium": {
                "name": "VSCodium",
                "display_name": "VSCodium",
                "paths": {
                    "windows": os.path.expandvars("%APPDATA%\\VSCodium\\User\\globalStorage\\storage.json"),
                    "macos": "~/Library/Application Support/VSCodium/User/globalStorage/storage.json",
                    "linux": "~/.config/VSCodium/User/globalStorage/storage.json"
                },
                "processes": ["VSCodium.exe", "codium"],
                "install_paths": [
                    "%LOCALAPPDATA%\\Programs\\VSCodium\\VSCodium.exe",
                    "%PROGRAMFILES%\\VSCodium\\VSCodium.exe"
                ],
                "icon": "🟢"
            },
            "code_oss": {
                "name": "Code - OSS",
                "display_name": "Code-OSS",
                "paths": {
                    "windows": os.path.expandvars("%APPDATA%\\Code - OSS\\User\\globalStorage\\storage.json"),
                    "macos": "~/Library/Application Support/Code - OSS/User/globalStorage/storage.json",
                    "linux": "~/.config/Code - OSS/User/globalStorage/storage.json"
                },
                "processes": ["code-oss.exe", "code-oss"],
                "install_paths": [
                    "%LOCALAPPDATA%\\Programs\\Code - OSS\\code-oss.exe",
                    "%PROGRAMFILES%\\Code - OSS\\code-oss.exe"
                ],
                "icon": "🔴"
            }
        }
        
    def detect_all_editors(self) -> Dict[str, Dict]:
        """检测所有编辑器"""
        results = {}
        
        for editor_id, config in self.editor_configs.items():
            editor_info = self.detect_editor(editor_id)
            results[editor_id] = editor_info
            
        return results
        
    def detect_editor(self, editor_id: str) -> Dict:
        """检测单个编辑器"""
        if editor_id not in self.editor_configs:
            return {"status": "unknown", "error": "Unknown editor"}
            
        config = self.editor_configs[editor_id]
        result = {
            "id": editor_id,
            "name": config["name"],
            "display_name": config["display_name"],
            "icon": config["icon"],
            "status": "not_found",
            "installed": False,
            "running": False,
            "storage_path": "",
            "storage_exists": False,
            "storage_size": 0,
            "last_modified": None,
            "install_path": "",
            "processes": [],
            "telemetry_fields": 0
        }
        
        # 检查存储文件
        storage_path = self.get_storage_path(editor_id)
        if storage_path and os.path.exists(storage_path):
            result["storage_exists"] = True
            result["storage_path"] = storage_path
            result["installed"] = True
            result["status"] = "installed"
            
            # 获取文件信息
            try:
                stat = os.stat(storage_path)
                result["storage_size"] = stat.st_size
                result["last_modified"] = stat.st_mtime
                
                # 分析遥测字段
                result["telemetry_fields"] = self.count_telemetry_fields(storage_path)
            except Exception as e:
                result["error"] = str(e)
                
        # 检查安装路径
        install_path = self.find_install_path(editor_id)
        if install_path:
            result["install_path"] = install_path
            result["installed"] = True
            if result["status"] == "not_found":
                result["status"] = "installed"
                
        # 检查运行状态
        running_processes = self.check_running_processes(editor_id)
        if running_processes:
            result["running"] = True
            result["processes"] = running_processes
            result["status"] = "running"
            
        return result
        
    def get_storage_path(self, editor_id: str) -> Optional[str]:
        """获取编辑器存储文件路径"""
        if editor_id not in self.editor_configs:
            return None
            
        config = self.editor_configs[editor_id]
        
        # 根据操作系统选择路径
        import platform
        system = platform.system().lower()
        
        if system == "windows":
            return config["paths"].get("windows")
        elif system == "darwin":
            return os.path.expanduser(config["paths"].get("macos", ""))
        else:
            return os.path.expanduser(config["paths"].get("linux", ""))
            
    def find_install_path(self, editor_id: str) -> Optional[str]:
        """查找编辑器安装路径"""
        if editor_id not in self.editor_configs:
            return None
            
        config = self.editor_configs[editor_id]
        
        for path_template in config.get("install_paths", []):
            path = os.path.expandvars(path_template)
            if os.path.exists(path):
                return path
                
        return None
        
    def check_running_processes(self, editor_id: str) -> List[Dict]:
        """检查编辑器运行进程"""
        if editor_id not in self.editor_configs:
            return []
            
        config = self.editor_configs[editor_id]
        running_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe', 'create_time']):
                try:
                    proc_name = proc.info['name']
                    if proc_name in config["processes"]:
                        running_processes.append({
                            "pid": proc.info['pid'],
                            "name": proc_name,
                            "exe": proc.info['exe'],
                            "create_time": proc.info['create_time']
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"检查进程时出错: {e}")
            
        return running_processes
        
    def count_telemetry_fields(self, storage_path: str) -> int:
        """统计遥测字段数量"""
        try:
            with open(storage_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            count = 0
            if isinstance(data, dict):
                for key in data.keys():
                    if key.startswith('telemetry.'):
                        count += 1
                        
            return count
        except Exception:
            return 0
            
    def get_editor_info(self, editor_id: str) -> Dict:
        """获取编辑器基本信息"""
        if editor_id not in self.editor_configs:
            return {}
            
        config = self.editor_configs[editor_id].copy()
        return config
        
    def get_all_editor_ids(self) -> List[str]:
        """获取所有编辑器ID列表"""
        return list(self.editor_configs.keys())
        
    def get_installed_editors(self) -> List[str]:
        """获取已安装的编辑器ID列表"""
        installed = []
        detection_results = self.detect_all_editors()
        
        for editor_id, info in detection_results.items():
            if info.get("installed", False):
                installed.append(editor_id)
                
        return installed
        
    def get_running_editors(self) -> List[str]:
        """获取正在运行的编辑器ID列表"""
        running = []
        detection_results = self.detect_all_editors()
        
        for editor_id, info in detection_results.items():
            if info.get("running", False):
                running.append(editor_id)
                
        return running
        
    def terminate_editor_processes(self, editor_id: str) -> bool:
        """终止编辑器进程"""
        try:
            processes = self.check_running_processes(editor_id)
            for proc_info in processes:
                try:
                    proc = psutil.Process(proc_info["pid"])
                    proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return True
        except Exception as e:
            print(f"终止进程时出错: {e}")
            return False
            
    def get_editor_summary(self) -> Dict:
        """获取编辑器检测摘要"""
        detection_results = self.detect_all_editors()
        
        summary = {
            "total_editors": len(self.editor_configs),
            "installed_count": 0,
            "running_count": 0,
            "with_storage_count": 0,
            "total_telemetry_fields": 0
        }
        
        for editor_id, info in detection_results.items():
            if info.get("installed", False):
                summary["installed_count"] += 1
            if info.get("running", False):
                summary["running_count"] += 1
            if info.get("storage_exists", False):
                summary["with_storage_count"] += 1
            summary["total_telemetry_fields"] += info.get("telemetry_fields", 0)
            
        return summary
