@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: AUG 0.1 - Enhanced Telemetry ID Modifier
:: One-Click Enhanced Interface

title AUG 0.1 - Enhanced Telemetry ID Modifier

:MAIN_MENU
cls
echo.
echo ================================================================
echo                    AUG 0.1 Enhanced Version
echo                 Enhanced Telemetry ID Modifier
echo                        v0.1.0
echo ================================================================
echo.
echo --------------------------------------------------------
echo                    Main Functions
echo --------------------------------------------------------
echo.
echo  [1] Enhanced Mode - Modify All Telemetry IDs (Recommended)
echo  [2] Standard Mode - Modify Basic Telemetry IDs
echo  [3] Quick Mode - Modify Core IDs Only
echo  [4] Custom Mode - Select Specific Editors and Fields
echo.
echo --------------------------------------------------------
echo                   Management Functions
echo --------------------------------------------------------
echo.
echo  [5] View Supported Editors
echo  [6] Show Telemetry Field Information
echo  [7] View Backup Files
echo  [8] Restore from Backup
echo  [9] Clean Old Backups
echo.
echo --------------------------------------------------------
echo                     Other Options
echo --------------------------------------------------------
echo.
echo  [H] Help Information
echo  [Q] Exit Program
echo.
echo ================================================================
set /p choice="Please select operation (1-9, H, Q): "

if /i "%choice%"=="1" goto ENHANCED_MODE
if /i "%choice%"=="2" goto STANDARD_MODE
if /i "%choice%"=="3" goto QUICK_MODE
if /i "%choice%"=="4" goto CUSTOM_MODE
if /i "%choice%"=="5" goto SHOW_EDITORS
if /i "%choice%"=="6" goto SHOW_FIELDS
if /i "%choice%"=="7" goto LIST_BACKUPS
if /i "%choice%"=="8" goto RESTORE_BACKUP
if /i "%choice%"=="9" goto CLEAN_BACKUPS
if /i "%choice%"=="H" goto SHOW_HELP
if /i "%choice%"=="Q" goto EXIT_PROGRAM

echo.
echo Invalid selection, please try again...
timeout /t 2 >nul
goto MAIN_MENU

:ENHANCED_MODE
cls
echo.
echo ================================================================
echo                      Enhanced Mode
echo ================================================================
echo.
echo Enhanced mode will modify all telemetry ID fields in detected editors
echo Including: VS Code, Cursor, VSCodium, Code-OSS, etc.
echo.
echo WARNING: This operation will modify many telemetry fields
echo Please close all editors before proceeding
echo.
set /p confirm="Confirm Enhanced Mode execution? (Y/N): "
if /i not "%confirm%"=="Y" goto MAIN_MENU

echo.
echo Executing Enhanced Mode...
powershell -ExecutionPolicy Bypass -File ".\scripts\enhanced_id_modifier.ps1" -Enhanced -All
echo.
echo Enhanced Mode execution completed!
pause
goto MAIN_MENU

:STANDARD_MODE
cls
echo.
echo ================================================================
echo                      Standard Mode
echo ================================================================
echo.
echo Standard mode will modify basic telemetry ID fields
echo Including: machineId, deviceId, sessionId, instanceId, etc.
echo.
set /p confirm="Confirm Standard Mode execution? (Y/N): "
if /i not "%confirm%"=="Y" goto MAIN_MENU

echo.
echo Executing Standard Mode...
powershell -ExecutionPolicy Bypass -File ".\scripts\enhanced_id_modifier.ps1" -All -Fields "machineId,deviceId,sessionId,instanceId"
echo.
echo Standard Mode execution completed!
pause
goto MAIN_MENU

:QUICK_MODE
cls
echo.
echo ================================================================
echo                      Quick Mode
echo ================================================================
echo.
echo Quick mode only modifies core telemetry IDs (machineId and deviceId)
echo This is the most basic privacy protection operation
echo.
set /p confirm="Confirm Quick Mode execution? (Y/N): "
if /i not "%confirm%"=="Y" goto MAIN_MENU

echo.
echo Executing Quick Mode...
powershell -ExecutionPolicy Bypass -File ".\scripts\enhanced_id_modifier.ps1" -All -Fields "machineId,deviceId"
echo.
echo Quick Mode execution completed!
pause
goto MAIN_MENU

:CUSTOM_MODE
cls
echo.
echo ================================================================
echo                      Custom Mode
echo ================================================================
echo.
echo Custom mode allows you to select specific editors and fields
echo.
echo Supported Editors:
echo  - VSCode (Visual Studio Code)
echo  - Cursor (Cursor Editor)
echo  - VSCodium (Open Source Version)
echo  - CodeOSS (Code - OSS)
echo.
set /p editors="Enter editor list (comma separated, e.g: VSCode,Cursor): "
if "%editors%"=="" goto MAIN_MENU

echo.
echo Common Field Combinations:
echo  [1] Basic: machineId,deviceId
echo  [2] Standard: machineId,deviceId,sessionId,instanceId
echo  [3] Extended: machineId,deviceId,sessionId,instanceId,userId,hardwareId
echo  [4] Custom Input
echo.
set /p field_choice="Select field combination (1-4): "

set fields=
if "%field_choice%"=="1" set fields=machineId,deviceId
if "%field_choice%"=="2" set fields=machineId,deviceId,sessionId,instanceId
if "%field_choice%"=="3" set fields=machineId,deviceId,sessionId,instanceId,userId,hardwareId
if "%field_choice%"=="4" (
    set /p fields="Enter field list (comma separated): "
)

if "%fields%"=="" goto MAIN_MENU

echo.
echo Operation to be executed:
echo  Editors: %editors%
echo  Fields: %fields%
echo.
set /p confirm="Confirm Custom Mode execution? (Y/N): "
if /i not "%confirm%"=="Y" goto MAIN_MENU

echo.
echo Executing Custom Mode...
powershell -ExecutionPolicy Bypass -File ".\scripts\enhanced_id_modifier.ps1" -Editor "%editors%" -Fields "%fields%"
echo.
echo Custom Mode execution completed!
pause
goto MAIN_MENU

:SHOW_EDITORS
cls
echo.
echo ================================================================
echo                    Supported Editors
echo ================================================================
echo.
echo Detecting installed editors...
echo.
powershell -ExecutionPolicy Bypass -Command "& { Write-Host 'Detection Results:' -ForegroundColor Green; $configs = @{ 'VSCode' = '$env:APPDATA\Code\User\globalStorage\storage.json'; 'VSCodeInsiders' = '$env:APPDATA\Code - Insiders\User\globalStorage\storage.json'; 'Cursor' = '$env:APPDATA\Cursor\User\globalStorage\storage.json'; 'VSCodium' = '$env:APPDATA\VSCodium\User\globalStorage\storage.json'; 'CodeOSS' = '$env:APPDATA\Code - OSS\User\globalStorage\storage.json' }; foreach($editor in $configs.Keys) { if(Test-Path $configs[$editor]) { Write-Host \"[FOUND] $editor - Installed\" -ForegroundColor Green } else { Write-Host \"[NOT FOUND] $editor - Not Installed\" -ForegroundColor Red } } }"
echo.
pause
goto MAIN_MENU

:SHOW_FIELDS
cls
echo.
echo ================================================================
echo                    Telemetry Field Information
echo ================================================================
echo.
echo Displaying detailed telemetry field information...
echo.
powershell -ExecutionPolicy Bypass -File ".\scripts\enhanced_id_modifier.ps1" -Help
echo.
pause
goto MAIN_MENU

:LIST_BACKUPS
cls
echo.
echo ================================================================
echo                    Backup File List
echo ================================================================
echo.
powershell -ExecutionPolicy Bypass -File ".\scripts\enhanced_id_modifier.ps1" -ListBackups
echo.
pause
goto MAIN_MENU

:RESTORE_BACKUP
cls
echo.
echo ================================================================
echo                    Restore from Backup
echo ================================================================
echo.
echo First, view available backup files:
echo.
powershell -ExecutionPolicy Bypass -File ".\scripts\enhanced_id_modifier.ps1" -ListBackups
echo.
set /p editor="Enter editor name to restore (e.g: VSCode): "
if "%editor%"=="" goto MAIN_MENU

set /p backup_date="Enter backup date (format: YYYYMMDD, leave empty for latest): "

echo.
echo Restoring from backup...
if "%backup_date%"=="" (
    powershell -ExecutionPolicy Bypass -File ".\scripts\enhanced_id_modifier.ps1" -Restore -Editor "%editor%"
) else (
    powershell -ExecutionPolicy Bypass -File ".\scripts\enhanced_id_modifier.ps1" -Restore -Editor "%editor%" -BackupDate "%backup_date%"
)
echo.
echo Restore operation completed!
pause
goto MAIN_MENU

:CLEAN_BACKUPS
cls
echo.
echo ================================================================
echo                    Clean Old Backups
echo ================================================================
echo.
set /p days="Enter days to keep (delete older backups, e.g: 30): "
if "%days%"=="" goto MAIN_MENU

echo.
echo WARNING: Will delete all backup files older than %days% days
set /p confirm="Confirm cleanup of old backups? (Y/N): "
if /i not "%confirm%"=="Y" goto MAIN_MENU

echo.
echo Cleaning old backups...
powershell -ExecutionPolicy Bypass -File ".\scripts\enhanced_id_modifier.ps1" -CleanBackups -OlderThan %days%
echo.
echo Cleanup operation completed!
pause
goto MAIN_MENU

:SHOW_HELP
cls
echo.
echo ================================================================
echo                    Help Information
echo ================================================================
echo.
echo AUG 0.1 Enhanced Telemetry ID Modifier
echo.
echo Main Features:
echo   * Support multiple editors: VS Code, Cursor, VSCodium, Code-OSS
echo   * Modify 50+ types of telemetry ID fields
echo   * Automatic backup and restore functionality
echo   * Multiple operation modes available
echo.
echo Usage Notes:
echo   * Please close all target editors before running
echo   * All operations will automatically create backups
echo   * Restart editors after modification for changes to take effect
echo   * Recommend regular cleanup of old backup files
echo.
echo Technical Features:
echo   * Cryptographically secure random ID generation
echo   * Integrity verification
echo   * Detailed operation logging
echo   * Batch processing capabilities
echo.
echo Support Information:
echo   * Project Version: v0.1.0
echo   * Enhanced development based on original project
echo   * For legitimate privacy protection purposes only
echo.
pause
goto MAIN_MENU

:EXIT_PROGRAM
cls
echo.
echo ================================================================
echo                    Thank You for Using
echo ================================================================
echo.
echo AUG 0.1 Enhanced Telemetry ID Modifier
echo.
echo Main Improvements:
echo   * Support for more editor types
echo   * Added 50+ telemetry fields
echo   * Enhanced security and backup mechanisms
echo   * More user-friendly interface
echo.
echo Usage Reminders:
echo   * If you modified telemetry IDs, please restart related editors
echo   * Backup files are saved in the backups directory
echo   * Operation logs are saved in the logs directory
echo.
echo Privacy Statement:
echo   This tool is for legitimate privacy protection purposes only
echo   Please comply with relevant laws and software terms of use
echo.
echo Goodbye!
timeout /t 3 >nul
exit /b 0
