# AUG 0.1 Testing & Validation Guide

## Quick Start Testing

### Windows PowerShell Testing
```powershell
# Run comprehensive test suite
.\TelemetryTestSuite.ps1 -Verbose

# Test with modification simulation
.\TelemetryTestSuite.ps1 -TestModification -GenerateReport

# Quick editor detection test
powershell -Command "& { $editors = @{ 'VSCode' = '$env:APPDATA\Code\User\globalStorage\storage.json'; 'Cursor' = '$env:APPDATA\Cursor\User\globalStorage\storage.json' }; foreach($e in $editors.Keys) { if(Test-Path $editors[$e]) { Write-Host \"[FOUND] $e\" -ForegroundColor Green } else { Write-Host \"[NOT FOUND] $e\" -ForegroundColor Red } } }"
```

### Unix/Linux Testing
```bash
# Make script executable
chmod +x telemetry_test_suite.sh

# Run comprehensive test suite
./telemetry_test_suite.sh --verbose

# Test with modification simulation
./telemetry_test_suite.sh --test-modification --generate-report

# Quick dependency check
for dep in jq hexdump uuidgen; do command -v $dep >/dev/null && echo "✓ $dep" || echo "✗ $dep"; done
```

## Manual Telemetry Inspection

### Current Telemetry Fields Analysis
```powershell
# Windows - Analyze VS Code telemetry
$vscodePath = "$env:APPDATA\Code\User\globalStorage\storage.json"
if (Test-Path $vscodePath) {
    $content = Get-Content $vscodePath | ConvertFrom-Json
    $telemetryFields = $content.PSObject.Properties | Where-Object { $_.Name -like "telemetry.*" }
    
    Write-Host "Current VS Code Telemetry Fields:" -ForegroundColor Green
    foreach ($field in $telemetryFields) {
        Write-Host "  $($field.Name): $($field.Value)" -ForegroundColor Cyan
    }
    Write-Host "Total telemetry fields: $($telemetryFields.Count)" -ForegroundColor Yellow
}

# Analyze Cursor telemetry
$cursorPath = "$env:APPDATA\Cursor\User\globalStorage\storage.json"
if (Test-Path $cursorPath) {
    $content = Get-Content $cursorPath | ConvertFrom-Json
    $telemetryFields = $content.PSObject.Properties | Where-Object { $_.Name -like "telemetry.*" }
    
    Write-Host "Current Cursor Telemetry Fields:" -ForegroundColor Green
    foreach ($field in $telemetryFields) {
        Write-Host "  $($field.Name): $($field.Value)" -ForegroundColor Cyan
    }
    Write-Host "Total telemetry fields: $($telemetryFields.Count)" -ForegroundColor Yellow
}
```

```bash
# Unix - Analyze telemetry fields
analyze_telemetry() {
    local editor="$1"
    local path="$2"
    
    if [ -f "$path" ]; then
        echo "Current $editor Telemetry Fields:"
        if command -v jq >/dev/null; then
            jq -r 'to_entries[] | select(.key | startswith("telemetry.")) | "  \(.key): \(.value)"' "$path"
            local count=$(jq -r 'keys[] | select(startswith("telemetry."))' "$path" | wc -l)
            echo "Total telemetry fields: $count"
        else
            echo "  jq not available - install with: brew install jq (macOS) or apt install jq (Ubuntu)"
        fi
    else
        echo "$editor not found at: $path"
    fi
    echo ""
}

# Analyze different editors based on OS
case "$(uname -s)" in
    Darwin)
        analyze_telemetry "VS Code" "$HOME/Library/Application Support/Code/User/globalStorage/storage.json"
        analyze_telemetry "Cursor" "$HOME/Library/Application Support/Cursor/User/globalStorage/storage.json"
        ;;
    Linux)
        analyze_telemetry "VS Code" "$HOME/.config/Code/User/globalStorage/storage.json"
        analyze_telemetry "Cursor" "$HOME/.config/Cursor/User/globalStorage/storage.json"
        ;;
esac
```

## Testing Telemetry Modification

### Safe Testing Method
```powershell
# Windows - Create test environment
function Test-TelemetryModification {
    # Create test file
    $testData = @{
        "telemetry.machineId" = "original-machine-id-12345"
        "telemetry.devDeviceId" = "original-device-id-67890"
        "telemetry.sessionId" = "original-session-id-abcdef"
        "otherData" = "should-remain-unchanged"
    }
    
    $testFile = "test_storage.json"
    $testData | ConvertTo-Json | Set-Content $testFile
    
    Write-Host "Original data:" -ForegroundColor Yellow
    Get-Content $testFile | ConvertFrom-Json | Format-List
    
    # Simulate modification
    $content = Get-Content $testFile | ConvertFrom-Json
    $content."telemetry.machineId" = -join ((1..64) | ForEach-Object { '{0:x}' -f (Get-Random -Maximum 16) })
    $content."telemetry.devDeviceId" = [System.Guid]::NewGuid().ToString().ToLower()
    $content | ConvertTo-Json | Set-Content $testFile
    
    Write-Host "Modified data:" -ForegroundColor Green
    Get-Content $testFile | ConvertFrom-Json | Format-List
    
    # Cleanup
    Remove-Item $testFile
}

Test-TelemetryModification
```

### Backup Verification
```powershell
# Windows - Verify backup system
function Test-BackupSystem {
    $backupDir = ".\backups"
    if (Test-Path $backupDir) {
        $backups = Get-ChildItem $backupDir -Filter "*.json" | Sort-Object LastWriteTime -Descending
        
        Write-Host "Backup Analysis:" -ForegroundColor Green
        Write-Host "  Total backups: $($backups.Count)" -ForegroundColor Cyan
        Write-Host "  Backup directory: $backupDir" -ForegroundColor Cyan
        
        if ($backups.Count -gt 0) {
            Write-Host "  Recent backups:" -ForegroundColor Cyan
            $backups | Select-Object -First 5 | ForEach-Object {
                $age = (Get-Date) - $_.LastWriteTime
                $ageStr = if ($age.Days -gt 0) { "$($age.Days)d" } elseif ($age.Hours -gt 0) { "$($age.Hours)h" } else { "$($age.Minutes)m" }
                Write-Host "    $($_.Name) ($ageStr ago, $([math]::Round($_.Length/1KB, 1))KB)" -ForegroundColor Gray
            }
            
            # Test latest backup integrity
            $latest = $backups[0]
            try {
                $content = Get-Content $latest.FullName | ConvertFrom-Json
                Write-Host "  Latest backup JSON: Valid ✓" -ForegroundColor Green
            }
            catch {
                Write-Host "  Latest backup JSON: Invalid ✗" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "Backup directory not found: $backupDir" -ForegroundColor Red
    }
}

Test-BackupSystem
```

## Process Detection Testing

### Check Running Editors
```powershell
# Windows - Check for running editor processes
function Test-RunningProcesses {
    $processes = @("Code", "Cursor", "VSCodium", "code-oss")
    $running = @()
    
    Write-Host "Process Detection:" -ForegroundColor Green
    foreach ($proc in $processes) {
        $process = Get-Process -Name $proc -ErrorAction SilentlyContinue
        if ($process) {
            $running += $proc
            Write-Host "  ⚠ $proc is running (PID: $($process.Id))" -ForegroundColor Yellow
        } else {
            Write-Host "  ✓ $proc not running" -ForegroundColor Green
        }
    }
    
    if ($running.Count -gt 0) {
        Write-Host "Warning: $($running.Count) editor(s) running. Close before modification." -ForegroundColor Yellow
        return $false
    } else {
        Write-Host "All clear: No editors running" -ForegroundColor Green
        return $true
    }
}

$canProceed = Test-RunningProcesses
if ($canProceed) {
    Write-Host "✓ Safe to proceed with telemetry modification" -ForegroundColor Green
} else {
    Write-Host "⚠ Close running editors before proceeding" -ForegroundColor Yellow
}
```

```bash
# Unix - Check for running editor processes
check_running_processes() {
    local processes=("code" "cursor" "codium")
    local running=()
    
    echo "Process Detection:"
    for proc in "${processes[@]}"; do
        if pgrep -f "$proc" > /dev/null 2>&1; then
            echo "  ⚠ $proc is running"
            running+=("$proc")
        else
            echo "  ✓ $proc not running"
        fi
    done
    
    if [ ${#running[@]} -gt 0 ]; then
        echo "Warning: ${#running[@]} editor(s) running: ${running[*]}"
        echo "Close before modification."
        return 1
    else
        echo "All clear: No editors running"
        return 0
    fi
}

if check_running_processes; then
    echo "✓ Safe to proceed with telemetry modification"
else
    echo "⚠ Close running editors before proceeding"
fi
```

## Configuration Validation

### Verify Configuration Files
```powershell
# Windows - Validate configuration
function Test-Configuration {
    $configs = @{
        'Main Config' = 'config\telemetry_config.json'
        'Extended Fields' = 'config\extended_telemetry_fields.json'
    }
    
    Write-Host "Configuration Validation:" -ForegroundColor Green
    foreach ($name in $configs.Keys) {
        $path = $configs[$name]
        if (Test-Path $path) {
            Write-Host "  ✓ $name found" -ForegroundColor Green
            
            try {
                $content = Get-Content $path | ConvertFrom-Json
                Write-Host "    → Valid JSON format" -ForegroundColor Cyan
                
                if ($name -eq 'Main Config') {
                    $editorCount = $content.editors.PSObject.Properties.Count
                    $fieldCount = $content.telemetry_fields.PSObject.Properties.Count
                    Write-Host "    → Editors configured: $editorCount" -ForegroundColor Cyan
                    Write-Host "    → Fields configured: $fieldCount" -ForegroundColor Cyan
                }
            }
            catch {
                Write-Host "    ✗ Invalid JSON format" -ForegroundColor Red
            }
        } else {
            Write-Host "  ✗ $name not found: $path" -ForegroundColor Red
        }
    }
}

Test-Configuration
```

## Performance Testing

### Measure Operation Speed
```powershell
# Windows - Performance testing
function Test-Performance {
    Write-Host "Performance Testing:" -ForegroundColor Green
    
    # Test JSON parsing speed
    $testFile = "performance_test.json"
    $largeData = @{}
    for ($i = 1; $i -le 1000; $i++) {
        $largeData["field$i"] = "value$i"
    }
    $largeData | ConvertTo-Json | Set-Content $testFile
    
    $startTime = Get-Date
    $content = Get-Content $testFile | ConvertFrom-Json
    $endTime = Get-Date
    $parseTime = ($endTime - $startTime).TotalMilliseconds
    
    Write-Host "  JSON Parse Time: $([math]::Round($parseTime, 2))ms" -ForegroundColor Cyan
    
    # Test ID generation speed
    $startTime = Get-Date
    for ($i = 1; $i -le 100; $i++) {
        $null = [System.Guid]::NewGuid().ToString()
    }
    $endTime = Get-Date
    $idGenTime = ($endTime - $startTime).TotalMilliseconds
    
    Write-Host "  ID Generation (100x): $([math]::Round($idGenTime, 2))ms" -ForegroundColor Cyan
    
    Remove-Item $testFile
}

Test-Performance
```

## Automated Testing Schedule

### Set Up Regular Testing
```powershell
# Windows - Schedule automated testing
function New-TestingSchedule {
    $scriptPath = (Get-Location).Path + "\TelemetryTestSuite.ps1"
    $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File `"$scriptPath`" -GenerateReport"
    $trigger = New-ScheduledTaskTrigger -Daily -At "02:00"
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
    
    try {
        Register-ScheduledTask -TaskName "AUG-TelemetryTest" -Action $action -Trigger $trigger -Settings $settings -Description "Daily AUG 0.1 telemetry testing"
        Write-Host "✓ Scheduled daily testing at 2:00 AM" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to schedule testing: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Uncomment to enable scheduled testing
# New-TestingSchedule
```

## Troubleshooting Common Issues

### Issue 1: PowerShell Execution Policy
```powershell
# Check current policy
Get-ExecutionPolicy

# Set policy for current user (if needed)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Issue 2: Missing Dependencies (Unix)
```bash
# macOS
brew install jq

# Ubuntu/Debian
sudo apt update && sudo apt install jq

# Fedora/RHEL
sudo dnf install jq
```

### Issue 3: Permission Issues
```powershell
# Windows - Run as administrator if needed
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "Administrator privileges required for some operations" -ForegroundColor Yellow
}
```

```bash
# Unix - Check file permissions
ls -la scripts/enhanced_id_modifier.sh

# Make executable if needed
chmod +x scripts/enhanced_id_modifier.sh
```

## Quick Reference Commands

### Essential Testing Commands
```powershell
# Windows Quick Tests
.\TelemetryTestSuite.ps1                    # Basic test suite
.\TelemetryTestSuite.ps1 -Verbose           # Detailed output
.\TelemetryTestSuite.ps1 -TestModification  # Include modification test
.\TelemetryTestSuite.ps1 -GenerateReport    # Generate JSON report

# Check specific editor
$path = "$env:APPDATA\Code\User\globalStorage\storage.json"
if (Test-Path $path) { (Get-Content $path | ConvertFrom-Json).PSObject.Properties | Where-Object { $_.Name -like "telemetry.*" } | Measure-Object | Select-Object Count }
```

```bash
# Unix Quick Tests
./telemetry_test_suite.sh                    # Basic test suite
./telemetry_test_suite.sh --verbose          # Detailed output
./telemetry_test_suite.sh --test-modification # Include modification test
./telemetry_test_suite.sh --generate-report  # Generate JSON report

# Check specific editor (requires jq)
path="$HOME/.config/Code/User/globalStorage/storage.json"
[ -f "$path" ] && jq -r 'keys[] | select(startswith("telemetry."))' "$path" | wc -l
```

---

**Note**: Always run tests in a safe environment and ensure you have backups before making any modifications to production editor configurations.
