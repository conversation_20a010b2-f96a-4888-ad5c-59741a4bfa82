#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器模块
管理SQLite数据库的扫描、清理和备份
"""

import os
import sqlite3
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import json
import re

class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.backup_dir = Path("backups/database")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def scan_databases(self, editor_paths: List[str] = None) -> List[Dict]:
        """扫描数据库文件"""
        databases = []

        # 默认扫描路径 - 更全面的路径列表
        if not editor_paths:
            editor_paths = [
                # VS Code 系列
                os.path.expandvars("%APPDATA%\\Code"),
                os.path.expandvars("%APPDATA%\\Cursor"),
                os.path.expandvars("%APPDATA%\\VSCodium"),
                os.path.expandvars("%APPDATA%\\Code - Insiders"),
                os.path.expandvars("%APPDATA%\\Code - OSS"),

                # 用户数据目录
                os.path.expandvars("%LOCALAPPDATA%\\Programs\\Microsoft VS Code"),
                os.path.expandvars("%LOCALAPPDATA%\\Programs\\Cursor"),
                os.path.expandvars("%LOCALAPPDATA%\\Programs\\VSCodium"),

                # 缓存目录
                os.path.expandvars("%APPDATA%\\Code\\CachedData"),
                os.path.expandvars("%APPDATA%\\Cursor\\CachedData"),
                os.path.expandvars("%APPDATA%\\VSCodium\\CachedData"),

                # 扩展目录
                os.path.expandvars("%USERPROFILE%\\.vscode\\extensions"),
                os.path.expandvars("%USERPROFILE%\\.cursor\\extensions"),
                os.path.expandvars("%USERPROFILE%\\.vscodium\\extensions"),

                # 工作区存储
                os.path.expandvars("%APPDATA%\\Code\\User\\workspaceStorage"),
                os.path.expandvars("%APPDATA%\\Cursor\\User\\workspaceStorage"),
                os.path.expandvars("%APPDATA%\\VSCodium\\User\\workspaceStorage"),

                # 系统临时目录中的编辑器数据
                os.path.expandvars("%TEMP%\\vscode"),
                os.path.expandvars("%TEMP%\\cursor"),

                # 常见的应用数据目录
                os.path.expandvars("%LOCALAPPDATA%\\Microsoft\\VSCode"),
                os.path.expandvars("%LOCALAPPDATA%\\Cursor"),
            ]

        print(f"开始扫描 {len(editor_paths)} 个路径...")

        for base_path in editor_paths:
            if os.path.exists(base_path):
                print(f"扫描路径: {base_path}")
                found_dbs = self._scan_directory_for_databases(base_path)
                databases.extend(found_dbs)
                print(f"  发现 {len(found_dbs)} 个数据库文件")
            else:
                print(f"路径不存在: {base_path}")

        return databases
        
    def _scan_directory_for_databases(self, directory: str) -> List[Dict]:
        """扫描目录中的数据库文件"""
        databases = []

        try:
            for root, _, files in os.walk(directory):
                for file in files:
                    # 检查文件扩展名
                    if self._is_database_file(file):
                        db_path = os.path.join(root, file)

                        # 跳过太大的文件（可能不是我们要的数据库）
                        try:
                            file_size = os.path.getsize(db_path)
                            if file_size > 100 * 1024 * 1024:  # 跳过大于100MB的文件
                                continue
                        except:
                            continue

                        db_info = self._analyze_database(db_path)
                        if db_info:
                            databases.append(db_info)

        except Exception as e:
            print(f"扫描目录失败 {directory}: {e}")

        return databases

    def _is_database_file(self, filename: str) -> bool:
        """判断是否为数据库文件"""
        filename_lower = filename.lower()

        # 常见的数据库文件扩展名
        db_extensions = ['.db', '.sqlite', '.sqlite3', '.db3', '.s3db']

        # 检查扩展名
        for ext in db_extensions:
            if filename_lower.endswith(ext):
                return True

        # 检查特定的数据库文件名模式
        db_patterns = [
            'storage.db', 'cache.db', 'index.db', 'history.db',
            'extensions.db', 'workspace.db', 'settings.db',
            'telemetry.db', 'analytics.db', 'logs.db'
        ]

        for pattern in db_patterns:
            if pattern in filename_lower:
                return True

        # 检查是否为SQLite文件（通过文件头）
        return self._is_sqlite_file_by_header(filename)

    def _is_sqlite_file_by_header(self, file_path: str) -> bool:
        """通过文件头判断是否为SQLite文件"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(16)
                # SQLite文件头标识
                return header.startswith(b'SQLite format 3\x00')
        except:
            return False

    def _analyze_database(self, db_path: str) -> Optional[Dict]:
        """分析数据库文件"""
        try:
            # 获取文件信息
            stat = os.stat(db_path)
            
            db_info = {
                "path": db_path,
                "size": stat.st_size,
                "last_modified": datetime.fromtimestamp(stat.st_mtime),
                "total_records": 0,
                "augment_records": 0,
                "tables": [],
                "accessible": False
            }
            
            # 尝试连接数据库
            try:
                # 使用只读模式连接，避免锁定文件
                with sqlite3.connect(f"file:{db_path}?mode=ro", uri=True, timeout=2) as conn:
                    cursor = conn.cursor()

                    # 获取表列表
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    db_info["tables"] = tables
                    db_info["accessible"] = True

                    # 统计总记录数和augment记录数
                    total_records = 0
                    augment_records = 0

                    for table in tables:
                        try:
                            # 统计表记录数（限制查询时间）
                            cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                            count = cursor.fetchone()[0]
                            total_records += count

                            # 查找包含'augment'的记录 - 更安全的方式
                            if count > 0 and count < 10000:  # 只对小表进行内容搜索
                                cursor.execute(f"PRAGMA table_info(`{table}`)")
                                columns = [col[1] for col in cursor.fetchall()]

                                for column in columns:
                                    try:
                                        # 使用参数化查询避免SQL注入
                                        cursor.execute(f"SELECT COUNT(*) FROM `{table}` WHERE `{column}` LIKE ?", ('%augment%',))
                                        augment_count = cursor.fetchone()[0]
                                        augment_records += augment_count
                                    except Exception:
                                        # 忽略类型不匹配等错误
                                        continue

                        except Exception as e:
                            print(f"分析表 {table} 失败: {e}")
                            continue

                    db_info["total_records"] = total_records
                    db_info["augment_records"] = augment_records

            except Exception as e:
                # 如果只读模式失败，尝试检查文件是否为有效的SQLite文件
                if self._is_sqlite_file_by_header(db_path):
                    db_info["accessible"] = False
                    db_info["error"] = f"数据库被锁定或损坏: {str(e)}"
                else:
                    # 不是有效的SQLite文件，返回None
                    return None
                
            return db_info
            
        except Exception as e:
            print(f"分析数据库失败 {db_path}: {e}")
            return None
            
    def backup_database(self, db_path: str) -> Optional[str]:
        """备份数据库文件"""
        try:
            db_name = Path(db_path).name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{db_name}_{timestamp}.backup"
            backup_path = self.backup_dir / backup_filename
            
            shutil.copy2(db_path, backup_path)
            
            # 创建备份信息文件
            info_file = backup_path.with_suffix('.info')
            backup_info = {
                "original_path": db_path,
                "backup_time": timestamp,
                "file_size": os.path.getsize(backup_path)
            }
            
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
                
            return str(backup_path)
            
        except Exception as e:
            print(f"备份数据库失败: {e}")
            return None
            
    def clean_augment_records(self, db_path: str) -> Dict:
        """清理包含'augment'的记录"""
        result = {
            "success": False,
            "deleted_records": 0,
            "affected_tables": [],
            "error": None
        }
        
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 获取所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                total_deleted = 0
                
                for table in tables:
                    try:
                        # 获取表结构
                        cursor.execute(f"PRAGMA table_info(`{table}`)")
                        columns = [col[1] for col in cursor.fetchall()]
                        
                        deleted_in_table = 0
                        
                        for column in columns:
                            try:
                                # 删除包含'augment'的记录
                                cursor.execute(f"DELETE FROM `{table}` WHERE `{column}` LIKE '%augment%'")
                                deleted_count = cursor.rowcount
                                deleted_in_table += deleted_count
                                
                            except Exception as e:
                                print(f"清理表 {table} 列 {column} 失败: {e}")
                                continue
                        
                        if deleted_in_table > 0:
                            result["affected_tables"].append({
                                "table": table,
                                "deleted_records": deleted_in_table
                            })
                            total_deleted += deleted_in_table
                            
                    except Exception as e:
                        print(f"处理表 {table} 失败: {e}")
                        continue
                
                # 提交更改
                conn.commit()
                
                result["success"] = True
                result["deleted_records"] = total_deleted
                
        except Exception as e:
            result["error"] = str(e)
            
        return result
        
    def clean_telemetry_data(self, db_path: str) -> Dict:
        """清理遥测数据"""
        result = {
            "success": False,
            "deleted_records": 0,
            "affected_tables": [],
            "error": None
        }
        
        telemetry_keywords = [
            'telemetry', 'analytics', 'tracking', 'metrics', 
            'usage', 'statistics', 'diagnostic', 'crash'
        ]
        
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 获取所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                total_deleted = 0
                
                for table in tables:
                    try:
                        # 获取表结构
                        cursor.execute(f"PRAGMA table_info(`{table}`)")
                        columns = [col[1] for col in cursor.fetchall()]
                        
                        deleted_in_table = 0
                        
                        for column in columns:
                            for keyword in telemetry_keywords:
                                try:
                                    cursor.execute(f"DELETE FROM `{table}` WHERE `{column}` LIKE '%{keyword}%'")
                                    deleted_count = cursor.rowcount
                                    deleted_in_table += deleted_count
                                    
                                except Exception as e:
                                    continue
                        
                        if deleted_in_table > 0:
                            result["affected_tables"].append({
                                "table": table,
                                "deleted_records": deleted_in_table
                            })
                            total_deleted += deleted_in_table
                            
                    except Exception as e:
                        print(f"处理表 {table} 失败: {e}")
                        continue
                
                # 提交更改
                conn.commit()
                
                result["success"] = True
                result["deleted_records"] = total_deleted
                
        except Exception as e:
            result["error"] = str(e)
            
        return result
        
    def vacuum_database(self, db_path: str) -> bool:
        """压缩数据库"""
        try:
            with sqlite3.connect(db_path) as conn:
                conn.execute("VACUUM")
                conn.commit()
            return True
        except Exception as e:
            print(f"压缩数据库失败: {e}")
            return False
            
    def get_database_size(self, db_path: str) -> int:
        """获取数据库文件大小"""
        try:
            return os.path.getsize(db_path)
        except:
            return 0
            
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"
