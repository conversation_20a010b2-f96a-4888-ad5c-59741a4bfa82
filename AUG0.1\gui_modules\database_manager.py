#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器模块
管理SQLite数据库的扫描、清理和备份
"""

import os
import sqlite3
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import json
import re

class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.backup_dir = Path("backups/database")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def scan_databases(self, editor_paths: List[str] = None) -> List[Dict]:
        """扫描数据库文件"""
        databases = []
        
        # 默认扫描路径
        if not editor_paths:
            editor_paths = [
                os.path.expandvars("%APPDATA%\\Code"),
                os.path.expandvars("%APPDATA%\\Cursor"),
                os.path.expandvars("%APPDATA%\\VSCodium"),
                os.path.expandvars("%APPDATA%\\Code - Insiders"),
                os.path.expandvars("%APPDATA%\\Code - OSS")
            ]
        
        for base_path in editor_paths:
            if os.path.exists(base_path):
                databases.extend(self._scan_directory_for_databases(base_path))
                
        return databases
        
    def _scan_directory_for_databases(self, directory: str) -> List[Dict]:
        """扫描目录中的数据库文件"""
        databases = []
        
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith(('.db', '.sqlite', '.sqlite3')):
                        db_path = os.path.join(root, file)
                        db_info = self._analyze_database(db_path)
                        if db_info:
                            databases.append(db_info)
        except Exception as e:
            print(f"扫描目录失败 {directory}: {e}")
            
        return databases
        
    def _analyze_database(self, db_path: str) -> Optional[Dict]:
        """分析数据库文件"""
        try:
            # 获取文件信息
            stat = os.stat(db_path)
            
            db_info = {
                "path": db_path,
                "size": stat.st_size,
                "last_modified": datetime.fromtimestamp(stat.st_mtime),
                "total_records": 0,
                "augment_records": 0,
                "tables": [],
                "accessible": False
            }
            
            # 尝试连接数据库
            try:
                with sqlite3.connect(db_path, timeout=5) as conn:
                    cursor = conn.cursor()
                    
                    # 获取表列表
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    db_info["tables"] = tables
                    db_info["accessible"] = True
                    
                    # 统计总记录数
                    total_records = 0
                    augment_records = 0
                    
                    for table in tables:
                        try:
                            # 统计表记录数
                            cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                            count = cursor.fetchone()[0]
                            total_records += count
                            
                            # 查找包含'augment'的记录
                            cursor.execute(f"PRAGMA table_info(`{table}`)")
                            columns = [col[1] for col in cursor.fetchall()]
                            
                            for column in columns:
                                try:
                                    cursor.execute(f"SELECT COUNT(*) FROM `{table}` WHERE `{column}` LIKE '%augment%'")
                                    augment_count = cursor.fetchone()[0]
                                    augment_records += augment_count
                                except:
                                    continue
                                    
                        except Exception as e:
                            print(f"分析表 {table} 失败: {e}")
                            continue
                    
                    db_info["total_records"] = total_records
                    db_info["augment_records"] = augment_records
                    
            except Exception as e:
                print(f"无法访问数据库 {db_path}: {e}")
                
            return db_info
            
        except Exception as e:
            print(f"分析数据库失败 {db_path}: {e}")
            return None
            
    def backup_database(self, db_path: str) -> Optional[str]:
        """备份数据库文件"""
        try:
            db_name = Path(db_path).name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{db_name}_{timestamp}.backup"
            backup_path = self.backup_dir / backup_filename
            
            shutil.copy2(db_path, backup_path)
            
            # 创建备份信息文件
            info_file = backup_path.with_suffix('.info')
            backup_info = {
                "original_path": db_path,
                "backup_time": timestamp,
                "file_size": os.path.getsize(backup_path)
            }
            
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
                
            return str(backup_path)
            
        except Exception as e:
            print(f"备份数据库失败: {e}")
            return None
            
    def clean_augment_records(self, db_path: str) -> Dict:
        """清理包含'augment'的记录"""
        result = {
            "success": False,
            "deleted_records": 0,
            "affected_tables": [],
            "error": None
        }
        
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 获取所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                total_deleted = 0
                
                for table in tables:
                    try:
                        # 获取表结构
                        cursor.execute(f"PRAGMA table_info(`{table}`)")
                        columns = [col[1] for col in cursor.fetchall()]
                        
                        deleted_in_table = 0
                        
                        for column in columns:
                            try:
                                # 删除包含'augment'的记录
                                cursor.execute(f"DELETE FROM `{table}` WHERE `{column}` LIKE '%augment%'")
                                deleted_count = cursor.rowcount
                                deleted_in_table += deleted_count
                                
                            except Exception as e:
                                print(f"清理表 {table} 列 {column} 失败: {e}")
                                continue
                        
                        if deleted_in_table > 0:
                            result["affected_tables"].append({
                                "table": table,
                                "deleted_records": deleted_in_table
                            })
                            total_deleted += deleted_in_table
                            
                    except Exception as e:
                        print(f"处理表 {table} 失败: {e}")
                        continue
                
                # 提交更改
                conn.commit()
                
                result["success"] = True
                result["deleted_records"] = total_deleted
                
        except Exception as e:
            result["error"] = str(e)
            
        return result
        
    def clean_telemetry_data(self, db_path: str) -> Dict:
        """清理遥测数据"""
        result = {
            "success": False,
            "deleted_records": 0,
            "affected_tables": [],
            "error": None
        }
        
        telemetry_keywords = [
            'telemetry', 'analytics', 'tracking', 'metrics', 
            'usage', 'statistics', 'diagnostic', 'crash'
        ]
        
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 获取所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                total_deleted = 0
                
                for table in tables:
                    try:
                        # 获取表结构
                        cursor.execute(f"PRAGMA table_info(`{table}`)")
                        columns = [col[1] for col in cursor.fetchall()]
                        
                        deleted_in_table = 0
                        
                        for column in columns:
                            for keyword in telemetry_keywords:
                                try:
                                    cursor.execute(f"DELETE FROM `{table}` WHERE `{column}` LIKE '%{keyword}%'")
                                    deleted_count = cursor.rowcount
                                    deleted_in_table += deleted_count
                                    
                                except Exception as e:
                                    continue
                        
                        if deleted_in_table > 0:
                            result["affected_tables"].append({
                                "table": table,
                                "deleted_records": deleted_in_table
                            })
                            total_deleted += deleted_in_table
                            
                    except Exception as e:
                        print(f"处理表 {table} 失败: {e}")
                        continue
                
                # 提交更改
                conn.commit()
                
                result["success"] = True
                result["deleted_records"] = total_deleted
                
        except Exception as e:
            result["error"] = str(e)
            
        return result
        
    def vacuum_database(self, db_path: str) -> bool:
        """压缩数据库"""
        try:
            with sqlite3.connect(db_path) as conn:
                conn.execute("VACUUM")
                conn.commit()
            return True
        except Exception as e:
            print(f"压缩数据库失败: {e}")
            return False
            
    def get_database_size(self, db_path: str) -> int:
        """获取数据库文件大小"""
        try:
            return os.path.getsize(db_path)
        except:
            return 0
            
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"
