#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版数据库管理器模块
管理编辑器数据文件的扫描、清理和备份（包括SQLite、JSON、日志等）
"""

import os
import sqlite3
import shutil
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.backup_dir = Path("backups/database")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def scan_databases(self, editor_paths: List[str] = None) -> List[Dict]:
        """扫描数据库和数据文件"""
        data_files = []
        
        # 默认扫描路径
        if not editor_paths:
            editor_paths = [
                # VS Code 系列 - 主要数据目录
                os.path.expandvars("%APPDATA%\\Code\\User"),
                os.path.expandvars("%APPDATA%\\Cursor\\User"),
                os.path.expandvars("%APPDATA%\\VSCodium\\User"),
                
                # 日志目录
                os.path.expandvars("%APPDATA%\\Code\\logs"),
                os.path.expandvars("%APPDATA%\\Cursor\\logs"),
                
                # 缓存目录
                os.path.expandvars("%APPDATA%\\Code\\CachedData"),
                os.path.expandvars("%APPDATA%\\Cursor\\CachedData"),
                
                # 常见的SQLite数据库位置
                os.path.expandvars("%LOCALAPPDATA%\\Google\\Chrome\\User Data\\Default"),
                os.path.expandvars("%LOCALAPPDATA%\\Microsoft\\Edge\\User Data\\Default"),
            ]
        
        print(f"开始扫描 {len(editor_paths)} 个路径...")
        
        for base_path in editor_paths:
            if os.path.exists(base_path):
                print(f"扫描路径: {base_path}")
                found_items = self._scan_directory_for_data_files(base_path)
                data_files.extend(found_items)
                print(f"  发现 {len(found_items)} 个数据文件")
            else:
                print(f"路径不存在: {base_path}")
                
        return data_files
        
    def _scan_directory_for_data_files(self, directory: str) -> List[Dict]:
        """扫描目录中的数据文件"""
        data_files = []
        
        try:
            for root, _, files in os.walk(directory):
                for file in files:
                    if self._is_data_file(file):
                        file_path = os.path.join(root, file)
                        
                        # 跳过太大的文件
                        try:
                            file_size = os.path.getsize(file_path)
                            if file_size > 100 * 1024 * 1024:  # 跳过大于100MB的文件
                                continue
                        except:
                            continue
                            
                        file_info = self._analyze_data_file(file_path)
                        if file_info:
                            data_files.append(file_info)
                            
        except Exception as e:
            print(f"扫描目录失败 {directory}: {e}")
            
        return data_files
        
    def _is_data_file(self, filename: str) -> bool:
        """判断是否为数据文件"""
        filename_lower = filename.lower()
        
        # SQLite数据库文件
        if any(filename_lower.endswith(ext) for ext in ['.db', '.sqlite', '.sqlite3', '.db3', '.s3db']):
            return True
        
        # 重要的JSON文件
        if filename_lower.endswith('.json'):
            important_json = [
                'storage.json', 'settings.json', 'globalStorage.json',
                'telemetry.json', 'state.json'
            ]
            if any(pattern in filename_lower for pattern in important_json):
                return True
        
        # 日志文件
        if filename_lower.endswith(('.log', '.txt')):
            log_patterns = ['telemetry', 'analytics', 'usage', 'crash']
            if any(pattern in filename_lower for pattern in log_patterns):
                return True
        
        return False
        
    def _analyze_data_file(self, file_path: str) -> Optional[Dict]:
        """分析数据文件"""
        try:
            stat = os.stat(file_path)
            
            file_info = {
                "path": file_path,
                "size": stat.st_size,
                "last_modified": datetime.fromtimestamp(stat.st_mtime),
                "total_records": 0,
                "augment_records": 0,
                "file_type": self._get_file_type(file_path),
                "tables": [],
                "accessible": False
            }
            
            # 根据文件类型进行分析
            if file_info["file_type"] == "sqlite":
                return self._analyze_sqlite_file(file_path, file_info)
            elif file_info["file_type"] == "json":
                return self._analyze_json_file(file_path, file_info)
            else:
                return self._analyze_text_file(file_path, file_info)
                
        except Exception as e:
            print(f"分析数据文件失败 {file_path}: {e}")
            return None
            
    def _get_file_type(self, file_path: str) -> str:
        """获取文件类型"""
        filename_lower = os.path.basename(file_path).lower()
        
        if any(filename_lower.endswith(ext) for ext in ['.db', '.sqlite', '.sqlite3', '.db3', '.s3db']):
            return "sqlite"
        elif filename_lower.endswith('.json'):
            return "json"
        else:
            return "text"
            
    def _analyze_sqlite_file(self, file_path: str, file_info: Dict) -> Optional[Dict]:
        """分析SQLite文件"""
        try:
            # 检查是否为SQLite文件
            if not self._is_sqlite_file_by_header(file_path):
                return None
                
            # 尝试连接数据库
            try:
                with sqlite3.connect(f"file:{file_path}?mode=ro", uri=True, timeout=2) as conn:
                    cursor = conn.cursor()
                    
                    # 获取表列表
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    file_info["tables"] = tables
                    file_info["accessible"] = True
                    
                    # 统计记录数
                    total_records = 0
                    augment_records = 0
                    
                    for table in tables[:5]:  # 只检查前5个表
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                            count = cursor.fetchone()[0]
                            total_records += count
                            
                            # 检查augment记录
                            if count > 0 and count < 1000:
                                cursor.execute(f"PRAGMA table_info(`{table}`)")
                                columns = [col[1] for col in cursor.fetchall()]
                                
                                for column in columns[:3]:  # 只检查前3列
                                    try:
                                        cursor.execute(f"SELECT COUNT(*) FROM `{table}` WHERE `{column}` LIKE ?", ('%augment%',))
                                        augment_count = cursor.fetchone()[0]
                                        augment_records += augment_count
                                    except:
                                        continue
                                        
                        except Exception:
                            continue
                    
                    file_info["total_records"] = total_records
                    file_info["augment_records"] = augment_records
                    
            except Exception as e:
                file_info["accessible"] = False
                file_info["error"] = f"无法访问数据库: {str(e)}"
                
            return file_info
            
        except Exception as e:
            print(f"分析SQLite文件失败 {file_path}: {e}")
            return None
            
    def _analyze_json_file(self, file_path: str, file_info: Dict) -> Optional[Dict]:
        """分析JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            file_info["accessible"] = True
            
            # 检查是否包含augment
            augment_count = content.lower().count('augment')
            file_info["augment_records"] = augment_count
            
            # 尝试解析JSON
            try:
                data = json.loads(content)
                if isinstance(data, dict):
                    file_info["total_records"] = len(data)
                elif isinstance(data, list):
                    file_info["total_records"] = len(data)
                else:
                    file_info["total_records"] = 1
            except:
                file_info["total_records"] = 1
                
            return file_info
            
        except Exception as e:
            file_info["accessible"] = False
            file_info["error"] = f"无法读取JSON文件: {str(e)}"
            return file_info
            
    def _analyze_text_file(self, file_path: str, file_info: Dict) -> Optional[Dict]:
        """分析文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            file_info["accessible"] = True
            
            # 统计行数和augment出现次数
            lines = content.split('\n')
            file_info["total_records"] = len(lines)
            file_info["augment_records"] = content.lower().count('augment')
            
            return file_info
            
        except Exception as e:
            file_info["accessible"] = False
            file_info["error"] = f"无法读取文件: {str(e)}"
            return file_info
            
    def _is_sqlite_file_by_header(self, file_path: str) -> bool:
        """通过文件头判断是否为SQLite文件"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(16)
                return header.startswith(b'SQLite format 3\x00')
        except:
            return False
            
    def backup_database(self, db_path: str) -> Optional[str]:
        """备份数据库文件"""
        try:
            db_name = Path(db_path).name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{db_name}_{timestamp}.backup"
            backup_path = self.backup_dir / backup_filename
            
            shutil.copy2(db_path, backup_path)
            
            # 创建备份信息文件
            info_file = backup_path.with_suffix('.info')
            backup_info = {
                "original_path": db_path,
                "backup_time": timestamp,
                "file_size": os.path.getsize(backup_path)
            }
            
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
                
            return str(backup_path)
            
        except Exception as e:
            print(f"备份数据库失败: {e}")
            return None
            
    def clean_augment_records(self, file_path: str) -> Dict:
        """清理包含'augment'的记录"""
        result = {
            "success": False,
            "deleted_records": 0,
            "affected_tables": [],
            "error": None
        }
        
        try:
            file_type = self._get_file_type(file_path)
            
            if file_type == "sqlite":
                return self._clean_sqlite_augment(file_path)
            elif file_type == "json":
                return self._clean_json_augment(file_path)
            else:
                return self._clean_text_augment(file_path)
                
        except Exception as e:
            result["error"] = str(e)
            
        return result
        
    def _clean_sqlite_augment(self, file_path: str) -> Dict:
        """清理SQLite文件中的augment记录"""
        result = {"success": False, "deleted_records": 0, "affected_tables": [], "error": None}
        
        try:
            with sqlite3.connect(file_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                total_deleted = 0
                
                for table in tables:
                    try:
                        cursor.execute(f"DELETE FROM `{table}` WHERE CAST(`{table}` AS TEXT) LIKE '%augment%'")
                        deleted_count = cursor.rowcount
                        if deleted_count > 0:
                            result["affected_tables"].append({"table": table, "deleted_records": deleted_count})
                            total_deleted += deleted_count
                    except:
                        continue
                
                conn.commit()
                result["success"] = True
                result["deleted_records"] = total_deleted
                
        except Exception as e:
            result["error"] = str(e)
            
        return result
        
    def _clean_json_augment(self, file_path: str) -> Dict:
        """清理JSON文件中的augment记录"""
        result = {"success": False, "deleted_records": 0, "error": None}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            original_count = content.lower().count('augment')
            
            # 简单的文本替换（可以改进为更精确的JSON解析）
            cleaned_content = content
            import re
            # 移除包含augment的键值对
            cleaned_content = re.sub(r'"[^"]*augment[^"]*"\s*:\s*[^,}]+[,}]', '', cleaned_content, flags=re.IGNORECASE)
            
            new_count = cleaned_content.lower().count('augment')
            deleted_count = original_count - new_count
            
            if deleted_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)
                    
                result["success"] = True
                result["deleted_records"] = deleted_count
            
        except Exception as e:
            result["error"] = str(e)
            
        return result
        
    def _clean_text_augment(self, file_path: str) -> Dict:
        """清理文本文件中的augment记录"""
        result = {"success": False, "deleted_records": 0, "error": None}
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            original_count = len(lines)
            cleaned_lines = [line for line in lines if 'augment' not in line.lower()]
            deleted_count = original_count - len(cleaned_lines)
            
            if deleted_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(cleaned_lines)
                    
                result["success"] = True
                result["deleted_records"] = deleted_count
            
        except Exception as e:
            result["error"] = str(e)
            
        return result
        
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"
