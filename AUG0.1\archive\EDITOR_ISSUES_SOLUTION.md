# 编辑器配置问题解决方案

## 问题总结

在运行 Augment ID 修改器时遇到了以下问题：

1. **Cursor 编辑器 UTF-8 BOM 编码问题**
   - 错误：`Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)`
   - 原因：Cursor 的 storage.json 文件包含 UTF-8 字节序标记（BOM）

2. **VSCodium 存储文件不存在**
   - 错误：`编辑器存储文件不存在`
   - 原因：VSCodium 未安装或配置文件路径不存在

3. **Code - OSS 编辑器不支持**
   - 错误：`不支持的编辑器: Code`
   - 原因：原脚本未包含对 Code - OSS 的支持

## 解决方案

### 1. 自动修复脚本

创建了专用的修复脚本 `fix_editor_issues.py`，功能包括：

- **UTF-8 BOM 检测和修复**：自动检测并移除 UTF-8 BOM
- **缺失文件创建**：为不存在的编辑器创建默认配置
- **文件验证**：确保修复后的 JSON 文件格式正确
- **备份机制**：修复前自动创建备份文件

### 2. 增强的主脚本

更新了 `direct_modify.py`，改进包括：

- **智能编码处理**：自动处理 UTF-8 和 UTF-8-sig 编码
- **扩展编辑器支持**：添加对更多编辑器的支持
- **创建缺失配置**：可选择为不存在的编辑器创建配置
- **错误恢复**：更好的错误处理和恢复机制

## 使用方法

### 方法一：运行修复脚本（推荐）

```bash
# 运行批处理文件
fix_editor_issues.bat

# 或直接运行Python脚本
python fix_editor_issues.py
```

### 方法二：使用增强的主脚本

```bash
# 标准模式
python direct_modify.py --mode enhanced

# 创建缺失配置
python direct_modify.py --mode enhanced --create-missing

# 指定特定编辑器
python direct_modify.py --editors "VSCode,Cursor" --mode enhanced
```

## 支持的编辑器

- **Visual Studio Code** (`VSCode`)
- **Visual Studio Code Insiders** (`VSCodeInsiders`)
- **Cursor Editor** (`Cursor`)
- **VSCodium** (`VSCodium`)
- **Code - OSS** (`CodeOSS`)

## 修复结果

运行修复脚本后的结果：

```
修复完成:
已修复编辑器: 2 (VSCode, Cursor)
已创建配置: 2 (VSCodeInsiders, VSCodium)
总计处理: 4

成功修改: 5/5 个编辑器
```

## 技术细节

### UTF-8 BOM 处理

```python
# 检测BOM
with open(file_path, 'rb') as f:
    content = f.read()
    has_bom = content.startswith(b'\xef\xbb\xbf')

# 读取时处理BOM
encoding = 'utf-8-sig' if has_bom else 'utf-8'
with open(file_path, 'r', encoding=encoding) as f:
    data = json.load(f)
```

### 默认配置创建

为缺失的编辑器创建包含以下字段的默认配置：

- `telemetry.machineId`
- `telemetry.devDeviceId`
- `telemetry.sessionId`
- `telemetry.sqmId`
- `telemetry.firstSessionDate`
- `telemetry.lastSessionDate`
- `telemetry.isNewAppInstall`
- `telemetry.optIn`

## 备份文件

所有修复和修改操作都会自动创建备份文件，保存在 `backups/` 目录下：

- 格式：`{编辑器名}_storage_{时间戳}.json`
- 示例：`Cursor_storage_fixed_20250611_091353.json`

## 注意事项

1. **重启编辑器**：修复完成后需要重启相关编辑器以应用更改
2. **备份重要**：修复前会自动创建备份，如有问题可以恢复
3. **权限要求**：需要对编辑器配置目录的写入权限
4. **编码一致性**：修复后的文件将保持与原文件相同的编码格式

## 故障排除

如果仍然遇到问题：

1. 检查文件权限
2. 确认编辑器已完全关闭
3. 查看备份文件是否正确创建
4. 检查 JSON 文件格式是否有效

## 更新日志

- **v1.1**：添加 UTF-8 BOM 处理
- **v1.2**：支持缺失文件创建
- **v1.3**：扩展编辑器支持
- **v1.4**：改进错误处理和备份机制
