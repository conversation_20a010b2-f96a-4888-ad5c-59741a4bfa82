#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备份管理器模块
管理编辑器配置文件的备份和恢复
"""

import os
import json
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import hashlib

class BackupManager:
    """备份管理器类"""
    
    def __init__(self):
        """初始化备份管理器"""
        self.backup_dir = Path("backups")
        self.backup_dir.mkdir(exist_ok=True)
        
    def create_backup(self, source_path: str, editor_name: str) -> Optional[str]:
        """创建备份文件"""
        try:
            if not os.path.exists(source_path):
                return None
                
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{editor_name}_storage_{timestamp}.json"
            backup_path = self.backup_dir / backup_filename
            
            # 复制文件
            shutil.copy2(source_path, backup_path)
            
            # 创建备份信息文件
            info_file = backup_path.with_suffix('.info')
            backup_info = {
                "source_path": source_path,
                "editor_name": editor_name,
                "backup_time": timestamp,
                "file_size": os.path.getsize(backup_path),
                "file_hash": self.calculate_file_hash(backup_path)
            }
            
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
                
            return str(backup_path)
            
        except Exception as e:
            print(f"创建备份失败: {e}")
            return None
            
    def list_backups(self, editor_name: str = None) -> List[Dict]:
        """列出备份文件"""
        backups = []
        
        try:
            for backup_file in self.backup_dir.glob("*.json"):
                if backup_file.name.endswith('.info'):
                    continue
                    
                # 解析文件名获取信息
                parts = backup_file.stem.split('_')
                if len(parts) >= 3:
                    file_editor = parts[0]
                    file_type = parts[1]
                    file_timestamp = '_'.join(parts[2:])
                    
                    # 过滤编辑器
                    if editor_name and file_editor != editor_name:
                        continue
                        
                    backup_info = {
                        "file_path": str(backup_file),
                        "editor_name": file_editor,
                        "file_type": file_type,
                        "timestamp": file_timestamp,
                        "file_size": backup_file.stat().st_size,
                        "creation_time": datetime.fromtimestamp(backup_file.stat().st_ctime),
                        "modification_time": datetime.fromtimestamp(backup_file.stat().st_mtime)
                    }
                    
                    # 尝试读取详细信息文件
                    info_file = backup_file.with_suffix('.info')
                    if info_file.exists():
                        try:
                            with open(info_file, 'r', encoding='utf-8') as f:
                                detailed_info = json.load(f)
                                backup_info.update(detailed_info)
                        except:
                            pass
                            
                    backups.append(backup_info)
                    
        except Exception as e:
            print(f"列出备份文件失败: {e}")
            
        # 按时间排序（最新的在前）
        backups.sort(key=lambda x: x.get("creation_time", datetime.min), reverse=True)
        return backups
        
    def restore_backup(self, backup_path: str, target_path: str = None) -> bool:
        """恢复备份文件"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                return False
                
            # 如果没有指定目标路径，尝试从信息文件获取
            if not target_path:
                info_file = backup_file.with_suffix('.info')
                if info_file.exists():
                    try:
                        with open(info_file, 'r', encoding='utf-8') as f:
                            backup_info = json.load(f)
                            target_path = backup_info.get("source_path")
                    except:
                        pass
                        
            if not target_path:
                return False
                
            # 创建目标目录
            target_dir = Path(target_path).parent
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 恢复文件
            shutil.copy2(backup_file, target_path)
            return True
            
        except Exception as e:
            print(f"恢复备份失败: {e}")
            return False
            
    def delete_backup(self, backup_path: str) -> bool:
        """删除备份文件"""
        try:
            backup_file = Path(backup_path)
            if backup_file.exists():
                backup_file.unlink()
                
            # 删除对应的信息文件
            info_file = backup_file.with_suffix('.info')
            if info_file.exists():
                info_file.unlink()
                
            return True
            
        except Exception as e:
            print(f"删除备份失败: {e}")
            return False
            
    def clean_old_backups(self, days: int = 30) -> int:
        """清理旧备份文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = 0
            
            for backup_file in self.backup_dir.glob("*.json"):
                if backup_file.name.endswith('.info'):
                    continue
                    
                file_time = datetime.fromtimestamp(backup_file.stat().st_ctime)
                if file_time < cutoff_date:
                    if self.delete_backup(str(backup_file)):
                        deleted_count += 1
                        
            return deleted_count
            
        except Exception as e:
            print(f"清理旧备份失败: {e}")
            return 0
            
    def get_backup_statistics(self) -> Dict:
        """获取备份统计信息"""
        try:
            backups = self.list_backups()
            
            stats = {
                "total_backups": len(backups),
                "total_size": 0,
                "editors": {},
                "oldest_backup": None,
                "newest_backup": None
            }
            
            if backups:
                stats["total_size"] = sum(backup.get("file_size", 0) for backup in backups)
                stats["oldest_backup"] = min(backups, key=lambda x: x.get("creation_time", datetime.max))
                stats["newest_backup"] = max(backups, key=lambda x: x.get("creation_time", datetime.min))
                
                # 按编辑器统计
                for backup in backups:
                    editor = backup.get("editor_name", "unknown")
                    if editor not in stats["editors"]:
                        stats["editors"][editor] = {"count": 0, "size": 0}
                    stats["editors"][editor]["count"] += 1
                    stats["editors"][editor]["size"] += backup.get("file_size", 0)
                    
            return stats
            
        except Exception as e:
            print(f"获取备份统计失败: {e}")
            return {}
            
    def calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return ""
            
    def verify_backup_integrity(self, backup_path: str) -> bool:
        """验证备份文件完整性"""
        try:
            backup_file = Path(backup_path)
            info_file = backup_file.with_suffix('.info')
            
            if not info_file.exists():
                return True  # 没有信息文件，无法验证但不算错误
                
            with open(info_file, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
                
            stored_hash = backup_info.get("file_hash")
            if not stored_hash:
                return True
                
            current_hash = self.calculate_file_hash(backup_file)
            return stored_hash == current_hash
            
        except Exception as e:
            print(f"验证备份完整性失败: {e}")
            return False
            
    def export_backup_list(self, output_path: str) -> bool:
        """导出备份列表"""
        try:
            backups = self.list_backups()
            stats = self.get_backup_statistics()
            
            export_data = {
                "export_time": datetime.now().isoformat(),
                "statistics": stats,
                "backups": []
            }
            
            for backup in backups:
                # 转换datetime对象为字符串
                backup_copy = backup.copy()
                for key, value in backup_copy.items():
                    if isinstance(value, datetime):
                        backup_copy[key] = value.isoformat()
                export_data["backups"].append(backup_copy)
                
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
                
            return True
            
        except Exception as e:
            print(f"导出备份列表失败: {e}")
            return False
            
    def find_latest_backup(self, editor_name: str) -> Optional[str]:
        """查找指定编辑器的最新备份"""
        backups = self.list_backups(editor_name)
        if backups:
            return backups[0]["file_path"]  # 已按时间排序，第一个是最新的
        return None
        
    def get_backup_info(self, backup_path: str) -> Optional[Dict]:
        """获取备份文件详细信息"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                return None
                
            info = {
                "file_path": str(backup_file),
                "file_size": backup_file.stat().st_size,
                "creation_time": datetime.fromtimestamp(backup_file.stat().st_ctime),
                "modification_time": datetime.fromtimestamp(backup_file.stat().st_mtime),
                "integrity_ok": self.verify_backup_integrity(backup_path)
            }
            
            # 尝试读取详细信息
            info_file = backup_file.with_suffix('.info')
            if info_file.exists():
                try:
                    with open(info_file, 'r', encoding='utf-8') as f:
                        detailed_info = json.load(f)
                        info.update(detailed_info)
                except:
                    pass
                    
            # 尝试读取JSON内容获取遥测字段数量
            try:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    telemetry_count = sum(1 for key in data.keys() if key.startswith('telemetry.'))
                    info["telemetry_fields"] = telemetry_count
            except:
                info["telemetry_fields"] = 0
                
            return info
            
        except Exception as e:
            print(f"获取备份信息失败: {e}")
            return None
