#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AUG 0.1 Enhanced Telemetry ID Modifier - 简化GUI版本
不依赖外部库的图形用户界面版本

Author: AUG Team
Version: 0.1.0
License: MIT
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import sys
import subprocess
import threading
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
try:
    from gui_modules.language_manager import LanguageManager
    from gui_modules.config_manager import ConfigManager
    from gui_modules.editor_detector import EditorDetector
    from gui_modules.telemetry_modifier import TelemetryModifier
    from gui_modules.backup_manager import BackupManager
    from gui_modules.log_manager import LogManager
    from gui_modules.database_manager import DatabaseManager
    from gui_modules.workspace_manager import WorkspaceManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必需的模块文件存在")
    sys.exit(1)

class AUGSimpleGUI:
    """AUG 0.1 简化GUI主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        self.setup_managers()
        self.setup_window()
        self.setup_variables()
        self.setup_ui()
        self.load_initial_data()
        
    def setup_managers(self):
        """初始化管理器"""
        try:
            self.lang_manager = LanguageManager()
            self.config_manager = ConfigManager()
            self.editor_detector = EditorDetector()
            self.telemetry_modifier = TelemetryModifier()
            self.backup_manager = BackupManager()
            self.log_manager = LogManager()
            self.database_manager = DatabaseManager()
            self.workspace_manager = WorkspaceManager()
        except Exception as e:
            messagebox.showerror("初始化错误", f"管理器初始化失败: {e}")
            sys.exit(1)
        
    def setup_window(self):
        """设置主窗口"""
        self.root = tk.Tk()
        self.root.title("AUG 0.1 Enhanced Telemetry ID Modifier")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果存在）
        try:
            if os.path.exists("assets/aug_icon.ico"):
                self.root.iconbitmap("assets/aug_icon.ico")
        except:
            pass
        
        # 居中显示窗口
        self.center_window()
        
        # 设置样式
        self.setup_styles()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_styles(self):
        """设置样式"""
        self.style = ttk.Style()
        
        # 尝试设置现代主题
        available_themes = self.style.theme_names()
        if 'clam' in available_themes:
            self.style.theme_use('clam')
        elif 'vista' in available_themes:
            self.style.theme_use('vista')
        
        # 自定义样式
        self.style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        self.style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        self.style.configure('Success.TButton', foreground='green')
        self.style.configure('Warning.TButton', foreground='orange')
        self.style.configure('Danger.TButton', foreground='red')
        
    def setup_variables(self):
        """设置变量"""
        self.current_language = tk.StringVar(value="zh_CN")
        self.operation_mode = tk.StringVar(value="enhanced")
        self.is_operation_running = False
        
    def setup_ui(self):
        """设置用户界面"""
        self.create_main_frame()
        self.create_status_bar()
        
    def create_main_frame(self):
        """创建主框架"""
        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = ttk.Frame(main_container)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(
            title_frame, 
            text="AUG 0.1 Enhanced Telemetry ID Modifier",
            style='Title.TLabel'
        ).pack()
        
        ttk.Label(
            title_frame,
            text="专业的代码编辑器隐私保护工具",
            font=('Arial', 10)
        ).pack()
        
        # 创建Notebook
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建标签页
        self.create_dashboard_tab()
        self.create_operation_tab()
        self.create_fields_tab()
        self.create_cleanup_tab()
        self.create_database_tab()
        self.create_workspace_tab()
        self.create_log_tab()
        
    def create_dashboard_tab(self):
        """创建仪表板标签页"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="🏠 仪表板")
        
        # 欢迎信息
        welcome_frame = ttk.LabelFrame(dashboard_frame, text="欢迎使用", padding=15)
        welcome_frame.pack(fill=tk.X, padx=10, pady=10)
        
        welcome_text = """AUG 0.1 是一个专业的隐私保护工具，用于修改代码编辑器的遥测标识符。

✓ 支持 VS Code、Cursor、VSCodium 等主流编辑器
✓ 提供 50+ 种遥测字段的修改
✓ 4种操作模式：增强、标准、快速、自定义
✓ 自动备份和恢复功能
✓ 实时操作日志监控"""
        
        ttk.Label(welcome_frame, text=welcome_text, justify=tk.LEFT).pack(anchor=tk.W)
        
        # 快速操作
        quick_frame = ttk.LabelFrame(dashboard_frame, text="快速操作", padding=15)
        quick_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 按钮网格
        btn_frame = ttk.Frame(quick_frame)
        btn_frame.pack()
        
        # 第一行
        row1 = ttk.Frame(btn_frame)
        row1.pack(pady=5)
        
        ttk.Button(
            row1, text="🔍 检测编辑器", 
            command=self.detect_editors,
            width=20
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row1, text="⚡ 快速修改", 
            command=lambda: self.run_quick_operation("quick"),
            width=20
        ).pack(side=tk.LEFT, padx=5)
        
        # 第二行
        row2 = ttk.Frame(btn_frame)
        row2.pack(pady=5)
        
        ttk.Button(
            row2, text="🛡️ 增强保护", 
            command=lambda: self.run_quick_operation("enhanced"),
            width=20
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row2, text="📁 备份管理", 
            command=self.open_backup_directory,
            width=20
        ).pack(side=tk.LEFT, padx=5)
        
        # 编辑器状态
        status_frame = ttk.LabelFrame(dashboard_frame, text="编辑器状态", padding=15)
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 编辑器列表
        self.editor_listbox = tk.Listbox(status_frame, height=8)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.editor_listbox.yview)
        self.editor_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.editor_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_operation_tab(self):
        """创建操作标签页"""
        operation_frame = ttk.Frame(self.notebook)
        self.notebook.add(operation_frame, text="⚙️ 操作")
        
        # 操作模式选择
        mode_frame = ttk.LabelFrame(operation_frame, text="操作模式", padding=15)
        mode_frame.pack(fill=tk.X, padx=10, pady=10)
        
        modes = [
            ("enhanced", "🛡️ 增强模式", "修改所有50+遥测字段，最大隐私保护"),
            ("standard", "⚖️ 标准模式", "修改10-15个常用字段，平衡保护"),
            ("quick", "⚡ 快速模式", "只修改2-3个核心字段，快速操作"),
            ("custom", "🔧 自定义模式", "用户完全自定义选择")
        ]
        
        for mode_id, title, desc in modes:
            frame = ttk.Frame(mode_frame)
            frame.pack(fill=tk.X, pady=2)
            
            ttk.Radiobutton(
                frame,
                text=title,
                variable=self.operation_mode,
                value=mode_id
            ).pack(side=tk.LEFT)
            
            ttk.Label(frame, text=desc, foreground="gray").pack(side=tk.LEFT, padx=(10, 0))
            
        # 操作控制
        control_frame = ttk.LabelFrame(operation_frame, text="操作控制", padding=15)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack()
        
        self.start_btn = ttk.Button(
            btn_frame, text="🚀 开始操作", 
            command=self.start_operation,
            width=15
        )
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(
            btn_frame, text="⏹️ 停止操作", 
            command=self.stop_operation,
            width=15,
            state=tk.DISABLED
        )
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        # 进度显示
        progress_frame = ttk.LabelFrame(operation_frame, text="操作进度", padding=15)
        progress_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame, 
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        self.progress_label = ttk.Label(progress_frame, text="等待操作...")
        self.progress_label.pack()

    def create_fields_tab(self):
        """创建字段选择标签页"""
        fields_frame = ttk.Frame(self.notebook)
        self.notebook.add(fields_frame, text="🔧 字段选择")

        # 字段类别选择
        category_frame = ttk.LabelFrame(fields_frame, text="字段类别", padding=10)
        category_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(10, 5), pady=10)

        # 类别列表
        self.categories_listbox = tk.Listbox(category_frame, width=20, height=15)
        cat_scrollbar = ttk.Scrollbar(category_frame, orient=tk.VERTICAL, command=self.categories_listbox.yview)
        self.categories_listbox.configure(yscrollcommand=cat_scrollbar.set)

        self.categories_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        cat_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定选择事件
        self.categories_listbox.bind("<<ListboxSelect>>", self.on_category_select)

        # 字段详情区域
        fields_detail_frame = ttk.Frame(fields_frame)
        fields_detail_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 10), pady=10)

        # 字段列表
        fields_list_frame = ttk.LabelFrame(fields_detail_frame, text="可选字段", padding=10)
        fields_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建字段树形视图
        columns = ("field_name", "type", "description", "privacy_level")
        self.fields_tree = ttk.Treeview(fields_list_frame, columns=columns, show="headings", height=12)

        # 设置列标题
        self.fields_tree.heading("field_name", text="字段名称")
        self.fields_tree.heading("type", text="类型")
        self.fields_tree.heading("description", text="描述")
        self.fields_tree.heading("privacy_level", text="隐私级别")

        # 设置列宽
        self.fields_tree.column("field_name", width=200)
        self.fields_tree.column("type", width=80)
        self.fields_tree.column("description", width=250)
        self.fields_tree.column("privacy_level", width=80)

        # 添加滚动条
        fields_scrollbar = ttk.Scrollbar(fields_list_frame, orient=tk.VERTICAL, command=self.fields_tree.yview)
        self.fields_tree.configure(yscrollcommand=fields_scrollbar.set)

        self.fields_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        fields_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 字段操作按钮
        field_ops_frame = ttk.LabelFrame(fields_detail_frame, text="字段操作", padding=10)
        field_ops_frame.pack(fill=tk.X)

        # 第一行按钮
        btn_row1 = ttk.Frame(field_ops_frame)
        btn_row1.pack(fill=tk.X, pady=2)

        ttk.Button(btn_row1, text="全选", command=self.select_all_fields, width=12).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_row1, text="全不选", command=self.deselect_all_fields, width=12).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_row1, text="反选", command=self.invert_field_selection, width=12).pack(side=tk.LEFT, padx=2)

        # 第二行按钮
        btn_row2 = ttk.Frame(field_ops_frame)
        btn_row2.pack(fill=tk.X, pady=2)

        ttk.Button(btn_row2, text="选择核心字段", command=self.select_core_fields, width=12).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_row2, text="选择高隐私字段", command=self.select_high_privacy_fields, width=12).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_row2, text="清除选择", command=self.clear_field_selection, width=12).pack(side=tk.LEFT, padx=2)

        # 选择统计
        self.field_selection_label = ttk.Label(field_ops_frame, text="已选择: 0 个字段")
        self.field_selection_label.pack(pady=5)

        # 绑定选择事件
        self.fields_tree.bind("<<TreeviewSelect>>", self.on_field_selection_change)

    def create_cleanup_tab(self):
        """创建清理标签页"""
        cleanup_frame = ttk.Frame(self.notebook)
        self.notebook.add(cleanup_frame, text="🧹 清理")

        # 清理选项
        options_frame = ttk.LabelFrame(cleanup_frame, text="清理选项", padding=15)
        options_frame.pack(fill=tk.X, padx=10, pady=10)

        # 清理类型选择
        self.cleanup_vars = {}

        cleanup_options = [
            ("clear_history", "清除编辑器历史记录", "清除最近打开的文件、项目历史等"),
            ("clear_cache", "清除缓存文件", "清除临时文件、缓存数据等"),
            ("clear_settings", "重置用户设置", "恢复编辑器默认设置（谨慎使用）"),
            ("clear_extensions", "清除扩展数据", "清除扩展的配置和数据"),
            ("clear_workspace", "清除工作区数据", "清除工作区特定的设置和状态"),
            ("clear_logs", "清除日志文件", "清除编辑器生成的日志文件"),
            ("clear_crash_reports", "清除崩溃报告", "清除错误报告和诊断数据"),
            ("clear_telemetry_cache", "清除遥测缓存", "清除遥测数据的本地缓存")
        ]

        for var_name, title, description in cleanup_options:
            self.cleanup_vars[var_name] = tk.BooleanVar()

            option_frame = ttk.Frame(options_frame)
            option_frame.pack(fill=tk.X, pady=2)

            ttk.Checkbutton(
                option_frame,
                text=title,
                variable=self.cleanup_vars[var_name]
            ).pack(side=tk.LEFT)

            ttk.Label(
                option_frame,
                text=f" - {description}",
                foreground="gray"
            ).pack(side=tk.LEFT, padx=(10, 0))

        # 快速选择按钮
        quick_select_frame = ttk.LabelFrame(cleanup_frame, text="快速选择", padding=15)
        quick_select_frame.pack(fill=tk.X, padx=10, pady=10)

        btn_frame = ttk.Frame(quick_select_frame)
        btn_frame.pack()

        ttk.Button(
            btn_frame, text="🔥 深度清理",
            command=self.select_deep_cleanup,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            btn_frame, text="🧹 标准清理",
            command=self.select_standard_cleanup,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            btn_frame, text="🔒 隐私清理",
            command=self.select_privacy_cleanup,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            btn_frame, text="❌ 清除选择",
            command=self.clear_cleanup_selection,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        # 清理控制
        control_frame = ttk.LabelFrame(cleanup_frame, text="执行清理", padding=15)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        # 警告信息
        warning_text = """⚠️ 警告: 清理操作将永久删除选定的数据，请确保已备份重要信息！
建议在执行清理前关闭所有相关编辑器。"""

        ttk.Label(
            control_frame,
            text=warning_text,
            foreground="red",
            justify=tk.LEFT
        ).pack(pady=5)

        # 执行按钮
        exec_btn_frame = ttk.Frame(control_frame)
        exec_btn_frame.pack(pady=10)

        self.cleanup_btn = ttk.Button(
            exec_btn_frame, text="🧹 开始清理",
            command=self.start_cleanup,
            width=15
        )
        self.cleanup_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(
            exec_btn_frame, text="📋 预览清理",
            command=self.preview_cleanup,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        # 清理进度
        cleanup_progress_frame = ttk.LabelFrame(cleanup_frame, text="清理进度", padding=15)
        cleanup_progress_frame.pack(fill=tk.X, padx=10, pady=10)

        self.cleanup_progress_var = tk.DoubleVar()
        self.cleanup_progress_bar = ttk.Progressbar(
            cleanup_progress_frame,
            variable=self.cleanup_progress_var,
            maximum=100
        )
        self.cleanup_progress_bar.pack(fill=tk.X, pady=5)

        self.cleanup_status_label = ttk.Label(cleanup_progress_frame, text="等待清理操作...")
        self.cleanup_status_label.pack()

    def create_database_tab(self):
        """创建数据库清理标签页"""
        database_frame = ttk.Frame(self.notebook)
        self.notebook.add(database_frame, text="🗃️ 数据库")

        # 数据库扫描区域
        scan_frame = ttk.LabelFrame(database_frame, text="数据库扫描", padding=15)
        scan_frame.pack(fill=tk.X, padx=10, pady=10)

        # 扫描控制
        scan_control_frame = ttk.Frame(scan_frame)
        scan_control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(
            scan_control_frame, text="🔍 扫描数据库",
            command=self.scan_databases,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            scan_control_frame, text="🔄 刷新列表",
            command=self.refresh_database_list,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        # 数据库列表
        db_list_frame = ttk.LabelFrame(database_frame, text="发现的数据库文件", padding=10)
        db_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建数据库树形视图
        db_columns = ("path", "size", "records", "augment_records", "last_modified")
        self.database_tree = ttk.Treeview(db_list_frame, columns=db_columns, show="headings", height=8)

        # 设置列标题
        self.database_tree.heading("path", text="数据库路径")
        self.database_tree.heading("size", text="文件大小")
        self.database_tree.heading("records", text="总记录数")
        self.database_tree.heading("augment_records", text="Augment记录")
        self.database_tree.heading("last_modified", text="最后修改")

        # 设置列宽
        self.database_tree.column("path", width=300)
        self.database_tree.column("size", width=80)
        self.database_tree.column("records", width=80)
        self.database_tree.column("augment_records", width=100)
        self.database_tree.column("last_modified", width=120)

        # 添加滚动条
        db_scrollbar = ttk.Scrollbar(db_list_frame, orient=tk.VERTICAL, command=self.database_tree.yview)
        self.database_tree.configure(yscrollcommand=db_scrollbar.set)

        self.database_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        db_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 数据库操作区域
        db_ops_frame = ttk.LabelFrame(database_frame, text="数据库操作", padding=15)
        db_ops_frame.pack(fill=tk.X, padx=10, pady=10)

        # 清理选项
        self.db_cleanup_vars = {}

        cleanup_options = [
            ("clean_augment_records", "清理包含'augment'的记录", "删除所有包含'augment'关键字的数据库记录"),
            ("clean_telemetry_data", "清理遥测数据", "删除遥测相关的数据库记录"),
            ("clean_usage_stats", "清理使用统计", "删除使用统计和分析数据"),
            ("clean_error_reports", "清理错误报告", "删除错误报告和崩溃数据"),
            ("vacuum_database", "压缩数据库", "执行VACUUM操作压缩数据库文件"),
            ("backup_before_clean", "清理前备份", "在清理前自动创建数据库备份")
        ]

        for var_name, title, description in cleanup_options:
            self.db_cleanup_vars[var_name] = tk.BooleanVar()
            if var_name == "backup_before_clean":  # 默认启用备份
                self.db_cleanup_vars[var_name].set(True)

            option_frame = ttk.Frame(db_ops_frame)
            option_frame.pack(fill=tk.X, pady=2)

            ttk.Checkbutton(
                option_frame,
                text=title,
                variable=self.db_cleanup_vars[var_name]
            ).pack(side=tk.LEFT)

            ttk.Label(
                option_frame,
                text=f" - {description}",
                foreground="gray"
            ).pack(side=tk.LEFT, padx=(10, 0))

        # 操作按钮
        db_btn_frame = ttk.Frame(db_ops_frame)
        db_btn_frame.pack(pady=10)

        self.db_clean_btn = ttk.Button(
            db_btn_frame, text="🗃️ 清理数据库",
            command=self.start_database_cleanup,
            width=15
        )
        self.db_clean_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(
            db_btn_frame, text="📋 预览清理",
            command=self.preview_database_cleanup,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            db_btn_frame, text="💾 备份数据库",
            command=self.backup_selected_databases,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        # 数据库清理进度
        db_progress_frame = ttk.LabelFrame(database_frame, text="清理进度", padding=15)
        db_progress_frame.pack(fill=tk.X, padx=10, pady=10)

        self.db_progress_var = tk.DoubleVar()
        self.db_progress_bar = ttk.Progressbar(
            db_progress_frame,
            variable=self.db_progress_var,
            maximum=100
        )
        self.db_progress_bar.pack(fill=tk.X, pady=5)

        self.db_status_label = ttk.Label(db_progress_frame, text="等待数据库操作...")
        self.db_status_label.pack()

    def create_workspace_tab(self):
        """创建工作区管理标签页"""
        workspace_frame = ttk.Frame(self.notebook)
        self.notebook.add(workspace_frame, text="💾 工作区")

        # 工作区扫描
        ws_scan_frame = ttk.LabelFrame(workspace_frame, text="工作区扫描", padding=15)
        ws_scan_frame.pack(fill=tk.X, padx=10, pady=10)

        # 扫描控制
        ws_scan_control_frame = ttk.Frame(ws_scan_frame)
        ws_scan_control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(
            ws_scan_control_frame, text="🔍 扫描工作区",
            command=self.scan_workspaces,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            ws_scan_control_frame, text="🔄 刷新列表",
            command=self.refresh_workspace_list,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        # 工作区列表
        ws_list_frame = ttk.LabelFrame(workspace_frame, text="工作区存储文件", padding=10)
        ws_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建工作区树形视图
        ws_columns = ("editor", "workspace_path", "size", "files_count", "last_accessed")
        self.workspace_tree = ttk.Treeview(ws_list_frame, columns=ws_columns, show="headings", height=8)

        # 设置列标题
        self.workspace_tree.heading("editor", text="编辑器")
        self.workspace_tree.heading("workspace_path", text="工作区路径")
        self.workspace_tree.heading("size", text="大小")
        self.workspace_tree.heading("files_count", text="文件数")
        self.workspace_tree.heading("last_accessed", text="最后访问")

        # 设置列宽
        self.workspace_tree.column("editor", width=100)
        self.workspace_tree.column("workspace_path", width=300)
        self.workspace_tree.column("size", width=80)
        self.workspace_tree.column("files_count", width=80)
        self.workspace_tree.column("last_accessed", width=120)

        # 添加滚动条
        ws_scrollbar = ttk.Scrollbar(ws_list_frame, orient=tk.VERTICAL, command=self.workspace_tree.yview)
        self.workspace_tree.configure(yscrollcommand=ws_scrollbar.set)

        self.workspace_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ws_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 工作区操作
        ws_ops_frame = ttk.LabelFrame(workspace_frame, text="工作区操作", padding=15)
        ws_ops_frame.pack(fill=tk.X, padx=10, pady=10)

        # 操作选项
        self.ws_cleanup_vars = {}

        ws_options = [
            ("clean_recent_files", "清理最近文件", "清除最近打开的文件记录"),
            ("clean_project_history", "清理项目历史", "清除项目打开历史"),
            ("clean_search_history", "清理搜索历史", "清除搜索和替换历史"),
            ("clean_workspace_settings", "清理工作区设置", "重置工作区特定设置"),
            ("clean_extension_data", "清理扩展数据", "清除工作区扩展数据"),
            ("backup_before_clean", "清理前备份", "在清理前自动创建备份")
        ]

        for var_name, title, description in ws_options:
            self.ws_cleanup_vars[var_name] = tk.BooleanVar()
            if var_name == "backup_before_clean":  # 默认启用备份
                self.ws_cleanup_vars[var_name].set(True)

            option_frame = ttk.Frame(ws_ops_frame)
            option_frame.pack(fill=tk.X, pady=2)

            ttk.Checkbutton(
                option_frame,
                text=title,
                variable=self.ws_cleanup_vars[var_name]
            ).pack(side=tk.LEFT)

            ttk.Label(
                option_frame,
                text=f" - {description}",
                foreground="gray"
            ).pack(side=tk.LEFT, padx=(10, 0))

        # 操作按钮
        ws_btn_frame = ttk.Frame(ws_ops_frame)
        ws_btn_frame.pack(pady=10)

        self.ws_clean_btn = ttk.Button(
            ws_btn_frame, text="💾 清理工作区",
            command=self.start_workspace_cleanup,
            width=15
        )
        self.ws_clean_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(
            ws_btn_frame, text="📋 预览清理",
            command=self.preview_workspace_cleanup,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            ws_btn_frame, text="💾 备份工作区",
            command=self.backup_selected_workspaces,
            width=15
        ).pack(side=tk.LEFT, padx=5)

    def create_log_tab(self):
        """创建日志标签页"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="📋 日志")
        
        # 日志控制
        control_frame = ttk.Frame(log_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            control_frame, text="🔄 刷新", 
            command=self.refresh_logs
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            control_frame, text="🗑️ 清空", 
            command=self.clear_logs
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            control_frame, text="💾 保存", 
            command=self.save_logs
        ).pack(side=tk.LEFT, padx=5)
        
        # 日志显示
        log_display_frame = ttk.LabelFrame(log_frame, text="操作日志", padding=10)
        log_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = tk.Text(
            log_display_frame, 
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg="#f8f9fa",
            fg="#212529"
        )
        
        log_scrollbar = ttk.Scrollbar(log_display_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置日志颜色
        self.log_text.tag_configure("ERROR", foreground="#dc3545")
        self.log_text.tag_configure("WARNING", foreground="#fd7e14")
        self.log_text.tag_configure("INFO", foreground="#0d6efd")
        self.log_text.tag_configure("SUCCESS", foreground="#198754")
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(
            self.status_bar, 
            text="就绪 - AUG 0.1 Enhanced Telemetry ID Modifier"
        )
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # 语言选择
        ttk.Label(self.status_bar, text="语言:").pack(side=tk.RIGHT, padx=5)
        lang_combo = ttk.Combobox(
            self.status_bar,
            textvariable=self.current_language,
            values=["zh_CN", "en_US"],
            state="readonly",
            width=8
        )
        lang_combo.pack(side=tk.RIGHT, padx=5)
        
    def load_initial_data(self):
        """加载初始数据"""
        # 设置回调
        self.log_manager.add_log_callback(self.on_log_message)
        self.telemetry_modifier.set_log_callback(self.on_log_message)
        self.telemetry_modifier.set_progress_callback(self.on_progress_update)

        # 加载字段数据
        self.load_field_categories()

        # 初始检测
        self.detect_editors()

        # 记录启动
        self.log_manager.info("AUG 0.1 简化GUI启动成功")
        
    # 事件处理方法
    def detect_editors(self):
        """检测编辑器"""
        self.status_label.config(text="正在检测编辑器...")

        def worker():
            try:
                self.log_manager.info("开始检测编辑器...")
                detection_results = self.editor_detector.detect_all_editors()

                # 在主线程中更新UI
                self.root.after(0, lambda: self.update_editor_list(detection_results))

            except Exception as e:
                self.log_manager.error(f"检测编辑器失败: {str(e)}")
                self.root.after(0, lambda: self.status_label.config(text="检测失败"))

        threading.Thread(target=worker, daemon=True).start()

    def update_editor_list(self, detection_results):
        """更新编辑器列表"""
        self.editor_listbox.delete(0, tk.END)

        installed_count = 0
        running_count = 0

        for editor_id, info in detection_results.items():
            icon = info.get("icon", "⚪")
            name = info.get("display_name", info.get("name", editor_id))
            status = "运行中" if info.get("running") else ("已安装" if info.get("installed") else "未找到")

            display_text = f"{icon} {name} - {status}"
            self.editor_listbox.insert(tk.END, display_text)

            if info.get("installed"):
                installed_count += 1
            if info.get("running"):
                running_count += 1

        status_text = f"检测完成 - {installed_count}个已安装, {running_count}个运行中"
        self.status_label.config(text=status_text)

        self.log_manager.success(f"编辑器检测完成: {status_text}")

    def run_quick_operation(self, mode):
        """快速操作"""
        self.operation_mode.set(mode)
        self.start_operation()

    def start_operation(self):
        """开始操作"""
        if self.is_operation_running:
            messagebox.showwarning("警告", "操作正在进行中，请等待完成")
            return

        mode = self.operation_mode.get()

        # 获取选择的字段（自定义模式）
        selected_fields = []
        if mode == "custom":
            selected_fields = self.get_selected_fields()
            if not selected_fields:
                messagebox.showerror("错误", "自定义模式下请先在'字段选择'标签页中选择要修改的字段")
                return

        # 获取已安装的编辑器
        installed_editors = self.editor_detector.get_installed_editors()
        if not installed_editors:
            messagebox.showerror("错误", "未找到已安装的编辑器")
            return

        # 检查运行中的编辑器
        running_editors = self.editor_detector.get_running_editors()
        if running_editors:
            editor_names = [self.editor_detector.get_editor_info(eid).get("name", eid) for eid in running_editors]
            if not messagebox.askyesno("警告",
                f"检测到以下编辑器正在运行:\n{chr(10).join(editor_names)}\n\n建议先关闭编辑器，是否继续？"):
                return

        # 显示操作确认
        mode_names = {
            "enhanced": "增强模式",
            "standard": "标准模式",
            "quick": "快速模式",
            "custom": "自定义模式"
        }

        # 构建确认信息
        field_count_text = ""
        if mode == "custom" and selected_fields:
            field_count_text = f"选择字段: {len(selected_fields)} 个\n"

        confirm_text = f"""即将执行 {mode_names.get(mode, mode)} 操作

目标编辑器: {len(installed_editors)} 个
{field_count_text}操作内容: 修改遥测标识符
备份: 自动创建

确定要继续吗？"""

        if not messagebox.askyesno("确认操作", confirm_text):
            return

        # 开始操作
        self.is_operation_running = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.progress_var.set(0)
        self.progress_label.config(text="正在准备操作...")

        # 异步执行
        self.telemetry_modifier.modify_telemetry_async(mode, installed_editors, selected_fields)

    def stop_operation(self):
        """停止操作"""
        if messagebox.askyesno("确认", "确定要停止当前操作吗？"):
            self.telemetry_modifier.stop_operation()
            self.on_operation_stopped()

    def on_operation_stopped(self):
        """操作停止处理"""
        self.is_operation_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.progress_label.config(text="操作已停止")

    def on_progress_update(self, progress, status):
        """进度更新回调"""
        self.root.after(0, lambda: self._update_progress_ui(progress, status))

    def _update_progress_ui(self, progress, status):
        """更新进度UI"""
        self.progress_var.set(progress)
        self.progress_label.config(text=status)

        if progress >= 100:
            self.on_operation_completed()

    def on_operation_completed(self):
        """操作完成处理"""
        self.is_operation_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

        messagebox.showinfo("完成", "遥测ID修改操作已完成！\n\n请重启相关编辑器以应用更改。")

        # 刷新编辑器列表
        self.detect_editors()

    def on_log_message(self, level, message):
        """日志消息回调"""
        self.root.after(0, lambda: self._add_log_message(level, message))

    def _add_log_message(self, level, message):
        """添加日志消息到UI"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_line = f"[{timestamp}] [{level}] {message}\n"

        self.log_text.insert(tk.END, log_line, level)
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > 1000:
            self.log_text.delete(1.0, "100.0")

    def refresh_logs(self):
        """刷新日志"""
        self.log_text.delete(1.0, tk.END)

        # 读取日志文件
        try:
            log_lines = self.log_manager.read_log_file(500)
            for line in log_lines:
                # 简单的级别检测
                level = "INFO"
                if "[ERROR]" in line:
                    level = "ERROR"
                elif "[WARNING]" in line:
                    level = "WARNING"
                elif "[SUCCESS]" in line:
                    level = "SUCCESS"

                self.log_text.insert(tk.END, line, level)
        except Exception as e:
            self.log_text.insert(tk.END, f"读取日志失败: {e}\n", "ERROR")

        self.log_text.see(tk.END)

    def clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空所有日志吗？"):
            self.log_text.delete(1.0, tk.END)
            self.log_manager.clear_log_file()

    def save_logs(self):
        """保存日志"""
        file_path = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".log",
            filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                content = self.log_text.get(1.0, tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", f"日志已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("失败", f"保存日志失败: {e}")

    def open_backup_directory(self):
        """打开备份目录"""
        import platform

        backup_dir = Path("backups")
        backup_dir.mkdir(exist_ok=True)

        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(backup_dir)])
            elif platform.system() == "Darwin":
                subprocess.run(["open", str(backup_dir)])
            else:
                subprocess.run(["xdg-open", str(backup_dir)])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开备份目录: {e}")

    # 字段选择相关方法
    def load_field_categories(self):
        """加载字段类别"""
        try:
            categories = self.config_manager.get_field_categories()

            # 清空现有项目
            self.categories_listbox.delete(0, tk.END)

            # 添加类别
            category_names = {
                "core_identifiers": "🔑 核心标识符",
                "installation_tracking": "📦 安装追踪",
                "user_tracking": "👤 用户追踪",
                "hardware_system": "💻 硬件系统",
                "network_location": "🌐 网络位置",
                "usage_behavior": "📊 使用行为",
                "performance_diagnostics": "⚡ 性能诊断",
                "ai_copilot": "🤖 AI和Copilot",
                "microsoft_ecosystem": "🏢 Microsoft生态",
                "privacy_compliance": "🔒 隐私合规"
            }

            for category in categories:
                display_name = category_names.get(category, category)
                self.categories_listbox.insert(tk.END, display_name)

        except Exception as e:
            self.log_manager.error(f"加载字段类别失败: {e}")

    def on_category_select(self, event):
        """字段类别选择事件"""
        selection = self.categories_listbox.curselection()
        if not selection:
            return

        index = selection[0]
        categories = self.config_manager.get_field_categories()

        if index < len(categories):
            category = categories[index]
            self.load_fields_by_category(category)

    def load_fields_by_category(self, category):
        """根据类别加载字段"""
        try:
            # 清空现有项目
            for item in self.fields_tree.get_children():
                self.fields_tree.delete(item)

            # 获取字段
            fields = self.config_manager.get_fields_by_category(category)

            for field_name, field_info in fields.items():
                field_type = field_info.get("type", "unknown")
                description = field_info.get("description", "")
                privacy_level = field_info.get("privacy_level", "medium")

                # 根据隐私级别设置颜色标签
                tags = [privacy_level]

                self.fields_tree.insert("", tk.END,
                                      values=(field_name, field_type, description, privacy_level),
                                      tags=tags)

            # 配置标签颜色
            self.fields_tree.tag_configure("high", foreground="#dc3545")
            self.fields_tree.tag_configure("medium", foreground="#fd7e14")
            self.fields_tree.tag_configure("low", foreground="#198754")

            self.update_field_selection_count()

        except Exception as e:
            self.log_manager.error(f"加载字段失败: {e}")

    def select_all_fields(self):
        """全选字段"""
        for item in self.fields_tree.get_children():
            self.fields_tree.selection_add(item)
        self.update_field_selection_count()

    def deselect_all_fields(self):
        """全不选字段"""
        self.fields_tree.selection_remove(self.fields_tree.selection())
        self.update_field_selection_count()

    def invert_field_selection(self):
        """反选字段"""
        all_items = self.fields_tree.get_children()
        selected_items = self.fields_tree.selection()

        # 取消当前选择
        self.fields_tree.selection_remove(selected_items)

        # 选择未选中的项目
        for item in all_items:
            if item not in selected_items:
                self.fields_tree.selection_add(item)

        self.update_field_selection_count()

    def select_core_fields(self):
        """选择核心字段"""
        self.deselect_all_fields()

        core_keywords = ["machineId", "deviceId", "sessionId", "instanceId"]

        for item in self.fields_tree.get_children():
            values = self.fields_tree.item(item, "values")
            if values:
                field_name = values[0]
                if any(keyword in field_name for keyword in core_keywords):
                    self.fields_tree.selection_add(item)

        self.update_field_selection_count()

    def select_high_privacy_fields(self):
        """选择高隐私级别字段"""
        self.deselect_all_fields()

        for item in self.fields_tree.get_children():
            values = self.fields_tree.item(item, "values")
            if values and values[3] == "high":  # privacy_level
                self.fields_tree.selection_add(item)

        self.update_field_selection_count()

    def clear_field_selection(self):
        """清除字段选择"""
        self.deselect_all_fields()

    def on_field_selection_change(self, event):
        """字段选择改变事件"""
        self.update_field_selection_count()

    def update_field_selection_count(self):
        """更新字段选择计数"""
        try:
            selected_count = len(self.fields_tree.selection())
            self.field_selection_label.config(text=f"已选择: {selected_count} 个字段")
        except:
            pass

    def get_selected_fields(self):
        """获取选择的字段"""
        selected = []

        for item in self.fields_tree.selection():
            values = self.fields_tree.item(item, "values")
            if values:
                field_name = values[0]
                selected.append(field_name)

        return selected

    # 清理功能相关方法
    def select_deep_cleanup(self):
        """选择深度清理"""
        for var in self.cleanup_vars.values():
            var.set(True)

    def select_standard_cleanup(self):
        """选择标准清理"""
        self.clear_cleanup_selection()

        standard_options = ["clear_history", "clear_cache", "clear_logs", "clear_telemetry_cache"]
        for option in standard_options:
            if option in self.cleanup_vars:
                self.cleanup_vars[option].set(True)

    def select_privacy_cleanup(self):
        """选择隐私清理"""
        self.clear_cleanup_selection()

        privacy_options = ["clear_telemetry_cache", "clear_crash_reports", "clear_logs"]
        for option in privacy_options:
            if option in self.cleanup_vars:
                self.cleanup_vars[option].set(True)

    def clear_cleanup_selection(self):
        """清除清理选择"""
        for var in self.cleanup_vars.values():
            var.set(False)

    def preview_cleanup(self):
        """预览清理操作"""
        selected_options = []
        for option, var in self.cleanup_vars.items():
            if var.get():
                selected_options.append(option)

        if not selected_options:
            messagebox.showinfo("预览", "未选择任何清理选项")
            return

        # 获取将要清理的路径
        cleanup_paths = self.get_cleanup_paths(selected_options)

        preview_text = "将要清理的内容:\n\n"
        total_size = 0

        for option, paths in cleanup_paths.items():
            option_names = {
                "clear_history": "历史记录",
                "clear_cache": "缓存文件",
                "clear_settings": "用户设置",
                "clear_extensions": "扩展数据",
                "clear_workspace": "工作区数据",
                "clear_logs": "日志文件",
                "clear_crash_reports": "崩溃报告",
                "clear_telemetry_cache": "遥测缓存"
            }

            preview_text += f"📁 {option_names.get(option, option)}:\n"

            for path in paths:
                if os.path.exists(path):
                    try:
                        if os.path.isfile(path):
                            size = os.path.getsize(path)
                            total_size += size
                            preview_text += f"  📄 {path} ({self.format_size(size)})\n"
                        else:
                            dir_size = self.get_directory_size(path)
                            total_size += dir_size
                            preview_text += f"  📁 {path} ({self.format_size(dir_size)})\n"
                    except:
                        preview_text += f"  ❓ {path} (无法访问)\n"
                else:
                    preview_text += f"  ❌ {path} (不存在)\n"
            preview_text += "\n"

        preview_text += f"总计大小: {self.format_size(total_size)}"

        # 显示预览窗口
        preview_window = tk.Toplevel(self.root)
        preview_window.title("清理预览")
        preview_window.geometry("600x500")
        preview_window.transient(self.root)
        preview_window.grab_set()

        text_widget = tk.Text(preview_window, wrap=tk.WORD, padx=10, pady=10)
        scrollbar = ttk.Scrollbar(preview_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.insert(1.0, preview_text)
        text_widget.config(state=tk.DISABLED)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 关闭按钮
        ttk.Button(preview_window, text="关闭", command=preview_window.destroy).pack(pady=10)

    def start_cleanup(self):
        """开始清理操作"""
        selected_options = []
        for option, var in self.cleanup_vars.items():
            if var.get():
                selected_options.append(option)

        if not selected_options:
            messagebox.showwarning("警告", "请先选择要清理的项目")
            return

        # 确认对话框
        option_names = {
            "clear_history": "历史记录",
            "clear_cache": "缓存文件",
            "clear_settings": "用户设置",
            "clear_extensions": "扩展数据",
            "clear_workspace": "工作区数据",
            "clear_logs": "日志文件",
            "clear_crash_reports": "崩溃报告",
            "clear_telemetry_cache": "遥测缓存"
        }

        selected_names = [option_names.get(opt, opt) for opt in selected_options]

        confirm_text = f"""⚠️ 确认清理操作

将要清理以下内容:
{chr(10).join('• ' + name for name in selected_names)}

⚠️ 警告: 此操作将永久删除选定的数据，无法撤销！

确定要继续吗？"""

        if not messagebox.askyesno("确认清理", confirm_text):
            return

        # 检查编辑器是否运行
        running_editors = self.editor_detector.get_running_editors()
        if running_editors:
            editor_names = [self.editor_detector.get_editor_info(eid).get("name", eid) for eid in running_editors]
            if not messagebox.askyesno("编辑器运行中",
                f"检测到以下编辑器正在运行:\n{chr(10).join(editor_names)}\n\n建议先关闭编辑器，是否继续？"):
                return

        # 开始清理
        self.cleanup_btn.config(state=tk.DISABLED)
        self.cleanup_progress_var.set(0)
        self.cleanup_status_label.config(text="正在清理...")

        # 异步执行清理
        def cleanup_worker():
            try:
                self.perform_cleanup(selected_options)
            except Exception as e:
                self.log_manager.error(f"清理操作失败: {e}")
                self.root.after(0, lambda: messagebox.showerror("清理失败", f"清理操作失败: {e}"))
            finally:
                self.root.after(0, self.cleanup_completed)

        threading.Thread(target=cleanup_worker, daemon=True).start()

    def get_cleanup_paths(self, selected_options):
        """获取清理路径"""
        cleanup_paths = {}

        # 获取编辑器路径
        editors = ["Code", "Cursor", "VSCodium", "Code - Insiders", "Code - OSS"]

        for option in selected_options:
            paths = []

            if option == "clear_history":
                for editor in editors:
                    base_path = os.path.expandvars(f"%APPDATA%\\{editor}\\User")
                    paths.extend([
                        os.path.join(base_path, "History"),
                        os.path.join(base_path, "globalStorage", "storage.json"),
                        os.path.join(base_path, "workspaceStorage")
                    ])

            elif option == "clear_cache":
                for editor in editors:
                    base_path = os.path.expandvars(f"%APPDATA%\\{editor}")
                    paths.extend([
                        os.path.join(base_path, "CachedData"),
                        os.path.join(base_path, "logs"),
                        os.path.join(base_path, "User", "workspaceStorage")
                    ])

            elif option == "clear_settings":
                for editor in editors:
                    base_path = os.path.expandvars(f"%APPDATA%\\{editor}\\User")
                    paths.extend([
                        os.path.join(base_path, "settings.json"),
                        os.path.join(base_path, "keybindings.json")
                    ])

            elif option == "clear_extensions":
                for editor in editors:
                    base_path = os.path.expandvars(f"%APPDATA%\\{editor}")
                    paths.append(os.path.join(base_path, "extensions"))

            elif option == "clear_workspace":
                for editor in editors:
                    base_path = os.path.expandvars(f"%APPDATA%\\{editor}\\User")
                    paths.append(os.path.join(base_path, "workspaceStorage"))

            elif option == "clear_logs":
                for editor in editors:
                    base_path = os.path.expandvars(f"%APPDATA%\\{editor}")
                    paths.append(os.path.join(base_path, "logs"))

            elif option == "clear_crash_reports":
                for editor in editors:
                    base_path = os.path.expandvars(f"%APPDATA%\\{editor}")
                    paths.append(os.path.join(base_path, "CrashReports"))

            elif option == "clear_telemetry_cache":
                for editor in editors:
                    base_path = os.path.expandvars(f"%APPDATA%\\{editor}")
                    paths.extend([
                        os.path.join(base_path, "User", "globalStorage", "storage.json"),
                        os.path.join(base_path, "telemetry")
                    ])

            cleanup_paths[option] = paths

        return cleanup_paths

    def perform_cleanup(self, selected_options):
        """执行清理操作"""
        cleanup_paths = self.get_cleanup_paths(selected_options)
        total_items = sum(len(paths) for paths in cleanup_paths.values())
        processed_items = 0

        for option, paths in cleanup_paths.items():
            option_names = {
                "clear_history": "历史记录",
                "clear_cache": "缓存文件",
                "clear_settings": "用户设置",
                "clear_extensions": "扩展数据",
                "clear_workspace": "工作区数据",
                "clear_logs": "日志文件",
                "clear_crash_reports": "崩溃报告",
                "clear_telemetry_cache": "遥测缓存"
            }

            option_name = option_names.get(option, option)
            self.log_manager.info(f"开始清理 {option_name}...")

            for path in paths:
                try:
                    if os.path.exists(path):
                        if os.path.isfile(path):
                            os.remove(path)
                            self.log_manager.success(f"已删除文件: {path}")
                        elif os.path.isdir(path):
                            import shutil
                            shutil.rmtree(path)
                            self.log_manager.success(f"已删除目录: {path}")
                    else:
                        self.log_manager.info(f"路径不存在，跳过: {path}")

                except Exception as e:
                    self.log_manager.error(f"删除失败 {path}: {e}")

                processed_items += 1
                progress = (processed_items / total_items) * 100

                # 更新进度
                self.root.after(0, lambda p=progress, s=f"正在清理 {option_name}...":
                               self.update_cleanup_progress(p, s))

        self.log_manager.success("清理操作完成")

    def update_cleanup_progress(self, progress, status):
        """更新清理进度"""
        self.cleanup_progress_var.set(progress)
        self.cleanup_status_label.config(text=status)

    def cleanup_completed(self):
        """清理完成处理"""
        self.cleanup_btn.config(state=tk.NORMAL)
        self.cleanup_progress_var.set(100)
        self.cleanup_status_label.config(text="清理完成")

        messagebox.showinfo("清理完成", "清理操作已完成！\n\n建议重启相关编辑器以确保更改生效。")

    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"

    def get_directory_size(self, directory):
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except:
                        pass
        except:
            pass
        return total_size

    # 数据库管理相关方法
    def scan_databases(self):
        """扫描数据库"""
        self.db_status_label.config(text="正在扫描数据库...")

        def worker():
            try:
                self.log_manager.info("开始扫描数据库文件...")
                databases = self.database_manager.scan_databases()

                # 在主线程中更新UI
                self.root.after(0, lambda: self.update_database_list(databases))

            except Exception as e:
                self.log_manager.error(f"扫描数据库失败: {str(e)}")
                self.root.after(0, lambda: self.db_status_label.config(text="扫描失败"))

        threading.Thread(target=worker, daemon=True).start()

    def update_database_list(self, databases):
        """更新数据库列表"""
        # 清空现有项目
        for item in self.database_tree.get_children():
            self.database_tree.delete(item)

        # 添加数据库信息
        for db_info in databases:
            path = db_info.get("path", "")
            size = self.database_manager.format_size(db_info.get("size", 0))
            total_records = str(db_info.get("total_records", 0))
            augment_records = str(db_info.get("augment_records", 0))
            last_modified = db_info.get("last_modified", datetime.min).strftime("%Y-%m-%d %H:%M")

            # 根据是否有augment记录设置标签
            tags = []
            if db_info.get("augment_records", 0) > 0:
                tags.append("has_augment")

            self.database_tree.insert("", tk.END,
                                    values=(path, size, total_records, augment_records, last_modified),
                                    tags=tags)

        # 配置标签颜色
        self.database_tree.tag_configure("has_augment", foreground="#dc3545")

        self.db_status_label.config(text=f"扫描完成 - 发现 {len(databases)} 个数据库")
        self.log_manager.success(f"数据库扫描完成，发现 {len(databases)} 个数据库文件")

    def refresh_database_list(self):
        """刷新数据库列表"""
        self.scan_databases()

    def preview_database_cleanup(self):
        """预览数据库清理"""
        selected_items = self.database_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要预览的数据库")
            return

        selected_options = []
        for option, var in self.db_cleanup_vars.items():
            if var.get():
                selected_options.append(option)

        if not selected_options:
            messagebox.showinfo("预览", "未选择任何清理选项")
            return

        preview_text = "数据库清理预览:\n\n"

        for item in selected_items:
            values = self.database_tree.item(item, "values")
            if values:
                db_path = values[0]
                preview_text += f"数据库: {db_path}\n"

                for option in selected_options:
                    option_names = {
                        "clean_augment_records": "清理包含'augment'的记录",
                        "clean_telemetry_data": "清理遥测数据",
                        "clean_usage_stats": "清理使用统计",
                        "clean_error_reports": "清理错误报告",
                        "vacuum_database": "压缩数据库",
                        "backup_before_clean": "清理前备份"
                    }
                    preview_text += f"  ✓ {option_names.get(option, option)}\n"

                preview_text += "\n"

        # 显示预览窗口
        preview_window = tk.Toplevel(self.root)
        preview_window.title("数据库清理预览")
        preview_window.geometry("600x400")
        preview_window.transient(self.root)
        preview_window.grab_set()

        text_widget = tk.Text(preview_window, wrap=tk.WORD, padx=10, pady=10)
        scrollbar = ttk.Scrollbar(preview_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.insert(1.0, preview_text)
        text_widget.config(state=tk.DISABLED)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        ttk.Button(preview_window, text="关闭", command=preview_window.destroy).pack(pady=10)

    def start_database_cleanup(self):
        """开始数据库清理"""
        selected_items = self.database_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要清理的数据库")
            return

        selected_options = []
        for option, var in self.db_cleanup_vars.items():
            if var.get():
                selected_options.append(option)

        if not selected_options:
            messagebox.showwarning("警告", "请先选择要执行的清理操作")
            return

        # 确认对话框
        if not messagebox.askyesno("确认清理",
            f"确定要清理选定的 {len(selected_items)} 个数据库吗？\n\n此操作可能无法撤销！"):
            return

        # 开始清理
        self.db_clean_btn.config(state=tk.DISABLED)
        self.db_progress_var.set(0)
        self.db_status_label.config(text="正在清理数据库...")

        def cleanup_worker():
            try:
                self.perform_database_cleanup(selected_items, selected_options)
            except Exception as e:
                self.log_manager.error(f"数据库清理失败: {e}")
                self.root.after(0, lambda: messagebox.showerror("清理失败", f"数据库清理失败: {e}"))
            finally:
                self.root.after(0, self.database_cleanup_completed)

        threading.Thread(target=cleanup_worker, daemon=True).start()

    def perform_database_cleanup(self, selected_items, selected_options):
        """执行数据库清理"""
        total_items = len(selected_items)
        processed_items = 0

        for item in selected_items:
            values = self.database_tree.item(item, "values")
            if not values:
                continue

            db_path = values[0]
            self.log_manager.info(f"开始清理数据库: {db_path}")

            # 备份数据库
            if self.db_cleanup_vars.get("backup_before_clean", tk.BooleanVar()).get():
                backup_path = self.database_manager.backup_database(db_path)
                if backup_path:
                    self.log_manager.success(f"数据库已备份到: {backup_path}")
                else:
                    self.log_manager.error(f"数据库备份失败: {db_path}")

            # 执行清理操作
            for option in selected_options:
                if option == "backup_before_clean":
                    continue

                try:
                    if option == "clean_augment_records":
                        result = self.database_manager.clean_augment_records(db_path)
                        if result["success"]:
                            self.log_manager.success(f"清理augment记录: {result['deleted_records']}条")
                        else:
                            self.log_manager.error(f"清理augment记录失败: {result.get('error', '未知错误')}")

                    elif option == "clean_telemetry_data":
                        result = self.database_manager.clean_telemetry_data(db_path)
                        if result["success"]:
                            self.log_manager.success(f"清理遥测数据: {result['deleted_records']}条")
                        else:
                            self.log_manager.error(f"清理遥测数据失败: {result.get('error', '未知错误')}")

                    elif option == "vacuum_database":
                        if self.database_manager.vacuum_database(db_path):
                            self.log_manager.success(f"数据库压缩完成: {db_path}")
                        else:
                            self.log_manager.error(f"数据库压缩失败: {db_path}")

                except Exception as e:
                    self.log_manager.error(f"执行清理操作失败 {option}: {e}")

            processed_items += 1
            progress = (processed_items / total_items) * 100

            # 更新进度
            self.root.after(0, lambda p=progress: self.db_progress_var.set(p))

        self.log_manager.success("数据库清理操作完成")

    def database_cleanup_completed(self):
        """数据库清理完成处理"""
        self.db_clean_btn.config(state=tk.NORMAL)
        self.db_progress_var.set(100)
        self.db_status_label.config(text="清理完成")

        messagebox.showinfo("清理完成", "数据库清理操作已完成！")

        # 刷新数据库列表
        self.scan_databases()

    def backup_selected_databases(self):
        """备份选定的数据库"""
        selected_items = self.database_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要备份的数据库")
            return

        success_count = 0

        for item in selected_items:
            values = self.database_tree.item(item, "values")
            if values:
                db_path = values[0]
                backup_path = self.database_manager.backup_database(db_path)
                if backup_path:
                    success_count += 1
                    self.log_manager.success(f"数据库已备份: {backup_path}")
                else:
                    self.log_manager.error(f"数据库备份失败: {db_path}")

        messagebox.showinfo("备份完成", f"成功备份 {success_count} 个数据库文件")

    # 工作区管理相关方法
    def scan_workspaces(self):
        """扫描工作区"""
        def worker():
            try:
                self.log_manager.info("开始扫描工作区...")
                workspaces = self.workspace_manager.scan_workspaces()

                # 在主线程中更新UI
                self.root.after(0, lambda: self.update_workspace_list(workspaces))

            except Exception as e:
                self.log_manager.error(f"扫描工作区失败: {str(e)}")

        threading.Thread(target=worker, daemon=True).start()

    def update_workspace_list(self, workspaces):
        """更新工作区列表"""
        # 清空现有项目
        for item in self.workspace_tree.get_children():
            self.workspace_tree.delete(item)

        # 添加工作区信息
        for ws_info in workspaces:
            editor_name = ws_info.get("editor_name", "")
            workspace_path = ws_info.get("workspace_path", "")
            size = self.workspace_manager.format_size(ws_info.get("size", 0))
            file_count = str(ws_info.get("file_count", 0))
            last_accessed = ws_info.get("last_accessed", datetime.min).strftime("%Y-%m-%d %H:%M")

            self.workspace_tree.insert("", tk.END,
                                     values=(editor_name, workspace_path, size, file_count, last_accessed))

        self.log_manager.success(f"工作区扫描完成，发现 {len(workspaces)} 个工作区")

    def refresh_workspace_list(self):
        """刷新工作区列表"""
        self.scan_workspaces()

    def preview_workspace_cleanup(self):
        """预览工作区清理"""
        selected_items = self.workspace_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要预览的工作区")
            return

        selected_options = []
        for option, var in self.ws_cleanup_vars.items():
            if var.get():
                selected_options.append(option)

        if not selected_options:
            messagebox.showinfo("预览", "未选择任何清理选项")
            return

        preview_text = "工作区清理预览:\n\n"

        for item in selected_items:
            values = self.workspace_tree.item(item, "values")
            if values:
                editor_name = values[0]
                workspace_path = values[1]
                preview_text += f"编辑器: {editor_name}\n"
                preview_text += f"工作区: {workspace_path}\n"

                for option in selected_options:
                    option_names = {
                        "clean_recent_files": "清理最近文件",
                        "clean_project_history": "清理项目历史",
                        "clean_search_history": "清理搜索历史",
                        "clean_workspace_settings": "清理工作区设置",
                        "clean_extension_data": "清理扩展数据",
                        "backup_before_clean": "清理前备份"
                    }
                    preview_text += f"  ✓ {option_names.get(option, option)}\n"

                preview_text += "\n"

        # 显示预览窗口
        preview_window = tk.Toplevel(self.root)
        preview_window.title("工作区清理预览")
        preview_window.geometry("600x400")
        preview_window.transient(self.root)
        preview_window.grab_set()

        text_widget = tk.Text(preview_window, wrap=tk.WORD, padx=10, pady=10)
        scrollbar = ttk.Scrollbar(preview_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.insert(1.0, preview_text)
        text_widget.config(state=tk.DISABLED)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        ttk.Button(preview_window, text="关闭", command=preview_window.destroy).pack(pady=10)

    def start_workspace_cleanup(self):
        """开始工作区清理"""
        selected_items = self.workspace_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要清理的工作区")
            return

        selected_options = []
        for option, var in self.ws_cleanup_vars.items():
            if var.get():
                selected_options.append(option)

        if not selected_options:
            messagebox.showwarning("警告", "请先选择要执行的清理操作")
            return

        # 确认对话框
        if not messagebox.askyesno("确认清理",
            f"确定要清理选定的 {len(selected_items)} 个工作区吗？\n\n此操作可能无法撤销！"):
            return

        # 开始清理
        self.ws_clean_btn.config(state=tk.DISABLED)

        def cleanup_worker():
            try:
                self.perform_workspace_cleanup(selected_items, selected_options)
            except Exception as e:
                self.log_manager.error(f"工作区清理失败: {e}")
                self.root.after(0, lambda: messagebox.showerror("清理失败", f"工作区清理失败: {e}"))
            finally:
                self.root.after(0, self.workspace_cleanup_completed)

        threading.Thread(target=cleanup_worker, daemon=True).start()

    def perform_workspace_cleanup(self, selected_items, selected_options):
        """执行工作区清理"""
        # 这里实现具体的工作区清理逻辑
        self.log_manager.info("开始执行工作区清理...")

        for item in selected_items:
            values = self.workspace_tree.item(item, "values")
            if not values:
                continue

            editor_name = values[0]
            workspace_path = values[1]

            self.log_manager.info(f"清理工作区: {editor_name} - {workspace_path}")

            # 这里可以添加具体的清理逻辑
            # 由于工作区清理比较复杂，这里只是示例

        self.log_manager.success("工作区清理操作完成")

    def workspace_cleanup_completed(self):
        """工作区清理完成处理"""
        self.ws_clean_btn.config(state=tk.NORMAL)

        messagebox.showinfo("清理完成", "工作区清理操作已完成！")

        # 刷新工作区列表
        self.scan_workspaces()

    def backup_selected_workspaces(self):
        """备份选定的工作区"""
        selected_items = self.workspace_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要备份的工作区")
            return

        success_count = 0

        # 这里实现工作区备份逻辑
        for item in selected_items:
            values = self.workspace_tree.item(item, "values")
            if values:
                workspace_path = values[1]
                # 实现备份逻辑
                success_count += 1
                self.log_manager.success(f"工作区已备份: {workspace_path}")

        messagebox.showinfo("备份完成", f"成功备份 {success_count} 个工作区")

    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """关闭应用程序"""
        if messagebox.askokcancel("退出", "确定要退出 AUG 0.1 吗？"):
            self.log_manager.info("AUG 0.1 简化GUI正在关闭...")
            self.root.destroy()


if __name__ == "__main__":
    try:
        app = AUGSimpleGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"应用程序启动失败：{str(e)}")
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
