#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AUG 0.1 Enhanced Telemetry ID Modifier - 简化GUI版本
不依赖外部库的图形用户界面版本

Author: AUG Team
Version: 0.1.0
License: MIT
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import sys
import subprocess
import threading
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
try:
    from gui_modules.language_manager import LanguageManager
    from gui_modules.config_manager import ConfigManager
    from gui_modules.editor_detector import EditorDetector
    from gui_modules.telemetry_modifier import TelemetryModifier
    from gui_modules.backup_manager import BackupManager
    from gui_modules.log_manager import LogManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必需的模块文件存在")
    sys.exit(1)

class AUGSimpleGUI:
    """AUG 0.1 简化GUI主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        self.setup_managers()
        self.setup_window()
        self.setup_variables()
        self.setup_ui()
        self.load_initial_data()
        
    def setup_managers(self):
        """初始化管理器"""
        try:
            self.lang_manager = LanguageManager()
            self.config_manager = ConfigManager()
            self.editor_detector = EditorDetector()
            self.telemetry_modifier = TelemetryModifier()
            self.backup_manager = BackupManager()
            self.log_manager = LogManager()
        except Exception as e:
            messagebox.showerror("初始化错误", f"管理器初始化失败: {e}")
            sys.exit(1)
        
    def setup_window(self):
        """设置主窗口"""
        self.root = tk.Tk()
        self.root.title("AUG 0.1 Enhanced Telemetry ID Modifier")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果存在）
        try:
            if os.path.exists("assets/aug_icon.ico"):
                self.root.iconbitmap("assets/aug_icon.ico")
        except:
            pass
        
        # 居中显示窗口
        self.center_window()
        
        # 设置样式
        self.setup_styles()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_styles(self):
        """设置样式"""
        self.style = ttk.Style()
        
        # 尝试设置现代主题
        available_themes = self.style.theme_names()
        if 'clam' in available_themes:
            self.style.theme_use('clam')
        elif 'vista' in available_themes:
            self.style.theme_use('vista')
        
        # 自定义样式
        self.style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        self.style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        self.style.configure('Success.TButton', foreground='green')
        self.style.configure('Warning.TButton', foreground='orange')
        self.style.configure('Danger.TButton', foreground='red')
        
    def setup_variables(self):
        """设置变量"""
        self.current_language = tk.StringVar(value="zh_CN")
        self.operation_mode = tk.StringVar(value="enhanced")
        self.is_operation_running = False
        
    def setup_ui(self):
        """设置用户界面"""
        self.create_main_frame()
        self.create_status_bar()
        
    def create_main_frame(self):
        """创建主框架"""
        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = ttk.Frame(main_container)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(
            title_frame, 
            text="AUG 0.1 Enhanced Telemetry ID Modifier",
            style='Title.TLabel'
        ).pack()
        
        ttk.Label(
            title_frame,
            text="专业的代码编辑器隐私保护工具",
            font=('Arial', 10)
        ).pack()
        
        # 创建Notebook
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建标签页
        self.create_dashboard_tab()
        self.create_operation_tab()
        self.create_log_tab()
        
    def create_dashboard_tab(self):
        """创建仪表板标签页"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="🏠 仪表板")
        
        # 欢迎信息
        welcome_frame = ttk.LabelFrame(dashboard_frame, text="欢迎使用", padding=15)
        welcome_frame.pack(fill=tk.X, padx=10, pady=10)
        
        welcome_text = """AUG 0.1 是一个专业的隐私保护工具，用于修改代码编辑器的遥测标识符。

✓ 支持 VS Code、Cursor、VSCodium 等主流编辑器
✓ 提供 50+ 种遥测字段的修改
✓ 4种操作模式：增强、标准、快速、自定义
✓ 自动备份和恢复功能
✓ 实时操作日志监控"""
        
        ttk.Label(welcome_frame, text=welcome_text, justify=tk.LEFT).pack(anchor=tk.W)
        
        # 快速操作
        quick_frame = ttk.LabelFrame(dashboard_frame, text="快速操作", padding=15)
        quick_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 按钮网格
        btn_frame = ttk.Frame(quick_frame)
        btn_frame.pack()
        
        # 第一行
        row1 = ttk.Frame(btn_frame)
        row1.pack(pady=5)
        
        ttk.Button(
            row1, text="🔍 检测编辑器", 
            command=self.detect_editors,
            width=20
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row1, text="⚡ 快速修改", 
            command=lambda: self.run_quick_operation("quick"),
            width=20
        ).pack(side=tk.LEFT, padx=5)
        
        # 第二行
        row2 = ttk.Frame(btn_frame)
        row2.pack(pady=5)
        
        ttk.Button(
            row2, text="🛡️ 增强保护", 
            command=lambda: self.run_quick_operation("enhanced"),
            width=20
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row2, text="📁 备份管理", 
            command=self.open_backup_directory,
            width=20
        ).pack(side=tk.LEFT, padx=5)
        
        # 编辑器状态
        status_frame = ttk.LabelFrame(dashboard_frame, text="编辑器状态", padding=15)
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 编辑器列表
        self.editor_listbox = tk.Listbox(status_frame, height=8)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.editor_listbox.yview)
        self.editor_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.editor_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_operation_tab(self):
        """创建操作标签页"""
        operation_frame = ttk.Frame(self.notebook)
        self.notebook.add(operation_frame, text="⚙️ 操作")
        
        # 操作模式选择
        mode_frame = ttk.LabelFrame(operation_frame, text="操作模式", padding=15)
        mode_frame.pack(fill=tk.X, padx=10, pady=10)
        
        modes = [
            ("enhanced", "🛡️ 增强模式", "修改所有50+遥测字段，最大隐私保护"),
            ("standard", "⚖️ 标准模式", "修改10-15个常用字段，平衡保护"),
            ("quick", "⚡ 快速模式", "只修改2-3个核心字段，快速操作"),
            ("custom", "🔧 自定义模式", "用户完全自定义选择")
        ]
        
        for mode_id, title, desc in modes:
            frame = ttk.Frame(mode_frame)
            frame.pack(fill=tk.X, pady=2)
            
            ttk.Radiobutton(
                frame,
                text=title,
                variable=self.operation_mode,
                value=mode_id
            ).pack(side=tk.LEFT)
            
            ttk.Label(frame, text=desc, foreground="gray").pack(side=tk.LEFT, padx=(10, 0))
            
        # 操作控制
        control_frame = ttk.LabelFrame(operation_frame, text="操作控制", padding=15)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack()
        
        self.start_btn = ttk.Button(
            btn_frame, text="🚀 开始操作", 
            command=self.start_operation,
            width=15
        )
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(
            btn_frame, text="⏹️ 停止操作", 
            command=self.stop_operation,
            width=15,
            state=tk.DISABLED
        )
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        # 进度显示
        progress_frame = ttk.LabelFrame(operation_frame, text="操作进度", padding=15)
        progress_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame, 
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        self.progress_label = ttk.Label(progress_frame, text="等待操作...")
        self.progress_label.pack()
        
    def create_log_tab(self):
        """创建日志标签页"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="📋 日志")
        
        # 日志控制
        control_frame = ttk.Frame(log_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            control_frame, text="🔄 刷新", 
            command=self.refresh_logs
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            control_frame, text="🗑️ 清空", 
            command=self.clear_logs
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            control_frame, text="💾 保存", 
            command=self.save_logs
        ).pack(side=tk.LEFT, padx=5)
        
        # 日志显示
        log_display_frame = ttk.LabelFrame(log_frame, text="操作日志", padding=10)
        log_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = tk.Text(
            log_display_frame, 
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg="#f8f9fa",
            fg="#212529"
        )
        
        log_scrollbar = ttk.Scrollbar(log_display_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置日志颜色
        self.log_text.tag_configure("ERROR", foreground="#dc3545")
        self.log_text.tag_configure("WARNING", foreground="#fd7e14")
        self.log_text.tag_configure("INFO", foreground="#0d6efd")
        self.log_text.tag_configure("SUCCESS", foreground="#198754")
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(
            self.status_bar, 
            text="就绪 - AUG 0.1 Enhanced Telemetry ID Modifier"
        )
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # 语言选择
        ttk.Label(self.status_bar, text="语言:").pack(side=tk.RIGHT, padx=5)
        lang_combo = ttk.Combobox(
            self.status_bar,
            textvariable=self.current_language,
            values=["zh_CN", "en_US"],
            state="readonly",
            width=8
        )
        lang_combo.pack(side=tk.RIGHT, padx=5)
        
    def load_initial_data(self):
        """加载初始数据"""
        # 设置回调
        self.log_manager.add_log_callback(self.on_log_message)
        self.telemetry_modifier.set_log_callback(self.on_log_message)
        self.telemetry_modifier.set_progress_callback(self.on_progress_update)
        
        # 初始检测
        self.detect_editors()
        
        # 记录启动
        self.log_manager.info("AUG 0.1 简化GUI启动成功")
        
    # 事件处理方法
    def detect_editors(self):
        """检测编辑器"""
        self.status_label.config(text="正在检测编辑器...")

        def worker():
            try:
                self.log_manager.info("开始检测编辑器...")
                detection_results = self.editor_detector.detect_all_editors()

                # 在主线程中更新UI
                self.root.after(0, lambda: self.update_editor_list(detection_results))

            except Exception as e:
                self.log_manager.error(f"检测编辑器失败: {str(e)}")
                self.root.after(0, lambda: self.status_label.config(text="检测失败"))

        threading.Thread(target=worker, daemon=True).start()

    def update_editor_list(self, detection_results):
        """更新编辑器列表"""
        self.editor_listbox.delete(0, tk.END)

        installed_count = 0
        running_count = 0

        for editor_id, info in detection_results.items():
            icon = info.get("icon", "⚪")
            name = info.get("display_name", info.get("name", editor_id))
            status = "运行中" if info.get("running") else ("已安装" if info.get("installed") else "未找到")

            display_text = f"{icon} {name} - {status}"
            self.editor_listbox.insert(tk.END, display_text)

            if info.get("installed"):
                installed_count += 1
            if info.get("running"):
                running_count += 1

        status_text = f"检测完成 - {installed_count}个已安装, {running_count}个运行中"
        self.status_label.config(text=status_text)

        self.log_manager.success(f"编辑器检测完成: {status_text}")

    def run_quick_operation(self, mode):
        """快速操作"""
        self.operation_mode.set(mode)
        self.start_operation()

    def start_operation(self):
        """开始操作"""
        if self.is_operation_running:
            messagebox.showwarning("警告", "操作正在进行中，请等待完成")
            return

        mode = self.operation_mode.get()

        # 获取已安装的编辑器
        installed_editors = self.editor_detector.get_installed_editors()
        if not installed_editors:
            messagebox.showerror("错误", "未找到已安装的编辑器")
            return

        # 检查运行中的编辑器
        running_editors = self.editor_detector.get_running_editors()
        if running_editors:
            editor_names = [self.editor_detector.get_editor_info(eid).get("name", eid) for eid in running_editors]
            if not messagebox.askyesno("警告",
                f"检测到以下编辑器正在运行:\n{chr(10).join(editor_names)}\n\n建议先关闭编辑器，是否继续？"):
                return

        # 显示操作确认
        mode_names = {
            "enhanced": "增强模式",
            "standard": "标准模式",
            "quick": "快速模式",
            "custom": "自定义模式"
        }

        confirm_text = f"""即将执行 {mode_names.get(mode, mode)} 操作

目标编辑器: {len(installed_editors)} 个
操作内容: 修改遥测标识符
备份: 自动创建

确定要继续吗？"""

        if not messagebox.askyesno("确认操作", confirm_text):
            return

        # 开始操作
        self.is_operation_running = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.progress_var.set(0)
        self.progress_label.config(text="正在准备操作...")

        # 异步执行
        self.telemetry_modifier.modify_telemetry_async(mode, installed_editors)

    def stop_operation(self):
        """停止操作"""
        if messagebox.askyesno("确认", "确定要停止当前操作吗？"):
            self.telemetry_modifier.stop_operation()
            self.on_operation_stopped()

    def on_operation_stopped(self):
        """操作停止处理"""
        self.is_operation_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.progress_label.config(text="操作已停止")

    def on_progress_update(self, progress, status):
        """进度更新回调"""
        self.root.after(0, lambda: self._update_progress_ui(progress, status))

    def _update_progress_ui(self, progress, status):
        """更新进度UI"""
        self.progress_var.set(progress)
        self.progress_label.config(text=status)

        if progress >= 100:
            self.on_operation_completed()

    def on_operation_completed(self):
        """操作完成处理"""
        self.is_operation_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

        messagebox.showinfo("完成", "遥测ID修改操作已完成！\n\n请重启相关编辑器以应用更改。")

        # 刷新编辑器列表
        self.detect_editors()

    def on_log_message(self, level, message):
        """日志消息回调"""
        self.root.after(0, lambda: self._add_log_message(level, message))

    def _add_log_message(self, level, message):
        """添加日志消息到UI"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_line = f"[{timestamp}] [{level}] {message}\n"

        self.log_text.insert(tk.END, log_line, level)
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > 1000:
            self.log_text.delete(1.0, "100.0")

    def refresh_logs(self):
        """刷新日志"""
        self.log_text.delete(1.0, tk.END)

        # 读取日志文件
        try:
            log_lines = self.log_manager.read_log_file(500)
            for line in log_lines:
                # 简单的级别检测
                level = "INFO"
                if "[ERROR]" in line:
                    level = "ERROR"
                elif "[WARNING]" in line:
                    level = "WARNING"
                elif "[SUCCESS]" in line:
                    level = "SUCCESS"

                self.log_text.insert(tk.END, line, level)
        except Exception as e:
            self.log_text.insert(tk.END, f"读取日志失败: {e}\n", "ERROR")

        self.log_text.see(tk.END)

    def clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空所有日志吗？"):
            self.log_text.delete(1.0, tk.END)
            self.log_manager.clear_log_file()

    def save_logs(self):
        """保存日志"""
        file_path = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".log",
            filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                content = self.log_text.get(1.0, tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", f"日志已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("失败", f"保存日志失败: {e}")

    def open_backup_directory(self):
        """打开备份目录"""
        import platform

        backup_dir = Path("backups")
        backup_dir.mkdir(exist_ok=True)

        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(backup_dir)])
            elif platform.system() == "Darwin":
                subprocess.run(["open", str(backup_dir)])
            else:
                subprocess.run(["xdg-open", str(backup_dir)])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开备份目录: {e}")

    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """关闭应用程序"""
        if messagebox.askokcancel("退出", "确定要退出 AUG 0.1 吗？"):
            self.log_manager.info("AUG 0.1 简化GUI正在关闭...")
            self.root.destroy()


if __name__ == "__main__":
    try:
        app = AUGSimpleGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"应用程序启动失败：{str(e)}")
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
