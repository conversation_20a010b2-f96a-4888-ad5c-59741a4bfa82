#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库扫描问题诊断脚本
分析为什么数据库扫描验证失败
"""

import os
import sys
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_sqlite_availability():
    """检查SQLite可用性"""
    print("🔍 检查SQLite可用性...")
    
    try:
        import sqlite3
        print(f"✅ SQLite3 模块可用，版本: {sqlite3.sqlite_version}")
        
        # 测试创建内存数据库
        conn = sqlite3.connect(":memory:")
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY)")
        cursor.execute("INSERT INTO test (id) VALUES (1)")
        result = cursor.fetchone()
        conn.close()
        
        print("✅ SQLite功能正常")
        return True
        
    except Exception as e:
        print(f"❌ SQLite检查失败: {e}")
        return False

def scan_for_database_files():
    """手动扫描数据库文件"""
    print("\n🔍 手动扫描数据库文件...")
    
    # 编辑器路径
    editor_paths = [
        os.path.expandvars("%APPDATA%\\Code"),
        os.path.expandvars("%APPDATA%\\Cursor"),
        os.path.expandvars("%APPDATA%\\VSCodium"),
        os.path.expandvars("%APPDATA%\\Code - Insiders"),
        os.path.expandvars("%APPDATA%\\Code - OSS"),
        os.path.expandvars("%LOCALAPPDATA%\\Programs\\Microsoft VS Code"),
        os.path.expandvars("%LOCALAPPDATA%\\Programs\\Cursor"),
        os.path.expandvars("%USERPROFILE%\\.vscode"),
        os.path.expandvars("%USERPROFILE%\\.cursor"),
    ]
    
    found_files = []
    
    for base_path in editor_paths:
        print(f"\n📁 扫描路径: {base_path}")
        
        if not os.path.exists(base_path):
            print("  ❌ 路径不存在")
            continue
            
        try:
            # 递归搜索数据库文件
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    file_lower = file.lower()
                    
                    # 检查数据库文件扩展名
                    if any(file_lower.endswith(ext) for ext in ['.db', '.sqlite', '.sqlite3', '.db3', '.s3db']):
                        file_path = os.path.join(root, file)
                        file_size = os.path.getsize(file_path)
                        
                        # 检查是否为SQLite文件
                        is_sqlite = check_sqlite_file(file_path)
                        
                        found_files.append({
                            'path': file_path,
                            'size': file_size,
                            'is_sqlite': is_sqlite,
                            'relative_path': os.path.relpath(file_path, base_path)
                        })
                        
                        print(f"  📄 {file} ({format_size(file_size)}) - {'SQLite' if is_sqlite else '非SQLite'}")
                        
        except Exception as e:
            print(f"  ❌ 扫描失败: {e}")
    
    print(f"\n✅ 总共发现 {len(found_files)} 个数据库文件")
    return found_files

def check_sqlite_file(file_path):
    """检查文件是否为SQLite数据库"""
    try:
        with open(file_path, 'rb') as f:
            header = f.read(16)
            return header.startswith(b'SQLite format 3\x00')
    except:
        return False

def analyze_found_databases(found_files):
    """分析发现的数据库文件"""
    print("\n🔍 分析发现的数据库文件...")
    
    sqlite_files = [f for f in found_files if f['is_sqlite']]
    
    if not sqlite_files:
        print("❌ 未发现有效的SQLite数据库文件")
        return False
    
    print(f"✅ 发现 {len(sqlite_files)} 个有效的SQLite文件:")
    
    for db_file in sqlite_files:
        print(f"\n📁 数据库: {os.path.basename(db_file['path'])}")
        print(f"   路径: {db_file['path']}")
        print(f"   大小: {format_size(db_file['size'])}")
        
        # 尝试分析数据库内容
        try:
            analyze_database_content(db_file['path'])
        except Exception as e:
            print(f"   ❌ 分析失败: {e}")
    
    return True

def analyze_database_content(db_path):
    """分析数据库内容"""
    try:
        # 使用只读模式连接
        with sqlite3.connect(f"file:{db_path}?mode=ro", uri=True, timeout=2) as conn:
            cursor = conn.cursor()
            
            # 获取表列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            print(f"   📋 表数量: {len(tables)}")
            
            if tables:
                print(f"   📋 表名: {', '.join(tables[:5])}")
                if len(tables) > 5:
                    print(f"        ... 还有 {len(tables) - 5} 个表")
                
                # 统计记录数
                total_records = 0
                augment_records = 0
                
                for table in tables[:10]:  # 只检查前10个表
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                        count = cursor.fetchone()[0]
                        total_records += count
                        
                        # 检查是否有augment相关记录
                        if count > 0 and count < 1000:  # 只对小表进行内容搜索
                            cursor.execute(f"PRAGMA table_info(`{table}`)")
                            columns = [col[1] for col in cursor.fetchall()]
                            
                            for column in columns:
                                try:
                                    cursor.execute(f"SELECT COUNT(*) FROM `{table}` WHERE `{column}` LIKE ?", ('%augment%',))
                                    augment_count = cursor.fetchone()[0]
                                    augment_records += augment_count
                                except:
                                    continue
                                    
                    except Exception as e:
                        print(f"   ⚠️ 表 {table} 分析失败: {e}")
                        continue
                
                print(f"   📊 总记录数: {total_records}")
                if augment_records > 0:
                    print(f"   🔍 Augment记录: {augment_records}")
                    
    except Exception as e:
        print(f"   ❌ 无法访问数据库: {e}")

def test_database_manager():
    """测试数据库管理器"""
    print("\n🔍 测试数据库管理器...")
    
    try:
        from gui_modules.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试扫描功能
        print("执行数据库扫描...")
        databases = db_manager.scan_databases()
        
        print(f"✅ 数据库管理器扫描结果: {len(databases)} 个数据库")
        
        for db in databases[:5]:  # 只显示前5个
            print(f"  📁 {os.path.basename(db.get('path', ''))}")
            print(f"     大小: {db_manager.format_size(db.get('size', 0))}")
            print(f"     可访问: {'是' if db.get('accessible', False) else '否'}")
            if db.get('accessible', False):
                print(f"     表数量: {len(db.get('tables', []))}")
                print(f"     记录数: {db.get('total_records', 0)}")
        
        return len(databases) > 0
        
    except Exception as e:
        print(f"❌ 数据库管理器测试失败: {e}")
        return False

def check_common_database_locations():
    """检查常见的数据库位置"""
    print("\n🔍 检查常见的数据库位置...")
    
    common_locations = [
        # Chrome/Edge 数据库
        os.path.expandvars("%LOCALAPPDATA%\\Google\\Chrome\\User Data\\Default"),
        os.path.expandvars("%LOCALAPPDATA%\\Microsoft\\Edge\\User Data\\Default"),
        
        # VS Code 相关
        os.path.expandvars("%APPDATA%\\Code\\CachedData"),
        os.path.expandvars("%APPDATA%\\Code\\logs"),
        
        # 系统数据库
        os.path.expandvars("%LOCALAPPDATA%\\Microsoft"),
        os.path.expandvars("%PROGRAMDATA%"),
        
        # 用户数据
        os.path.expandvars("%USERPROFILE%\\AppData"),
    ]
    
    found_dbs = []
    
    for location in common_locations:
        if os.path.exists(location):
            print(f"\n📁 检查: {location}")
            
            try:
                for root, dirs, files in os.walk(location):
                    # 限制搜索深度
                    level = root.replace(location, '').count(os.sep)
                    if level >= 3:
                        dirs[:] = []  # 不再深入
                        continue
                    
                    for file in files:
                        if file.lower().endswith(('.db', '.sqlite', '.sqlite3')):
                            file_path = os.path.join(root, file)
                            if check_sqlite_file(file_path):
                                file_size = os.path.getsize(file_path)
                                found_dbs.append(file_path)
                                print(f"  ✅ {file} ({format_size(file_size)})")
                                
            except Exception as e:
                print(f"  ❌ 搜索失败: {e}")
    
    print(f"\n✅ 在常见位置发现 {len(found_dbs)} 个SQLite数据库")
    return found_dbs

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.1f} {size_names[i]}"

def main():
    """主诊断函数"""
    print("🚀 数据库扫描问题诊断开始")
    print("=" * 50)
    
    # 1. 检查SQLite可用性
    if not check_sqlite_availability():
        print("❌ SQLite不可用，无法继续")
        return False
    
    # 2. 手动扫描数据库文件
    found_files = scan_for_database_files()
    
    # 3. 分析发现的数据库
    if found_files:
        analyze_found_databases(found_files)
    
    # 4. 检查常见位置
    common_dbs = check_common_database_locations()
    
    # 5. 测试数据库管理器
    manager_works = test_database_manager()
    
    print("\n" + "=" * 50)
    print("🎯 诊断总结:")
    print(f"  📄 编辑器路径中的数据库文件: {len(found_files)}")
    print(f"  📄 常见位置的数据库文件: {len(common_dbs)}")
    print(f"  🔧 数据库管理器工作状态: {'正常' if manager_works else '异常'}")
    
    if not found_files and not common_dbs:
        print("\n💡 可能的原因:")
        print("  1. 编辑器确实不使用SQLite数据库存储数据")
        print("  2. 数据库文件位置与预期不同")
        print("  3. 数据库文件被其他程序锁定")
        print("  4. 权限问题导致无法访问某些目录")
        
        print("\n🔧 建议解决方案:")
        print("  1. 检查编辑器是否真的使用SQLite")
        print("  2. 扩大搜索范围")
        print("  3. 以管理员权限运行")
        print("  4. 关闭所有编辑器后再扫描")
    
    return len(found_files) > 0 or len(common_dbs) > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
