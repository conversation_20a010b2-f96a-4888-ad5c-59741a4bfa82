"""
修复编辑器配置问题的专用脚本
解决UTF-8 BOM编码问题、缺失文件问题等
"""

import os
import json
import uuid
import datetime
import random
import shutil

def generate_machine_id():
    """生成机器ID"""
    return uuid.uuid4().hex

def generate_device_id():
    """生成设备ID"""
    return str(uuid.uuid4())

def generate_session_id():
    """生成会话ID"""
    return str(uuid.uuid4())

def generate_timestamp():
    """生成随机时间戳"""
    days = random.randint(1, 365)
    random_date = datetime.datetime.now() - datetime.timedelta(days=days)
    return random_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ")

def get_editor_paths():
    """获取所有支持的编辑器路径"""
    return {
        "VSCode": os.path.expandvars("%APPDATA%\\Code\\User\\globalStorage\\storage.json"),
        "VSCodeInsiders": os.path.expandvars("%APPDATA%\\Code - Insiders\\User\\globalStorage\\storage.json"),
        "Cursor": os.path.expandvars("%APPDATA%\\Cursor\\User\\globalStorage\\storage.json"),
        "VSCodium": os.path.expandvars("%APPDATA%\\VSCodium\\User\\globalStorage\\storage.json"),
        "CodeOSS": os.path.expandvars("%APPDATA%\\Code - OSS\\User\\globalStorage\\storage.json")
    }

def create_backup(file_path, editor_name):
    """创建备份文件"""
    if not os.path.exists(file_path):
        return None
        
    backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backups")
    os.makedirs(backup_dir, exist_ok=True)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = os.path.join(backup_dir, f"{editor_name}_storage_fixed_{timestamp}.json")
    
    try:
        shutil.copy2(file_path, backup_file)
        print(f"✓ 已创建备份: {backup_file}")
        return backup_file
    except Exception as e:
        print(f"✗ 创建备份失败: {str(e)}")
        return None

def fix_utf8_bom_issue(file_path):
    """修复UTF-8 BOM编码问题"""
    try:
        # 检查文件是否有BOM
        with open(file_path, 'rb') as f:
            content = f.read()
            
        if content.startswith(b'\xef\xbb\xbf'):
            print(f"  发现UTF-8 BOM，正在修复...")
            
            # 读取JSON内容（使用utf-8-sig处理BOM）
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                data = json.load(f)
            
            # 重新写入文件（不带BOM）
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"  ✓ UTF-8 BOM问题已修复")
            return True
        else:
            print(f"  ✓ 文件编码正常，无需修复")
            return True
            
    except Exception as e:
        print(f"  ✗ 修复UTF-8 BOM失败: {str(e)}")
        return False

def create_missing_storage(file_path, editor_name):
    """为缺失的编辑器创建默认存储文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 创建默认配置
        default_config = {
            "telemetry.machineId": generate_machine_id(),
            "telemetry.devDeviceId": generate_device_id(),
            "telemetry.sessionId": generate_session_id(),
            "telemetry.sqmId": generate_device_id(),
            "telemetry.firstSessionDate": generate_timestamp(),
            "telemetry.lastSessionDate": generate_timestamp(),
            "telemetry.isNewAppInstall": True,
            "telemetry.optIn": False
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        print(f"  ✓ 已创建默认存储文件")
        return True
        
    except Exception as e:
        print(f"  ✗ 创建默认存储文件失败: {str(e)}")
        return False

def validate_json_file(file_path):
    """验证JSON文件的有效性"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            json.load(f)
        print(f"  ✓ JSON文件格式有效")
        return True
    except json.JSONDecodeError as e:
        print(f"  ✗ JSON格式错误: {str(e)}")
        return False
    except Exception as e:
        print(f"  ✗ 文件读取错误: {str(e)}")
        return False

def fix_editor_issues(auto_mode=False):
    """修复所有编辑器问题"""
    print("编辑器配置问题修复工具")
    print("=" * 50)
    
    editor_paths = get_editor_paths()
    fixed_count = 0
    created_count = 0
    
    for editor_name, file_path in editor_paths.items():
        print(f"\n检查编辑器: {editor_name}")
        print(f"路径: {file_path}")
        
        if os.path.exists(file_path):
            print(f"  状态: 文件存在")
            
            # 创建备份
            backup_file = create_backup(file_path, editor_name)
            if not backup_file:
                print(f"  ⚠ 跳过修复（备份失败）")
                continue
            
            # 修复UTF-8 BOM问题
            if fix_utf8_bom_issue(file_path):
                # 验证修复后的文件
                if validate_json_file(file_path):
                    fixed_count += 1
                    print(f"  ✓ {editor_name} 修复完成")
                else:
                    print(f"  ✗ {editor_name} 修复失败")
            else:
                print(f"  ✗ {editor_name} 修复失败")
                
        else:
            print(f"  状态: 文件不存在")
            
            # 询问是否创建默认配置
            if auto_mode:
                response = 'y'
                print(f"  自动模式：为 {editor_name} 创建默认配置")
            else:
                response = input(f"  是否为 {editor_name} 创建默认配置? (y/N): ").strip().lower()

            if response in ['y', 'yes']:
                if create_missing_storage(file_path, editor_name):
                    if validate_json_file(file_path):
                        created_count += 1
                        print(f"  ✓ {editor_name} 默认配置创建完成")
                    else:
                        print(f"  ✗ {editor_name} 默认配置创建失败")
                else:
                    print(f"  ✗ {editor_name} 默认配置创建失败")
            else:
                print(f"  - 跳过 {editor_name}")
    
    print(f"\n修复完成:")
    print(f"已修复编辑器: {fixed_count}")
    print(f"已创建配置: {created_count}")
    print(f"总计处理: {fixed_count + created_count}")
    
    if fixed_count > 0 or created_count > 0:
        print("\n建议重启相关编辑器以应用更改")

if __name__ == "__main__":
    import sys

    # 检查是否为自动模式
    auto_mode = '--auto-fix' in sys.argv

    try:
        fix_editor_issues(auto_mode)
    except KeyboardInterrupt:
        print("\n\n操作已取消")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
    finally:
        if not auto_mode:
            input("\n按Enter键退出...")
