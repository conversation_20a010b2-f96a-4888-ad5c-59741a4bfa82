#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器模块
管理应用程序配置和设置
"""

import json
import os
from typing import Dict, Any, Optional
from pathlib import Path

class ConfigManager:
    """配置管理器类"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.config_dir = Path("config")
        self.gui_config_file = self.config_dir / "gui_config.json"
        self.telemetry_config_file = self.config_dir / "telemetry_config.json"
        self.database_config_file = self.config_dir / "database_config.json"
        
        self.gui_config = {}
        self.telemetry_config = {}
        self.database_config = {}
        
        self.ensure_config_dir()
        self.load_configs()
        
    def ensure_config_dir(self):
        """确保配置目录存在"""
        self.config_dir.mkdir(exist_ok=True)
        
    def load_configs(self):
        """加载所有配置文件"""
        self.load_gui_config()
        self.load_telemetry_config()
        self.load_database_config()
        
    def load_gui_config(self):
        """加载GUI配置"""
        default_gui_config = {
            "window": {
                "width": 1200,
                "height": 800,
                "theme": "superhero",
                "language": "zh_CN",
                "remember_position": True,
                "last_position": {"x": 100, "y": 100}
            },
            "interface": {
                "show_tooltips": True,
                "auto_detect_editors": True,
                "confirm_operations": True,
                "show_advanced_options": False
            },
            "operations": {
                "default_mode": "enhanced",
                "auto_backup": True,
                "backup_retention_days": 30,
                "log_level": "INFO"
            },
            "security": {
                "require_confirmation": True,
                "encrypt_backups": False,
                "secure_delete": False
            }
        }
        
        if self.gui_config_file.exists():
            try:
                with open(self.gui_config_file, 'r', encoding='utf-8') as f:
                    self.gui_config = json.load(f)
                # 合并默认配置（确保新增的配置项存在）
                self.gui_config = self._merge_configs(default_gui_config, self.gui_config)
            except Exception as e:
                print(f"加载GUI配置失败: {e}")
                self.gui_config = default_gui_config
        else:
            self.gui_config = default_gui_config
            self.save_gui_config()
            
    def load_telemetry_config(self):
        """加载遥测配置"""
        if self.telemetry_config_file.exists():
            try:
                with open(self.telemetry_config_file, 'r', encoding='utf-8') as f:
                    self.telemetry_config = json.load(f)
            except Exception as e:
                print(f"加载遥测配置失败: {e}")
                self.telemetry_config = {}
        else:
            # 如果文件不存在，创建默认配置
            self.create_default_telemetry_config()
            
    def load_database_config(self):
        """加载数据库配置"""
        if self.database_config_file.exists():
            try:
                with open(self.database_config_file, 'r', encoding='utf-8') as f:
                    self.database_config = json.load(f)
            except Exception as e:
                print(f"加载数据库配置失败: {e}")
                self.database_config = {}
        else:
            # 如果文件不存在，创建默认配置
            self.create_default_database_config()
            
    def save_gui_config(self):
        """保存GUI配置"""
        try:
            with open(self.gui_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.gui_config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存GUI配置失败: {e}")
            
    def save_telemetry_config(self):
        """保存遥测配置"""
        try:
            with open(self.telemetry_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.telemetry_config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存遥测配置失败: {e}")
            
    def save_database_config(self):
        """保存数据库配置"""
        try:
            with open(self.database_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.database_config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存数据库配置失败: {e}")
            
    def get_gui_config(self, key: str = None, default: Any = None) -> Any:
        """获取GUI配置"""
        if key is None:
            return self.gui_config
        return self._get_nested_value(self.gui_config, key, default)
        
    def set_gui_config(self, key: str, value: Any):
        """设置GUI配置"""
        self._set_nested_value(self.gui_config, key, value)
        self.save_gui_config()
        
    def get_telemetry_config(self, key: str = None, default: Any = None) -> Any:
        """获取遥测配置"""
        if key is None:
            return self.telemetry_config
        return self._get_nested_value(self.telemetry_config, key, default)
        
    def get_database_config(self, key: str = None, default: Any = None) -> Any:
        """获取数据库配置"""
        if key is None:
            return self.database_config
        return self._get_nested_value(self.database_config, key, default)
        
    def get_supported_editors(self) -> Dict[str, Dict]:
        """获取支持的编辑器列表"""
        return self.get_telemetry_config("editors", {})
        
    def get_telemetry_fields(self) -> Dict[str, Dict]:
        """获取遥测字段配置"""
        return self.get_telemetry_config("telemetry_fields", {})
        
    def get_field_categories(self) -> list:
        """获取字段类别列表"""
        fields = self.get_telemetry_fields()
        return list(fields.keys())
        
    def get_fields_by_category(self, category: str) -> Dict[str, Dict]:
        """根据类别获取字段"""
        fields = self.get_telemetry_fields()
        return fields.get(category, {})
        
    def _get_nested_value(self, config: dict, key: str, default: Any = None) -> Any:
        """获取嵌套配置值"""
        keys = key.split('.')
        value = config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
        
    def _set_nested_value(self, config: dict, key: str, value: Any):
        """设置嵌套配置值"""
        keys = key.split('.')
        current = config
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        current[keys[-1]] = value
        
    def _merge_configs(self, default: dict, user: dict) -> dict:
        """合并配置字典"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
        
    def create_default_telemetry_config(self):
        """创建默认遥测配置"""
        default_config = {
            "version": "0.1.0",
            "description": "Enhanced Telemetry ID Configuration for GUI",
            "editors": {
                "vscode": {
                    "name": "Visual Studio Code",
                    "enabled": True,
                    "paths": {
                        "windows": "%APPDATA%/Code/User/globalStorage/storage.json"
                    },
                    "processes": ["code", "Code.exe"],
                    "priority": 1
                },
                "cursor": {
                    "name": "Cursor Editor",
                    "enabled": True,
                    "paths": {
                        "windows": "%APPDATA%/Cursor/User/globalStorage/storage.json"
                    },
                    "processes": ["cursor", "Cursor.exe"],
                    "priority": 2
                },
                "vscodium": {
                    "name": "VSCodium",
                    "enabled": True,
                    "paths": {
                        "windows": "%APPDATA%/VSCodium/User/globalStorage/storage.json"
                    },
                    "processes": ["codium", "VSCodium.exe"],
                    "priority": 3
                }
            },
            "telemetry_fields": {
                "core_identifiers": {
                    "telemetry.machineId": {
                        "type": "hex64",
                        "description": "机器唯一标识符",
                        "required": True,
                        "category": "core",
                        "privacy_level": "high"
                    },
                    "telemetry.devDeviceId": {
                        "type": "uuid4",
                        "description": "设备标识符",
                        "required": True,
                        "category": "core",
                        "privacy_level": "high"
                    },
                    "telemetry.sessionId": {
                        "type": "uuid4",
                        "description": "会话标识符",
                        "required": False,
                        "category": "core",
                        "privacy_level": "high"
                    },
                    "telemetry.sqmId": {
                        "type": "uuid4",
                        "description": "SQM标识符",
                        "required": False,
                        "category": "core",
                        "privacy_level": "high"
                    }
                },
                "installation_tracking": {
                    "telemetry.instanceId": {
                        "type": "uuid4",
                        "description": "实例标识符",
                        "required": False,
                        "category": "installation",
                        "privacy_level": "medium"
                    },
                    "telemetry.installationId": {
                        "type": "uuid4",
                        "description": "安装标识符",
                        "required": False,
                        "category": "installation",
                        "privacy_level": "medium"
                    },
                    "telemetry.firstSessionDate": {
                        "type": "timestamp",
                        "description": "首次会话日期",
                        "required": False,
                        "category": "installation",
                        "privacy_level": "low"
                    },
                    "telemetry.lastSessionDate": {
                        "type": "timestamp",
                        "description": "最后会话日期",
                        "required": False,
                        "category": "installation",
                        "privacy_level": "low"
                    },
                    "telemetry.isNewAppInstall": {
                        "type": "boolean",
                        "description": "是否新安装",
                        "required": False,
                        "category": "installation",
                        "privacy_level": "low"
                    }
                },
                "user_tracking": {
                    "telemetry.userId": {
                        "type": "uuid4",
                        "description": "用户标识符",
                        "required": False,
                        "category": "user",
                        "privacy_level": "high"
                    },
                    "telemetry.accountId": {
                        "type": "uuid4",
                        "description": "账户标识符",
                        "required": False,
                        "category": "user",
                        "privacy_level": "high"
                    },
                    "telemetry.profileId": {
                        "type": "uuid4",
                        "description": "配置文件标识符",
                        "required": False,
                        "category": "user",
                        "privacy_level": "medium"
                    }
                },
                "hardware_system": {
                    "telemetry.hardwareId": {
                        "type": "hex32",
                        "description": "硬件标识符",
                        "required": False,
                        "category": "hardware",
                        "privacy_level": "high"
                    },
                    "telemetry.systemId": {
                        "type": "hex32",
                        "description": "系统标识符",
                        "required": False,
                        "category": "hardware",
                        "privacy_level": "high"
                    },
                    "telemetry.platformId": {
                        "type": "hex16",
                        "description": "平台标识符",
                        "required": False,
                        "category": "hardware",
                        "privacy_level": "medium"
                    }
                }
            }
        }
        
        self.telemetry_config = default_config
        self.save_telemetry_config()
        
    def create_default_database_config(self):
        """创建默认数据库配置"""
        default_config = {
            "version": "0.1.0",
            "description": "Database Configuration for GUI",
            "backup_settings": {
                "enabled": True,
                "backup_directory": "backups",
                "retention_days": 30,
                "compression": False,
                "encryption": False
            }
        }
        
        self.database_config = default_config
        self.save_database_config()
        
    def export_config(self, file_path: str) -> bool:
        """导出配置到文件"""
        try:
            export_data = {
                "gui_config": self.gui_config,
                "telemetry_config": self.telemetry_config,
                "database_config": self.database_config,
                "export_time": str(Path().cwd()),
                "version": "0.1.0"
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
            
    def import_config(self, file_path: str) -> bool:
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
                
            if "gui_config" in import_data:
                self.gui_config = import_data["gui_config"]
                self.save_gui_config()
                
            if "telemetry_config" in import_data:
                self.telemetry_config = import_data["telemetry_config"]
                self.save_telemetry_config()
                
            if "database_config" in import_data:
                self.database_config = import_data["database_config"]
                self.save_database_config()
                
            return True
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
