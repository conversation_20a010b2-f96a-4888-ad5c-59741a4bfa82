"""
直接修改编辑器遥测ID，不依赖PowerShell脚本
"""

import os
import json
import uuid
import random
import datetime
import argparse
import sys
import shutil
from pathlib import Path

def generate_machine_id():
    """生成机器ID"""
    # 使用uuid4生成随机ID
    return uuid.uuid4().hex

def generate_device_id():
    """生成设备ID"""
    return str(uuid.uuid4())

def generate_session_id():
    """生成会话ID"""
    return str(uuid.uuid4())

def generate_timestamp():
    """生成随机时间戳"""
    # 生成过去一年内的随机日期
    days = random.randint(1, 365)
    random_date = datetime.datetime.now() - datetime.timedelta(days=days)
    return random_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ")

def create_backup(file_path, editor_name):
    """创建备份文件"""
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在: {file_path}")
        return None
        
    # 创建备份目录
    backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backups")
    os.makedirs(backup_dir, exist_ok=True)
    
    # 创建带时间戳的备份文件名
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = os.path.join(backup_dir, f"{editor_name}_storage_{timestamp}.json")
    
    # 复制文件
    try:
        shutil.copy2(file_path, backup_file)
        print(f"已创建备份: {backup_file}")
        return backup_file
    except Exception as e:
        print(f"创建备份失败: {str(e)}")
        return None

def modify_telemetry_ids(file_path, fields=None):
    """修改遥测ID"""
    try:
        # 读取JSON文件，处理UTF-8 BOM问题
        data = None
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except (UnicodeDecodeError, json.JSONDecodeError) as e:
            # 如果遇到UTF-8 BOM问题或JSON解析错误，使用utf-8-sig编码
            try:
                with open(file_path, 'r', encoding='utf-8-sig') as f:
                    data = json.load(f)
                print(f"检测到BOM编码，已使用utf-8-sig重新读取")
            except Exception as e2:
                print(f"使用utf-8-sig编码仍然失败: {str(e2)}")
                # 尝试检查文件内容
                try:
                    with open(file_path, 'rb') as f:
                        first_bytes = f.read(10)
                        print(f"文件前10字节: {first_bytes}")
                except:
                    pass
                raise e
            
        # 确定要修改的字段
        if not fields:
            fields = ["telemetry.machineId", "telemetry.devDeviceId"]
            
        # 修改字段
        modified = False
        for field in fields:
            if field == "telemetry.machineId":
                data[field] = generate_machine_id()
                modified = True
            elif field == "telemetry.devDeviceId":
                data[field] = generate_device_id()
                modified = True
            elif field == "telemetry.sessionId":
                data[field] = generate_session_id()
                modified = True
            elif field == "telemetry.firstSessionDate" or field == "telemetry.lastSessionDate":
                data[field] = generate_timestamp()
                modified = True
            elif field in data:
                # 对于其他字段，如果存在则使用UUID替换
                data[field] = str(uuid.uuid4())
                modified = True
                
        # 写回文件，保持原有编码格式
        if modified:
            # 检查原文件是否有BOM
            with open(file_path, 'rb') as f:
                first_bytes = f.read(3)
                has_bom = first_bytes == b'\xef\xbb\xbf'

            # 根据原文件是否有BOM选择编码方式
            encoding = 'utf-8-sig' if has_bom else 'utf-8'
            with open(file_path, 'w', encoding=encoding) as f:
                json.dump(data, f, indent=2)
            print(f"已成功修改文件: {file_path}")
            return True
        else:
            print(f"未修改任何字段")
            return False
            
    except Exception as e:
        print(f"修改文件失败: {str(e)}")
        return False

def get_editor_path(editor):
    """获取编辑器存储文件路径"""
    editor_paths = {
        "VSCode": "%APPDATA%\\Code\\User\\globalStorage\\storage.json",
        "VSCodeInsiders": "%APPDATA%\\Code - Insiders\\User\\globalStorage\\storage.json",
        "Cursor": "%APPDATA%\\Cursor\\User\\globalStorage\\storage.json",
        "VSCodium": "%APPDATA%\\VSCodium\\User\\globalStorage\\storage.json",
        "CodeOSS": "%APPDATA%\\Code - OSS\\User\\globalStorage\\storage.json",
        "Code": "%APPDATA%\\Code\\User\\globalStorage\\storage.json"  # 添加对 Code 的支持
    }

    if editor in editor_paths:
        return os.path.expandvars(editor_paths[editor])
    else:
        return None

def create_default_storage(file_path):
    """为不存在的编辑器创建默认存储文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 创建默认配置
        default_config = {
            "telemetry.machineId": generate_machine_id(),
            "telemetry.devDeviceId": generate_device_id(),
            "telemetry.sessionId": generate_session_id(),
            "telemetry.sqmId": generate_device_id(),
            "telemetry.firstSessionDate": generate_timestamp(),
            "telemetry.lastSessionDate": generate_timestamp(),
            "telemetry.isNewAppInstall": True,
            "telemetry.optIn": False
        }

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2)

        print(f"已创建默认存储文件: {file_path}")
        return True
    except Exception as e:
        print(f"创建默认存储文件失败: {str(e)}")
        return False

def process_editor(editor, fields=None, mode=None, create_missing=False):
    """处理单个编辑器"""
    print(f"\n处理编辑器: {editor}")

    # 获取编辑器路径
    file_path = get_editor_path(editor)
    if not file_path:
        print(f"不支持的编辑器: {editor}")
        return False

    if not os.path.exists(file_path):
        if create_missing:
            print(f"编辑器存储文件不存在，尝试创建: {file_path}")
            if not create_default_storage(file_path):
                return False
        else:
            print(f"编辑器存储文件不存在: {file_path}")
            return False
        
    # 根据模式确定字段
    if mode == "enhanced":
        fields = [
            "telemetry.machineId", 
            "telemetry.devDeviceId",
            "telemetry.sessionId",
            "telemetry.sqmId",
            "telemetry.firstSessionDate",
            "telemetry.lastSessionDate",
            "telemetry.isNewAppInstall",
            "telemetry.optIn"
        ]
    elif mode == "quick":
        fields = ["telemetry.machineId", "telemetry.devDeviceId"]
    elif not fields:
        fields = ["telemetry.machineId", "telemetry.devDeviceId"]
    
    # 创建备份
    backup_file = create_backup(file_path, editor)
    if not backup_file:
        print(f"无法为 {editor} 创建备份，跳过修改")
        return False
        
    # 修改遥测ID
    result = modify_telemetry_ids(file_path, fields)
    return result

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="直接修改编辑器遥测ID")
    parser.add_argument("--editors", "-e", help="要修改的编辑器，用逗号分隔")
    parser.add_argument("--fields", "-f", help="要修改的字段，用逗号分隔")
    parser.add_argument("--mode", "-m", help="操作模式: standard, enhanced, quick")
    parser.add_argument("--create-missing", "-c", action="store_true", help="为不存在的编辑器创建默认配置")
    args = parser.parse_args()
    
    print("Augment ID修改器 - 直接修改模式")
    print("=" * 50)
    
    # 确定要处理的编辑器
    if args.editors:
        editors = [e.strip() for e in args.editors.split(",")]
    else:
        # 检测已安装的编辑器
        editors = []
        supported_editors = ["VSCode", "VSCodeInsiders", "Cursor", "VSCodium", "CodeOSS", "Code"]

        for editor in supported_editors:
            editor_path = get_editor_path(editor)
            if editor_path and os.path.exists(editor_path):
                editors.append(editor)

        if not editors:
            if args.create_missing:
                print("未检测到已安装的编辑器，但将尝试为所有支持的编辑器创建配置")
                editors = supported_editors
            else:
                print("未检测到支持的编辑器")
                print("使用 --create-missing 参数可以为不存在的编辑器创建默认配置")
                return
            
    # 确定要修改的字段
    fields = args.fields.split(",") if args.fields else None
    
    # 处理每个编辑器
    success_count = 0
    for editor in editors:
        if process_editor(editor, fields, args.mode, args.create_missing):
            success_count += 1
            
    # 显示结果
    print("\n处理完成:")
    print(f"成功修改: {success_count}/{len(editors)} 个编辑器")
    
    if success_count > 0:
        print("\n请重启编辑器以应用更改")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"发生错误: {str(e)}")
    finally:
        input("\n按Enter键退出...")