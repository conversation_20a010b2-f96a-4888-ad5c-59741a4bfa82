#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键清理功能测试脚本
验证一键清理功能是否正常工作
"""

import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_oneclick_scan():
    """测试一键扫描功能"""
    print("🔍 测试一键扫描功能...")
    
    try:
        # 导入必要的模块
        from gui_modules.config_manager import ConfigManager
        from gui_modules.editor_detector import EditorDetector
        from gui_modules.telemetry_modifier import TelemetryModifier
        from gui_modules.backup_manager import BackupManager
        from gui_modules.log_manager import LogManager
        from gui_modules.database_manager import DatabaseManager
        from gui_modules.workspace_manager import WorkspaceManager
        from gui_modules.augment_detector import AugmentDetector
        
        # 初始化管理器
        config_manager = ConfigManager()
        editor_detector = EditorDetector()
        database_manager = DatabaseManager()
        workspace_manager = WorkspaceManager()
        augment_detector = AugmentDetector()
        
        print("✅ 所有管理器初始化成功")
        
        # 测试编辑器检测
        print("\n📝 测试编辑器检测...")
        installed_editors = editor_detector.get_installed_editors()
        print(f"✅ 检测到 {len(installed_editors)} 个已安装的编辑器")
        
        # 测试数据库扫描
        print("\n🗃️ 测试数据库扫描...")
        databases = database_manager.scan_databases()
        db_size = sum(db.get("size", 0) for db in databases)
        db_augment = sum(db.get("augment_records", 0) for db in databases)
        print(f"✅ 扫描到 {len(databases)} 个数据文件")
        print(f"   总大小: {database_manager.format_size(db_size)}")
        print(f"   Augment记录: {db_augment} 个")
        
        # 测试工作区扫描
        print("\n💾 测试工作区扫描...")
        workspaces = workspace_manager.scan_workspaces()
        ws_size = sum(ws.get("size", 0) for ws in workspaces)
        print(f"✅ 扫描到 {len(workspaces)} 个工作区")
        print(f"   总大小: {workspace_manager.format_size(ws_size)}")
        
        # 测试Augment扫描
        print("\n🔍 测试Augment扫描...")
        augment_scan = augment_detector.scan_augment_extensions()
        augment_size = 0
        
        for ext in augment_scan.get("extensions", []):
            augment_size += ext.get("size", 0)
        for storage in augment_scan.get("storage_data", []):
            augment_size += storage.get("size", 0)
            
        print(f"✅ 扫描到 {augment_scan.get('total_found', 0)} 个Augment项目")
        print(f"   总大小: {augment_detector.format_size(augment_size)}")
        
        # 汇总结果
        total_items = len(databases) + len(workspaces) + augment_scan.get('total_found', 0)
        total_size = db_size + ws_size + augment_size
        total_augment = db_augment + augment_scan.get('total_found', 0)
        
        print(f"\n📊 扫描总结:")
        print(f"   总项目数: {total_items} 个")
        print(f"   总大小: {database_manager.format_size(total_size)}")
        print(f"   Augment相关: {total_augment} 个")
        
        return {
            "success": True,
            "total_items": total_items,
            "total_size": total_size,
            "total_augment": total_augment,
            "databases": len(databases),
            "workspaces": len(workspaces),
            "augment_items": augment_scan.get('total_found', 0)
        }
        
    except Exception as e:
        print(f"❌ 一键扫描测试失败: {e}")
        return {"success": False, "error": str(e)}

def test_cleanup_preparation():
    """测试清理准备功能"""
    print("\n🔧 测试清理准备功能...")
    
    try:
        from gui_modules.database_manager import DatabaseManager
        from gui_modules.augment_detector import AugmentDetector
        
        database_manager = DatabaseManager()
        augment_detector = AugmentDetector()
        
        # 测试数据库备份
        print("💾 测试数据库备份...")
        databases = database_manager.scan_databases()
        
        if databases:
            test_db = databases[0]
            db_path = test_db.get("path", "")
            
            if os.path.exists(db_path):
                backup_path = database_manager.backup_database(db_path)
                if backup_path and os.path.exists(backup_path):
                    print(f"✅ 数据库备份成功: {backup_path}")
                    
                    # 清理测试备份
                    os.remove(backup_path)
                    info_file = backup_path.replace('.backup', '.info')
                    if os.path.exists(info_file):
                        os.remove(info_file)
                else:
                    print("⚠️ 数据库备份失败")
        
        # 测试Augment备份
        print("💾 测试Augment备份...")
        augment_scan = augment_detector.scan_augment_extensions()
        
        if augment_scan.get('total_found', 0) > 0:
            # 这里只测试备份功能，不实际执行
            print(f"✅ 发现 {augment_scan.get('total_found', 0)} 个Augment项目可备份")
        else:
            print("ℹ️ 未发现Augment项目")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理准备测试失败: {e}")
        return False

def test_real_cleanup_capability():
    """测试真实清理能力（验证清理功能是否可用）"""
    print("\n🔧 测试真实清理能力...")

    try:
        from gui_modules.database_manager import DatabaseManager
        from gui_modules.augment_detector import AugmentDetector

        database_manager = DatabaseManager()
        augment_detector = AugmentDetector()

        # 检查数据库清理能力
        print("🗃️ 检查数据库清理能力...")
        databases = database_manager.scan_databases()

        augment_records_found = 0
        cleanable_databases = 0

        for db_info in databases:
            if db_info.get("accessible", False):
                cleanable_databases += 1
                augment_records_found += db_info.get("augment_records", 0)

        print(f"✅ 可清理数据库: {cleanable_databases} 个")
        print(f"   发现Augment记录: {augment_records_found} 个")

        # 检查Augment清理能力
        print("🔍 检查Augment清理能力...")
        augment_scan = augment_detector.scan_augment_extensions()
        augment_items = augment_scan.get('total_found', 0)

        if augment_items > 0:
            print(f"✅ 发现可清理Augment项目: {augment_items} 个")

            # 检查具体项目
            extensions = augment_scan.get("extensions", [])
            storage_data = augment_scan.get("storage_data", [])
            settings_refs = augment_scan.get("settings_references", [])

            print(f"   扩展文件: {len(extensions)} 个")
            print(f"   存储数据: {len(storage_data)} 个")
            print(f"   设置引用: {len(settings_refs)} 个")
        else:
            print("ℹ️ 未发现Augment项目")

        # 检查编辑器清理能力
        print("🧹 检查编辑器清理能力...")
        from gui_modules.editor_detector import EditorDetector
        editor_detector = EditorDetector()

        installed_editors = editor_detector.get_installed_editors()
        cleanable_editors = 0

        for editor_id in installed_editors:
            editor_info = editor_detector.get_editor_info(editor_id)
            storage_path = editor_info.get("storage_path", "")

            if os.path.exists(storage_path):
                cleanable_editors += 1

        print(f"✅ 可清理编辑器: {cleanable_editors} 个")

        # 检查工作区清理能力
        print("💾 检查工作区清理能力...")
        from gui_modules.workspace_manager import WorkspaceManager
        workspace_manager = WorkspaceManager()
        workspaces = workspace_manager.scan_workspaces()

        cleanable_workspaces = len([ws for ws in workspaces if ws.get("exists", False)])
        print(f"✅ 可清理工作区: {cleanable_workspaces} 个")

        # 总结清理能力
        total_cleanable = cleanable_databases + augment_items + cleanable_editors + cleanable_workspaces

        if total_cleanable > 0:
            print(f"\n📊 清理能力总结:")
            print(f"   总可清理项目: {total_cleanable} 个")
            print(f"   数据库文件: {cleanable_databases} 个")
            print(f"   Augment项目: {augment_items} 个")
            print(f"   编辑器数据: {cleanable_editors} 个")
            print(f"   工作区数据: {cleanable_workspaces} 个")
            print(f"✅ 清理功能完全可用")
            return True
        else:
            print("⚠️ 未发现可清理的项目")
            return False

    except Exception as e:
        print(f"❌ 清理能力检查失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成...")
    
    try:
        # 测试导入GUI模块
        import tkinter as tk
        from tkinter import ttk
        
        print("✅ Tkinter模块导入成功")
        
        # 测试创建简单窗口
        root = tk.Tk()
        root.title("一键清理测试")
        root.geometry("400x300")
        
        # 创建测试控件
        label = ttk.Label(root, text="一键清理功能测试")
        label.pack(pady=20)
        
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(root, variable=progress_var, maximum=100)
        progress_bar.pack(pady=10, padx=20, fill=tk.X)
        
        # 测试进度条功能
        for i in range(0, 101, 20):
            progress_var.set(i)
            root.update()
            time.sleep(0.05)
        
        root.destroy()
        print("✅ GUI集成测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 一键清理功能测试开始")
    print("=" * 50)
    
    tests = [
        ("一键扫描功能", test_oneclick_scan),
        ("清理准备功能", test_cleanup_preparation),
        ("真实清理能力", test_real_cleanup_capability),
        ("GUI集成测试", test_gui_integration)
    ]
    
    passed = 0
    failed = 0
    scan_results = None
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if isinstance(result, dict):
                # 扫描测试返回详细结果
                if result.get("success", False):
                    passed += 1
                    scan_results = result
                    print(f"✅ {test_name} 测试通过")
                else:
                    failed += 1
                    print(f"❌ {test_name} 测试失败: {result.get('error', '未知错误')}")
            elif result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                failed += 1
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed} 个通过, {failed} 个失败")
    
    if scan_results and scan_results.get("success"):
        print(f"\n📊 扫描结果摘要:")
        print(f"   总项目数: {scan_results['total_items']} 个")
        print(f"   数据库文件: {scan_results['databases']} 个")
        print(f"   工作区: {scan_results['workspaces']} 个")
        print(f"   Augment项目: {scan_results['augment_items']} 个")
        print(f"   Augment相关总数: {scan_results['total_augment']} 个")
    
    if failed == 0:
        print("\n🎉 一键清理功能完全正常！")
        print("✅ 所有组件都能正常工作")
        print("✅ 可以安全地使用一键清理功能")
    elif passed > failed:
        print("\n✅ 一键清理功能基本正常")
        print("⚠️ 部分功能可能需要优化")
    else:
        print("\n⚠️ 一键清理功能需要修复")
    
    print(f"\n💡 使用建议:")
    print(f"   1. 使用前先关闭所有编辑器")
    print(f"   2. 重要数据会自动备份")
    print(f"   3. 清理后建议重启编辑器")
    print(f"   4. 如需恢复，查看备份文件夹")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
