# AUG 0.1 Enhanced Telemetry ID Modifier - GUI版本

## 📋 项目简介

**AUG 0.1** 是一个专业的隐私保护工具，专门用于修改代码编辑器的遥测标识符，保护开发者的隐私和系统信息不被过度收集。本版本提供了现代化的图形用户界面，使操作更加直观和便捷。

## ✨ 主要特性

### 🎯 核心功能
- **多编辑器支持**: VS Code、Cursor、VSCodium、VS Code Insiders、Code-OSS
- **全面字段覆盖**: 50+ 种不同类型的遥测标识符
- **多种操作模式**: 增强、标准、快速、自定义四种模式
- **精确字段选择**: 可视化选择特定遥测字段进行修改
- **深度清理功能**: 清除编辑器历史记录、缓存等隐私数据
- **自动备份**: 操作前自动创建备份，支持一键恢复
- **实时日志**: 详细的操作日志记录和监控

### 🖥️ 界面特性
- **现代化设计**: 直观易用的图形界面
- **多标签页设计**: 仪表板、操作、字段选择、清理、日志五大功能区
- **双语支持**: 中英文界面切换
- **响应式布局**: 适配不同屏幕尺寸
- **实时反馈**: 进度条和状态指示
- **操作向导**: 简化的操作流程
- **可视化选择**: 树形视图和智能选择工具

### 🔒 安全特性
- **加密级随机**: 使用加密级随机数生成器
- **完整性验证**: 文件完整性检查
- **安全备份**: 自动备份和恢复机制
- **操作审计**: 完整的操作日志记录

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10/11 (主要支持)
- **Python**: 3.7 或更高版本
- **内存**: 至少 512MB 可用内存
- **磁盘空间**: 至少 100MB 可用空间

### 安装步骤

1. **检查Python环境**
   ```bash
   python --version
   ```
   如果未安装Python，请从 [python.org](https://www.python.org/downloads/) 下载安装。

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **启动GUI应用**
   - **方式一**: 双击 `启动GUI.bat` 文件
   - **方式二**: 命令行运行
     ```bash
     python gui_launcher_simple.py
     ```

### 首次使用

1. **检测编辑器**: 点击"检测编辑器"按钮，自动扫描系统中的代码编辑器
2. **选择模式**: 根据需要选择操作模式（推荐使用"增强模式"）
3. **执行操作**: 点击"开始操作"执行修改
4. **重启编辑器**: 操作完成后重启相关编辑器以应用更改

## 📖 使用指南

### 操作模式说明

| 模式 | 修改字段数 | 保护级别 | 适用场景 |
|------|------------|----------|----------|
| **🛡️ 增强模式** | 50+ 个 | 最高 | 最大隐私保护，推荐使用 |
| **⚖️ 标准模式** | 10-15 个 | 中等 | 平衡保护和兼容性 |
| **⚡ 快速模式** | 2-3 个 | 基础 | 快速操作，最小影响 |
| **🔧 自定义模式** | 用户选择 | 自定义 | 高级用户完全自定义 |

### 支持的编辑器

| 编辑器 | 状态 | 配置路径 |
|--------|------|----------|
| **Visual Studio Code** | ✅ 完全支持 | `%APPDATA%\Code\User\globalStorage\storage.json` |
| **Cursor Editor** | ✅ 完全支持 | `%APPDATA%\Cursor\User\globalStorage\storage.json` |
| **VSCodium** | ✅ 完全支持 | `%APPDATA%\VSCodium\User\globalStorage\storage.json` |
| **VS Code Insiders** | ✅ 完全支持 | `%APPDATA%\Code - Insiders\User\globalStorage\storage.json` |
| **Code - OSS** | ✅ 完全支持 | `%APPDATA%\Code - OSS\User\globalStorage\storage.json` |

### 遥测字段类别

1. **核心标识符** (4种)
   - `telemetry.machineId` - 机器唯一标识符
   - `telemetry.devDeviceId` - 设备标识符
   - `telemetry.sessionId` - 会话标识符
   - `telemetry.sqmId` - SQM标识符

2. **安装追踪** (6种)
   - 安装日期、实例ID、选择状态等

3. **用户追踪** (4种)
   - 用户ID、账户ID、配置文件ID等

4. **硬件系统** (6种)
   - 硬件指纹、系统ID、平台ID等

5. **网络位置** (6种)
   - 网络ID、位置ID、时区ID等

6. **使用行为** (6种)
   - 使用模式、工作流ID、项目ID等

7. **性能诊断** (6种)
   - 性能ID、诊断ID、崩溃ID等

8. **AI和Copilot** (6种)
   - AI相关的遥测字段

9. **Microsoft生态** (6种)
   - Microsoft服务相关字段

10. **隐私合规** (6种)
    - GDPR、隐私同意等字段

## 🔧 高级功能

### 备份管理
- **自动备份**: 每次操作前自动创建备份
- **备份列表**: 查看所有历史备份
- **一键恢复**: 快速恢复到任意备份点
- **备份清理**: 自动清理过期备份

### 日志系统
- **实时日志**: 操作过程实时显示
- **日志级别**: ERROR、WARNING、INFO、SUCCESS、VERBOSE
- **日志过滤**: 按级别过滤显示
- **日志导出**: 保存日志到文件

### 配置管理
- **配置导入/导出**: 备份和分享配置
- **自定义设置**: 个性化界面和行为
- **多语言**: 中英文界面切换

## ⚠️ 注意事项

### 使用前须知
1. **关闭编辑器**: 建议在操作前关闭所有目标编辑器
2. **管理员权限**: 某些情况下可能需要管理员权限
3. **备份重要**: 虽然有自动备份，但建议手动备份重要配置
4. **重启应用**: 修改后需要重启编辑器才能生效

### 安全建议
1. **定期备份**: 定期导出配置和备份文件
2. **验证操作**: 操作前仔细确认选择的编辑器和模式
3. **监控日志**: 关注操作日志中的错误和警告信息
4. **测试环境**: 重要环境建议先在测试环境验证

## 🐛 故障排除

### 常见问题

**Q: GUI启动失败**
A: 检查Python环境和依赖模块是否正确安装

**Q: 检测不到编辑器**
A: 确认编辑器已正确安装，检查配置路径是否存在

**Q: 操作失败**
A: 查看日志获取详细错误信息，确认编辑器已关闭

**Q: 备份恢复失败**
A: 检查备份文件完整性，确认目标路径可写

### 获取帮助
- **查看日志**: 详细的错误信息在日志中
- **检查配置**: 确认配置文件格式正确
- **重启应用**: 尝试重启GUI应用
- **联系支持**: 提供日志文件获取技术支持

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交问题报告和功能请求。如需贡献代码，请先阅读贡献指南。

## 📞 联系我们

- **项目主页**: [AUG 0.1 Project](https://github.com/aug-team/aug-0.1)
- **问题报告**: [Issues](https://github.com/aug-team/aug-0.1/issues)
- **技术支持**: <EMAIL>

---

© 2024 AUG Team. All rights reserved.
