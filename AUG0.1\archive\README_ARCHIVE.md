# Archive 文件夹说明

此文件夹包含了项目的测试文件、开发版本和其他辅助工具，这些文件已从主目录移动到此处以保持项目结构的整洁。

## 📁 文件分类

### 🔧 核心功能脚本
- `direct_modify.py` - 直接修改编辑器遥测ID的核心脚本
- `fix_editor_issues.py` - 修复编辑器配置问题的专用脚本

### 🚀 启动器和批处理文件
- `direct_modify.bat` - 直接修改脚本的批处理启动器
- `fix_editor_issues.bat` - 修复脚本的批处理启动器
- `OneClick-Fix-All.bat` - 一键修复所有问题的批处理文件
- `run_direct_modify.bat` - 运行直接修改的批处理文件
- `run_gui.bat` - 运行GUI界面的批处理文件

### 🧪 测试和验证工具
- `test_gui.bat` - GUI测试批处理文件
- `test_gui_functions.py` - GUI功能测试脚本
- `verify_fixes.py` - 验证修复结果的脚本
- `verify_gui.bat` - GUI验证批处理文件
- `verify_modification.py` - 验证修改结果的脚本
- `TelemetryTestSuite.ps1` - PowerShell遥测测试套件
- `telemetry_test_suite.sh` - Shell遥测测试套件

### 🛠️ 开发和安装工具
- `install_enhanced.ps1` - 增强版安装脚本
- `create_mock_files.bat` - 创建模拟文件的批处理
- `mock_storage.py` - 模拟存储文件的Python脚本

### 📚 文档和报告
- `COMPREHENSIVE_ANALYSIS_REPORT.md` - 综合分析报告
- `EDITOR_ISSUES_SOLUTION.md` - 编辑器问题解决方案文档
- `PROJECT_SUMMARY.md` - 项目总结文档
- `TESTING_GUIDE.md` - 测试指南文档

### 🗂️ 临时文件
- `temp_ps_version.txt` - 临时PowerShell版本信息文件

## 🔄 如何使用这些文件

如果您需要使用这些归档文件：

1. **开发和调试**：可以使用测试脚本和验证工具
2. **问题排查**：参考解决方案文档和分析报告
3. **功能扩展**：基于核心脚本进行二次开发

## ⚠️ 注意事项

- 这些文件已经过测试，功能正常
- 如需使用，请确保依赖的配置文件和目录结构完整
- 建议在使用前阅读相关文档

## 🏠 返回主目录

主目录现在只包含必要的启动文件：
- `启动器.py` - 主要的Python启动器
- `OneClick-Enhanced.bat` - 一键增强批处理
- `Quick-Start.bat` - 快速启动批处理

这样的结构更加简洁，便于日常使用。
