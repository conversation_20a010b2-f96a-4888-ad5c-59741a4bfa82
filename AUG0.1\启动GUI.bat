@echo off
chcp 65001 >nul
title AUG 0.1 Enhanced Telemetry ID Modifier - GUI启动器

echo.
echo ========================================
echo  AUG 0.1 Enhanced Telemetry ID Modifier
echo  图形用户界面版本启动器
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python，请先安装Python 3.7或更高版本
    echo.
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo [信息] 检测到Python环境
python --version

:: 检查必需的模块
echo.
echo [信息] 检查必需的Python模块...

python -c "import tkinter" 2>nul
if errorlevel 1 (
    echo [错误] tkinter模块未找到，请安装完整的Python环境
    pause
    exit /b 1
)

python -c "import psutil" 2>nul
if errorlevel 1 (
    echo [警告] psutil模块未找到，正在尝试安装...
    pip install psutil
    if errorlevel 1 (
        echo [错误] 安装psutil失败，请手动安装: pip install psutil
        pause
        exit /b 1
    )
)

echo [信息] 所有必需模块检查完成

:: 检查GUI模块文件
echo.
echo [信息] 检查GUI模块文件...

if not exist "gui_modules" (
    echo [错误] gui_modules目录不存在
    pause
    exit /b 1
)

if not exist "gui_modules\__init__.py" (
    echo [错误] gui_modules模块初始化文件不存在
    pause
    exit /b 1
)

if not exist "gui_launcher_simple.py" (
    echo [错误] GUI启动文件不存在
    pause
    exit /b 1
)

echo [信息] GUI模块文件检查完成

:: 启动GUI应用
echo.
echo [信息] 正在启动AUG 0.1 GUI应用...
echo.

python gui_launcher_simple.py

if errorlevel 1 (
    echo.
    echo [错误] GUI应用启动失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查Python环境是否正确安装
    echo 2. 检查所有必需的模块文件是否存在
    echo 3. 查看错误信息并联系技术支持
    echo.
    pause
    exit /b 1
)

echo.
echo [信息] AUG 0.1 GUI应用已关闭
pause
