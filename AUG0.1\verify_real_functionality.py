#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AUG 0.1 真实功能验证脚本
验证所有功能是否对真实文件有效果
"""

import os
import sys
import json
import shutil
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_editor_detection():
    """验证编辑器检测功能"""
    print("🔍 验证编辑器检测功能...")
    
    try:
        from gui_modules.editor_detector import EditorDetector
        
        detector = EditorDetector()
        results = detector.detect_all_editors()
        
        real_editors = []
        for editor_id, info in results.items():
            if info.get("storage_exists", False):
                storage_path = info.get("storage_path", "")
                if os.path.exists(storage_path):
                    real_editors.append({
                        "id": editor_id,
                        "name": info.get("name", ""),
                        "path": storage_path,
                        "size": info.get("storage_size", 0),
                        "telemetry_fields": info.get("telemetry_fields", 0)
                    })
        
        print(f"✅ 发现 {len(real_editors)} 个真实的编辑器存储文件:")
        for editor in real_editors:
            print(f"  📝 {editor['name']}")
            print(f"     路径: {editor['path']}")
            print(f"     大小: {editor['size']} 字节")
            print(f"     遥测字段: {editor['telemetry_fields']} 个")
            
        return real_editors
        
    except Exception as e:
        print(f"❌ 编辑器检测验证失败: {e}")
        return []

def verify_telemetry_fields():
    """验证遥测字段读取功能"""
    print("\n🔍 验证遥测字段读取功能...")
    
    try:
        from gui_modules.editor_detector import EditorDetector
        
        detector = EditorDetector()
        results = detector.detect_all_editors()
        
        total_fields_found = 0
        
        for editor_id, info in results.items():
            if info.get("storage_exists", False):
                storage_path = info.get("storage_path", "")
                
                try:
                    with open(storage_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    telemetry_fields = {}
                    for key, value in data.items():
                        if key.startswith('telemetry.'):
                            telemetry_fields[key] = value
                            
                    if telemetry_fields:
                        print(f"✅ {info.get('name', editor_id)} 中的遥测字段:")
                        for field, value in list(telemetry_fields.items())[:5]:  # 只显示前5个
                            print(f"     {field}: {str(value)[:50]}...")
                        if len(telemetry_fields) > 5:
                            print(f"     ... 还有 {len(telemetry_fields) - 5} 个字段")
                        total_fields_found += len(telemetry_fields)
                    
                except Exception as e:
                    print(f"  ⚠️ 无法读取 {info.get('name', editor_id)}: {e}")
        
        print(f"✅ 总共发现 {total_fields_found} 个真实的遥测字段")
        return total_fields_found > 0
        
    except Exception as e:
        print(f"❌ 遥测字段验证失败: {e}")
        return False

def verify_backup_functionality():
    """验证备份功能"""
    print("\n🔍 验证备份功能...")
    
    try:
        from gui_modules.backup_manager import BackupManager
        from gui_modules.editor_detector import EditorDetector
        
        backup_manager = BackupManager()
        detector = EditorDetector()
        
        # 找到一个真实的编辑器存储文件
        results = detector.detect_all_editors()
        test_file = None
        test_editor = None
        
        for editor_id, info in results.items():
            if info.get("storage_exists", False):
                test_file = info.get("storage_path", "")
                test_editor = info.get("name", editor_id)
                break
        
        if test_file and os.path.exists(test_file):
            print(f"✅ 使用真实文件测试备份: {test_file}")
            
            # 创建备份
            backup_path = backup_manager.create_backup(test_file, test_editor)
            
            if backup_path and os.path.exists(backup_path):
                print(f"✅ 备份创建成功: {backup_path}")
                
                # 验证备份内容
                original_size = os.path.getsize(test_file)
                backup_size = os.path.getsize(backup_path)
                
                print(f"  📊 原文件大小: {original_size} 字节")
                print(f"  📊 备份文件大小: {backup_size} 字节")
                
                if original_size == backup_size:
                    print("✅ 备份文件大小匹配")
                else:
                    print("⚠️ 备份文件大小不匹配")
                
                # 验证备份信息
                backup_info = backup_manager.get_backup_info(backup_path)
                if backup_info:
                    print("✅ 备份信息读取成功")
                    print(f"  🔒 完整性检查: {'通过' if backup_info.get('integrity_ok', False) else '失败'}")
                
                return True
            else:
                print("❌ 备份创建失败")
                return False
        else:
            print("⚠️ 未找到可用于测试的真实编辑器文件")
            return False
            
    except Exception as e:
        print(f"❌ 备份功能验证失败: {e}")
        return False

def verify_database_scanning():
    """验证数据库扫描功能"""
    print("\n🔍 验证数据库扫描功能...")
    
    try:
        from gui_modules.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 扫描真实的数据库文件
        databases = db_manager.scan_databases()
        
        real_databases = [db for db in databases if db.get("accessible", False)]
        
        print(f"✅ 发现 {len(databases)} 个数据库文件，其中 {len(real_databases)} 个可访问")
        
        for db in real_databases[:3]:  # 只显示前3个
            print(f"  🗃️ {os.path.basename(db.get('path', ''))}")
            print(f"     大小: {db_manager.format_size(db.get('size', 0))}")
            print(f"     记录数: {db.get('total_records', 0)}")
            print(f"     Augment记录: {db.get('augment_records', 0)}")
            
        return len(real_databases) > 0
        
    except Exception as e:
        print(f"❌ 数据库扫描验证失败: {e}")
        return False

def verify_workspace_scanning():
    """验证工作区扫描功能"""
    print("\n🔍 验证工作区扫描功能...")
    
    try:
        from gui_modules.workspace_manager import WorkspaceManager
        
        ws_manager = WorkspaceManager()
        
        # 扫描真实的工作区
        workspaces = ws_manager.scan_workspaces()
        
        real_workspaces = [ws for ws in workspaces if os.path.exists(ws.get("workspace_path", ""))]
        
        print(f"✅ 发现 {len(workspaces)} 个工作区，其中 {len(real_workspaces)} 个真实存在")
        
        # 按编辑器分组统计
        editor_stats = {}
        total_size = 0
        
        for ws in real_workspaces:
            editor = ws.get("editor_name", "Unknown")
            size = ws.get("size", 0)
            
            if editor not in editor_stats:
                editor_stats[editor] = {"count": 0, "size": 0}
            editor_stats[editor]["count"] += 1
            editor_stats[editor]["size"] += size
            total_size += size
        
        print(f"✅ 工作区统计:")
        for editor, stats in editor_stats.items():
            print(f"  💾 {editor}: {stats['count']} 个工作区, {ws_manager.format_size(stats['size'])}")
        
        print(f"✅ 总大小: {ws_manager.format_size(total_size)}")
        
        return len(real_workspaces) > 0
        
    except Exception as e:
        print(f"❌ 工作区扫描验证失败: {e}")
        return False

def verify_field_modification_capability():
    """验证字段修改能力（不实际修改）"""
    print("\n🔍 验证字段修改能力...")
    
    try:
        from gui_modules.telemetry_modifier import TelemetryModifier
        from gui_modules.editor_detector import EditorDetector
        
        modifier = TelemetryModifier()
        detector = EditorDetector()
        
        # 获取真实编辑器
        results = detector.detect_all_editors()
        real_editors = [editor_id for editor_id, info in results.items() 
                       if info.get("storage_exists", False)]
        
        if real_editors:
            print(f"✅ 可修改的编辑器: {len(real_editors)} 个")
            
            # 测试不同模式的字段
            modes = ["quick", "standard", "enhanced"]
            for mode in modes:
                fields = modifier.get_default_fields_by_mode(mode)
                print(f"  🔧 {mode} 模式: {len(fields)} 个字段")
            
            # 测试操作验证
            errors, warnings = modifier.validate_operation("enhanced", real_editors)
            print(f"✅ 操作验证: {len(errors)} 个错误, {len(warnings)} 个警告")
            
            # 测试操作摘要
            summary = modifier.get_operation_summary("enhanced", real_editors)
            print(f"✅ 操作摘要: {summary['editor_count']} 个编辑器, {summary['field_count']} 个字段")
            
            return True
        else:
            print("⚠️ 未找到可修改的编辑器")
            return False
            
    except Exception as e:
        print(f"❌ 字段修改能力验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 AUG 0.1 真实功能验证开始")
    print("=" * 60)
    
    verifications = [
        ("编辑器检测", verify_editor_detection),
        ("遥测字段读取", verify_telemetry_fields),
        ("备份功能", verify_backup_functionality),
        ("数据库扫描", verify_database_scanning),
        ("工作区扫描", verify_workspace_scanning),
        ("字段修改能力", verify_field_modification_capability)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in verifications:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 验证通过")
            else:
                failed += 1
                print(f"❌ {test_name} 验证失败")
        except Exception as e:
            print(f"❌ {test_name} 验证异常: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"🎯 验证结果: {passed} 个通过, {failed} 个失败")
    
    if failed == 0:
        print("🎉 所有真实功能验证通过！")
        print("✅ AUG 0.1 的所有功能都能对真实文件产生效果")
    elif passed > failed:
        print("✅ 大部分功能验证通过，系统基本可用")
    else:
        print("⚠️ 多个功能需要修复")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
