@echo off
chcp 65001 >nul
title Augment ID 修改器 - 一键修复所有问题

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Augment ID 修改器 - 一键修复工具                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 此工具将自动解决以下问题：
echo.
echo ✓ Cursor 编辑器的 UTF-8 BOM 编码问题
echo ✓ VSCodium 等编辑器的缺失配置文件问题  
echo ✓ Code - OSS 编辑器支持问题
echo ✓ 自动修改所有编辑器的遥测ID
echo.
echo 支持的编辑器：
echo   • Visual Studio Code
echo   • Visual Studio Code Insiders  
echo   • Cursor Editor
echo   • VSCodium
echo   • Code - OSS
echo.

set /p choice="是否继续执行一键修复？(Y/N): "
if /i "%choice%" neq "Y" (
    echo 操作已取消。
    pause
    exit /b
)

echo.
echo ════════════════════════════════════════════════════════════════
echo 第一步：修复编辑器配置问题
echo ════════════════════════════════════════════════════════════════
echo.

echo 正在运行编辑器问题修复脚本...
python "%~dp0fix_editor_issues.py" --auto-fix

if %errorlevel% neq 0 (
    echo.
    echo ✗ 编辑器问题修复失败！
    echo 请检查错误信息并手动运行 fix_editor_issues.py
    pause
    exit /b 1
)

echo.
echo ✓ 编辑器配置问题修复完成！
echo.

echo ════════════════════════════════════════════════════════════════
echo 第二步：修改遥测ID
echo ════════════════════════════════════════════════════════════════
echo.

echo 正在运行遥测ID修改脚本...
python "%~dp0direct_modify.py" --mode enhanced --create-missing

if %errorlevel% neq 0 (
    echo.
    echo ✗ 遥测ID修改失败！
    echo 请检查错误信息并手动运行 direct_modify.py
    pause
    exit /b 1
)

echo.
echo ✓ 遥测ID修改完成！
echo.

echo ════════════════════════════════════════════════════════════════
echo 修复完成总结
echo ════════════════════════════════════════════════════════════════
echo.
echo ✓ 所有编辑器配置问题已修复
echo ✓ 所有支持的编辑器遥测ID已更新
echo ✓ 备份文件已保存到 backups 目录
echo.
echo ⚠ 重要提醒：
echo   请重启所有相关编辑器以应用更改！
echo.
echo 如需查看详细信息，请参考：
echo   • EDITOR_ISSUES_SOLUTION.md - 问题解决方案文档
echo   • backups/ 目录 - 备份文件
echo   • logs/ 目录 - 操作日志
echo.

pause
