#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
遥测修改器模块
负责修改编辑器的遥测标识符
"""

import json
import os
import uuid
import random
import subprocess
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from pathlib import Path

class TelemetryModifier:
    """遥测修改器类"""
    
    def __init__(self):
        """初始化遥测修改器"""
        self.powershell_script = Path("scripts/enhanced_id_modifier.ps1")
        self.bash_script = Path("scripts/enhanced_id_modifier.sh")
        self.is_running = False
        self.current_operation = None
        self.progress_callback = None
        self.log_callback = None
        
    def set_progress_callback(self, callback: Callable[[int, str], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
        
    def set_log_callback(self, callback: Callable[[str, str], None]):
        """设置日志回调函数"""
        self.log_callback = callback
        
    def log_message(self, level: str, message: str):
        """记录日志消息"""
        if self.log_callback:
            self.log_callback(level, message)
        print(f"[{level}] {message}")
        
    def update_progress(self, progress: int, status: str):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(progress, status)
            
    def generate_machine_id(self) -> str:
        """生成机器ID"""
        # 生成64位十六进制ID
        return ''.join(random.choices('0123456789abcdef', k=64))
        
    def generate_device_id(self) -> str:
        """生成设备ID"""
        return str(uuid.uuid4())
        
    def generate_session_id(self) -> str:
        """生成会话ID"""
        return str(uuid.uuid4())
        
    def generate_timestamp(self) -> str:
        """生成随机时间戳"""
        # 生成过去一年内的随机日期
        days = random.randint(1, 365)
        random_date = datetime.now() - timedelta(days=days)
        return random_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        
    def generate_boolean(self) -> bool:
        """生成随机布尔值"""
        return random.choice([True, False])
        
    def generate_value_by_type(self, field_type: str) -> str:
        """根据类型生成值"""
        type_generators = {
            "hex64": self.generate_machine_id,
            "hex32": lambda: ''.join(random.choices('0123456789abcdef', k=32)),
            "hex16": lambda: ''.join(random.choices('0123456789abcdef', k=16)),
            "hex8": lambda: ''.join(random.choices('0123456789abcdef', k=8)),
            "uuid4": self.generate_device_id,
            "timestamp": self.generate_timestamp,
            "boolean": self.generate_boolean
        }
        
        generator = type_generators.get(field_type, self.generate_device_id)
        return generator()
        
    def modify_storage_file(self, storage_path: str, fields_to_modify: Dict[str, str]) -> bool:
        """修改存储文件"""
        try:
            self.log_message("INFO", f"正在修改存储文件: {storage_path}")
            
            # 读取原始文件
            with open(storage_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 修改字段
            modified_count = 0
            for field_name, field_type in fields_to_modify.items():
                new_value = self.generate_value_by_type(field_type)
                data[field_name] = new_value
                modified_count += 1
                self.log_message("SUCCESS", f"更新 {field_name}: {new_value}")
                
            # 保存修改后的文件
            with open(storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            self.log_message("SUCCESS", f"成功修改 {modified_count} 个遥测字段")
            return True
            
        except Exception as e:
            self.log_message("ERROR", f"修改存储文件失败: {str(e)}")
            return False
            
    def run_powershell_script(self, mode: str, editors: List[str] = None, fields: List[str] = None) -> bool:
        """运行PowerShell脚本"""
        try:
            if not self.powershell_script.exists():
                self.log_message("ERROR", "PowerShell脚本不存在")
                return False
                
            # 构建命令参数
            cmd = ["powershell", "-ExecutionPolicy", "Bypass", "-File", str(self.powershell_script)]
            
            if mode == "enhanced":
                cmd.extend(["-Enhanced", "-All"])
            elif mode == "standard":
                cmd.extend(["-All", "-Fields", "machineId,deviceId,sessionId,instanceId"])
            elif mode == "quick":
                cmd.extend(["-All", "-Fields", "machineId,deviceId"])
            elif mode == "custom":
                if editors:
                    cmd.extend(["-Editor", ",".join(editors)])
                if fields:
                    cmd.extend(["-Fields", ",".join(fields)])
                    
            self.log_message("INFO", f"执行命令: {' '.join(cmd)}")
            
            # 执行命令
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            # 读取输出
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                self.log_message("SUCCESS", "PowerShell脚本执行成功")
                if stdout:
                    for line in stdout.split('\n'):
                        if line.strip():
                            self.log_message("INFO", line.strip())
                return True
            else:
                self.log_message("ERROR", f"PowerShell脚本执行失败: {stderr}")
                return False
                
        except Exception as e:
            self.log_message("ERROR", f"执行PowerShell脚本时出错: {str(e)}")
            return False
            
    def modify_telemetry_async(self, mode: str, editors: List[str] = None, fields: List[str] = None):
        """异步修改遥测数据"""
        def worker():
            try:
                self.is_running = True
                self.update_progress(0, "开始操作...")
                
                # 使用PowerShell脚本
                success = self.run_powershell_script(mode, editors, fields)
                
                if success:
                    self.update_progress(100, "操作完成")
                    self.log_message("SUCCESS", "遥测ID修改完成")
                else:
                    self.update_progress(0, "操作失败")
                    self.log_message("ERROR", "遥测ID修改失败")
                    
            except Exception as e:
                self.log_message("ERROR", f"操作过程中出错: {str(e)}")
                self.update_progress(0, "操作出错")
            finally:
                self.is_running = False
                
        # 在新线程中执行
        self.current_operation = threading.Thread(target=worker)
        self.current_operation.start()
        
    def stop_operation(self):
        """停止当前操作"""
        if self.is_running and self.current_operation:
            self.log_message("WARNING", "正在停止操作...")
            # 注意：这里只是设置标志，实际的停止需要在操作中检查
            self.is_running = False
            
    def get_default_fields_by_mode(self, mode: str) -> List[str]:
        """根据模式获取默认字段列表"""
        mode_fields = {
            "quick": [
                "telemetry.machineId",
                "telemetry.devDeviceId"
            ],
            "standard": [
                "telemetry.machineId",
                "telemetry.devDeviceId",
                "telemetry.sessionId",
                "telemetry.instanceId",
                "telemetry.sqmId",
                "telemetry.firstSessionDate",
                "telemetry.lastSessionDate",
                "telemetry.isNewAppInstall",
                "telemetry.optIn"
            ],
            "enhanced": [
                # 核心标识符
                "telemetry.machineId",
                "telemetry.devDeviceId",
                "telemetry.sessionId",
                "telemetry.sqmId",
                "telemetry.instanceId",
                "telemetry.installationId",
                # 时间戳
                "telemetry.firstSessionDate",
                "telemetry.lastSessionDate",
                # 用户相关
                "telemetry.userId",
                "telemetry.accountId",
                "telemetry.profileId",
                "telemetry.workspaceId",
                # 硬件相关
                "telemetry.hardwareId",
                "telemetry.systemId",
                "telemetry.platformId",
                "telemetry.cpuId",
                "telemetry.memoryId",
                # 网络相关
                "telemetry.networkId",
                "telemetry.locationId",
                "telemetry.ipId",
                # 隐私相关
                "telemetry.consentId",
                "telemetry.privacyId",
                "telemetry.gdprId",
                # 其他
                "telemetry.optIn",
                "telemetry.isNewAppInstall"
            ]
        }
        
        return mode_fields.get(mode, mode_fields["standard"])
        
    def validate_operation(self, mode: str, editors: List[str], fields: List[str] = None) -> tuple:
        """验证操作参数"""
        errors = []
        warnings = []
        
        # 验证模式
        valid_modes = ["enhanced", "standard", "quick", "custom"]
        if mode not in valid_modes:
            errors.append(f"无效的操作模式: {mode}")
            
        # 验证编辑器
        if not editors:
            errors.append("未选择任何编辑器")
        else:
            # 检查编辑器是否存在
            from .editor_detector import EditorDetector
            detector = EditorDetector()
            detection_results = detector.detect_all_editors()
            
            for editor_id in editors:
                if editor_id not in detection_results:
                    errors.append(f"未知的编辑器: {editor_id}")
                elif not detection_results[editor_id].get("installed", False):
                    warnings.append(f"编辑器 {editor_id} 未安装")
                elif detection_results[editor_id].get("running", False):
                    warnings.append(f"编辑器 {editor_id} 正在运行，建议先关闭")
                    
        # 验证字段（自定义模式）
        if mode == "custom" and not fields:
            errors.append("自定义模式下未选择任何字段")
            
        return errors, warnings
        
    def get_operation_summary(self, mode: str, editors: List[str], fields: List[str] = None) -> Dict:
        """获取操作摘要"""
        if not fields:
            fields = self.get_default_fields_by_mode(mode)
            
        return {
            "mode": mode,
            "editors": editors,
            "fields": fields,
            "editor_count": len(editors),
            "field_count": len(fields),
            "estimated_time": len(editors) * 2  # 估计每个编辑器2秒
        }
