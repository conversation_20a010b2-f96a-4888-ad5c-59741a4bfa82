import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import subprocess
import os
import sys
import json
import threading
import time
from pathlib import Path

# 简化主题系统 - 避免复杂依赖
class AdvancedTheme:
    """简化的高级主题系统"""

    def __init__(self, theme_name="modern_blue"):
        self.current_theme = theme_name
        self._init_theme_colors()
        self._init_fonts()
        self._init_spacing()
        self._init_icons()

    def _init_theme_colors(self):
        """初始化主题颜色"""
        # 基础颜色
        self.PRIMARY_COLOR = "#2563eb"
        self.SECONDARY_COLOR = "#64748b"
        self.SUCCESS_COLOR = "#10b981"
        self.WARNING_COLOR = "#f59e0b"
        self.ERROR_COLOR = "#ef4444"
        self.BACKGROUND_COLOR = "#f8fafc"
        self.CARD_COLOR = "#ffffff"
        self.TEXT_COLOR = "#1e293b"
        self.BORDER_COLOR = "#e2e8f0"

        # 扩展颜色
        self.PRIMARY_HOVER = "#1d4ed8"
        self.PRIMARY_LIGHT = "#dbeafe"
        self.SUCCESS_LIGHT = "#d1fae5"
        self.WARNING_LIGHT = "#fef3c7"
        self.ERROR_LIGHT = "#fee2e2"
        self.INFO_COLOR = "#3b82f6"
        self.INFO_LIGHT = "#dbeafe"
        self.TEXT_SECONDARY = "#6b7280"
        self.TEXT_TERTIARY = "#9ca3af"
        self.BORDER_FOCUS = "#3b82f6"
        self.BORDER_PRIMARY = "#e5e7eb"

        # 兼容性别名
        self.PRIMARY = self.PRIMARY_COLOR
        self.SECONDARY = self.SECONDARY_COLOR
        self.SUCCESS = self.SUCCESS_COLOR
        self.WARNING = self.WARNING_COLOR
        self.ERROR = self.ERROR_COLOR
        self.INFO = self.INFO_COLOR
        self.BG_SECONDARY = self.BACKGROUND_COLOR
        self.BG_CARD = self.CARD_COLOR
        self.TEXT_PRIMARY = self.TEXT_COLOR

    def _init_fonts(self):
        """初始化字体系统"""
        self.FONTS = {
            "display": ("Microsoft YaHei UI", 24, "bold"),
            "title_1": ("Microsoft YaHei UI", 20, "bold"),
            "title_2": ("Microsoft YaHei UI", 16, "bold"),
            "title_3": ("Microsoft YaHei UI", 14, "bold"),
            "headline": ("Microsoft YaHei UI", 13, "bold"),
            "body": ("Microsoft YaHei UI", 11),
            "caption": ("Microsoft YaHei UI", 10),
            "footnote": ("Microsoft YaHei UI", 9),
        }

        # 兼容性别名
        self.TITLE_FONT = self.FONTS["title_1"]
        self.HEADER_FONT = self.FONTS["title_2"]
        self.BODY_FONT = self.FONTS["body"]
        self.BUTTON_FONT = self.FONTS["headline"]
        self.CAPTION_FONT = self.FONTS["caption"]

    def _init_spacing(self):
        """初始化间距系统"""
        self.SPACING = {
            "xs": 4, "sm": 8, "md": 12, "lg": 16,
            "xl": 20, "2xl": 24, "3xl": 32, "4xl": 40,
            "5xl": 48, "6xl": 64
        }

    def _init_icons(self):
        """初始化图标系统"""
        self.ICONS = {
            "crown": "👑", "rocket": "🚀", "magic": "✨",
            "settings": "⚙️", "info": "ℹ️", "check": "✅",
            "warning": "⚠️", "error": "❌", "search": "🔍",
            "clean": "🧹", "shield": "🛡️", "heart": "❤️",
            "star": "⭐", "fire": "🔥", "gem": "💎",
            "edit": "✏️", "database": "🗄️", "play": "▶️",
            "stop": "⏹️", "folder": "📁", "file": "📄",
            "refresh": "🔄", "backup": "💾", "about": "ℹ️",
            "brain": "🧠", "target": "🎯", "lightning": "⚡",
            "lock": "🔒", "unlock": "🔓", "eye": "👁️"
        }

    def switch_theme(self, theme_name):
        """切换主题"""
        self.current_theme = theme_name
        # 简化的主题切换实现
        pass

# 简化的动画管理器
class AnimationManager:
    """简化的动画管理器"""

    @staticmethod
    def fade_in(widget, duration=300, callback=None):
        """简化的淡入动画"""
        if callback:
            widget.after(duration, callback)

# 简化的响应式布局
class ResponsiveLayout:
    """简化的响应式布局"""

    def __init__(self, root):
        self.root = root
        self.callbacks = []

    def add_callback(self, callback):
        """添加回调"""
        self.callbacks.append(callback)

class AugmentApp:
    def __init__(self, root):
        self.root = root
        self.theme = AdvancedTheme("modern_blue")
        self.animation = AnimationManager()
        self.responsive = ResponsiveLayout(root)

        # 配置窗口
        self.setup_window()

        # 配置样式
        self.setup_styles()

        # 创建界面
        self.create_interface()

        # 初始化功能
        self.initialize_features()

    def setup_window(self):
        """配置高级主窗口"""
        # 窗口基本设置
        self.root.title(f"{self.theme.ICONS['crown']} AUG 0.1 Premium - Augment ID修改器")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        self.root.configure(bg=self.theme.BACKGROUND_COLOR)

        # 设置窗口图标
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # 设置窗口属性
        self.root.attributes('-alpha', 0.98)  # 轻微透明效果

        # 响应式布局回调
        self.responsive.add_callback(self._on_responsive_change)

        # 居中显示窗口
        self.center_window()

        # 添加窗口动画效果
        self._animate_window_open()

    def _animate_window_open(self):
        """窗口打开动画"""
        # 从小到大的缩放效果
        original_alpha = 0.0
        target_alpha = 0.98
        steps = 20

        def animate(step=0):
            if step <= steps:
                alpha = original_alpha + (target_alpha - original_alpha) * (step / steps)
                self.root.attributes('-alpha', alpha)
                if step < steps:
                    self.root.after(20, lambda: animate(step + 1))

        animate()

    def _on_responsive_change(self, breakpoint):
        """响应式布局变化处理"""
        if breakpoint == "sm":
            # 小屏幕布局调整
            self.root.geometry("900x700")
        elif breakpoint == "md":
            # 中等屏幕布局调整
            self.root.geometry("1000x750")
        elif breakpoint == "lg":
            # 大屏幕布局调整
            self.root.geometry("1200x800")
        else:  # xl
            # 超大屏幕布局调整
            self.root.geometry("1400x900")

    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def setup_styles(self):
        """配置高级样式系统"""
        style = ttk.Style()

        # 使用现代主题
        style.theme_use('clam')

        # 配置高级Notebook样式
        style.configure('Premium.TNotebook',
                       background=self.theme.BACKGROUND_COLOR,
                       borderwidth=0,
                       tabmargins=[0, 0, 0, 0])

        style.configure('Premium.TNotebook.Tab',
                       padding=[self.theme.SPACING["xl"], self.theme.SPACING["md"]],
                       font=self.theme.FONTS["headline"],
                       background=self.theme.CARD_COLOR,
                       foreground=self.theme.TEXT_SECONDARY,
                       borderwidth=0,
                       focuscolor='none')

        style.map('Premium.TNotebook.Tab',
                 background=[('selected', self.theme.PRIMARY_COLOR),
                           ('active', self.theme.PRIMARY_LIGHT),
                           ('!active', self.theme.CARD_COLOR)])

        style.map('Premium.TNotebook.Tab',
                 foreground=[('selected', 'white'),
                           ('active', self.theme.PRIMARY_COLOR),
                           ('!active', self.theme.TEXT_SECONDARY)])

        # 配置高级按钮样式
        self._configure_button_styles(style)

        # 配置输入框样式
        self._configure_entry_styles(style)

        # 配置进度条样式
        self._configure_progressbar_styles(style)

        # 配置树形视图样式
        self._configure_treeview_styles(style)

        # 配置滚动条样式
        self._configure_scrollbar_styles(style)

    def _configure_button_styles(self, style):
        """配置按钮样式"""
        # 主要按钮
        style.configure('Primary.TButton',
                       font=self.theme.FONTS["headline"],
                       padding=[self.theme.SPACING["xl"], self.theme.SPACING["md"]],
                       background=self.theme.PRIMARY_COLOR,
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat')

        style.map('Primary.TButton',
                 background=[('active', self.theme.PRIMARY_HOVER),
                           ('pressed', "#1e40af")])

        # 成功按钮
        style.configure('Success.TButton',
                       font=self.theme.FONTS["body"],
                       padding=[self.theme.SPACING["lg"], self.theme.SPACING["sm"]],
                       background=self.theme.SUCCESS_COLOR,
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat')

        # 警告按钮
        style.configure('Warning.TButton',
                       font=self.theme.FONTS["body"],
                       padding=[self.theme.SPACING["lg"], self.theme.SPACING["sm"]],
                       background=self.theme.WARNING_COLOR,
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat')

        # 次要按钮
        style.configure('Secondary.TButton',
                       font=self.theme.FONTS["body"],
                       padding=[self.theme.SPACING["lg"], self.theme.SPACING["sm"]],
                       background=self.theme.SECONDARY_COLOR,
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat')

        # 轮廓按钮
        style.configure('Outline.TButton',
                       font=self.theme.FONTS["body"],
                       padding=[self.theme.SPACING["lg"], self.theme.SPACING["sm"]],
                       background=self.theme.CARD_COLOR,
                       foreground=self.theme.PRIMARY_COLOR,
                       borderwidth=1,
                       relief='solid')

    def _configure_entry_styles(self, style):
        """配置输入框样式"""
        style.configure('Premium.TEntry',
                       fieldbackground=self.theme.CARD_COLOR,
                       borderwidth=1,
                       relief='solid',
                       bordercolor=self.theme.BORDER_PRIMARY,
                       focuscolor=self.theme.BORDER_FOCUS,
                       padding=[self.theme.SPACING["sm"], self.theme.SPACING["sm"]])

    def _configure_progressbar_styles(self, style):
        """配置进度条样式"""
        style.configure('Premium.Horizontal.TProgressbar',
                       background=self.theme.PRIMARY_COLOR,
                       troughcolor=self.theme.PRIMARY_LIGHT,
                       borderwidth=0,
                       lightcolor=self.theme.PRIMARY_COLOR,
                       darkcolor=self.theme.PRIMARY_COLOR)

    def _configure_treeview_styles(self, style):
        """配置树形视图样式"""
        style.configure('Premium.Treeview',
                       background=self.theme.CARD_COLOR,
                       foreground=self.theme.TEXT_PRIMARY,
                       fieldbackground=self.theme.CARD_COLOR,
                       borderwidth=1,
                       relief='solid')

        style.configure('Premium.Treeview.Heading',
                       background=self.theme.PRIMARY_LIGHT,
                       foreground=self.theme.PRIMARY_COLOR,
                       font=self.theme.FONTS["headline"])

    def _configure_scrollbar_styles(self, style):
        """配置滚动条样式"""
        style.configure('Premium.Vertical.TScrollbar',
                       background=self.theme.BORDER_PRIMARY,
                       troughcolor=self.theme.PRIMARY_LIGHT,
                       borderwidth=0,
                       arrowcolor=self.theme.TEXT_SECONDARY)

    def create_interface(self):
        """创建高级现代化界面"""
        # 主容器 - 使用渐变背景效果
        self.main_container = tk.Frame(self.root, bg=self.theme.BACKGROUND_COLOR)
        self.main_container.pack(fill=tk.BOTH, expand=True,
                               padx=self.theme.SPACING["xl"],
                               pady=self.theme.SPACING["xl"])

        # 创建顶部导航栏
        self.create_navigation_bar()

        # 创建高级标题区域
        self.create_premium_header()

        # 创建内容区域
        self.create_premium_content_area()

        # 创建增强状态栏
        self.create_premium_status_bar()

        # 添加快捷键绑定
        self.setup_keyboard_shortcuts()

        # 启动界面动画
        self.animation.fade_in(self.main_container)

    def create_navigation_bar(self):
        """创建顶部导航栏"""
        nav_frame = tk.Frame(self.main_container,
                           bg=self.theme.CARD_COLOR,
                           height=60,
                           relief='flat',
                           bd=0)
        nav_frame.pack(fill=tk.X, pady=(0, self.theme.SPACING["lg"]))
        nav_frame.pack_propagate(False)

        # 左侧品牌区域
        brand_frame = tk.Frame(nav_frame, bg=self.theme.CARD_COLOR)
        brand_frame.pack(side=tk.LEFT, fill=tk.Y, padx=self.theme.SPACING["lg"])

        brand_label = tk.Label(brand_frame,
                              text=f"{self.theme.ICONS['crown']} AUG Premium",
                              font=self.theme.FONTS["title_2"],
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.PRIMARY_COLOR)
        brand_label.pack(side=tk.LEFT, pady=self.theme.SPACING["md"])

        # 右侧操作区域
        actions_frame = tk.Frame(nav_frame, bg=self.theme.CARD_COLOR)
        actions_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=self.theme.SPACING["lg"])

        # 主题切换按钮
        theme_btn = tk.Button(actions_frame,
                             text=f"{self.theme.ICONS['magic']}",
                             font=self.theme.FONTS["body"],
                             bg=self.theme.CARD_COLOR,
                             fg=self.theme.TEXT_SECONDARY,
                             relief='flat',
                             bd=0,
                             padx=self.theme.SPACING["sm"],
                             pady=self.theme.SPACING["sm"],
                             command=self.toggle_theme)
        theme_btn.pack(side=tk.RIGHT, padx=self.theme.SPACING["sm"], pady=self.theme.SPACING["md"])

        # 设置按钮
        settings_btn = tk.Button(actions_frame,
                               text=f"{self.theme.ICONS['settings']}",
                               font=self.theme.FONTS["body"],
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_SECONDARY,
                               relief='flat',
                               bd=0,
                               padx=self.theme.SPACING["sm"],
                               pady=self.theme.SPACING["sm"],
                               command=self.show_settings)
        settings_btn.pack(side=tk.RIGHT, padx=self.theme.SPACING["sm"], pady=self.theme.SPACING["md"])

    def toggle_theme(self):
        """切换主题"""
        themes = ["modern_blue", "dark_mode", "elegant_purple"]
        current_index = themes.index(self.theme.current_theme)
        next_index = (current_index + 1) % len(themes)
        next_theme = themes[next_index]

        # 切换主题
        self.theme.switch_theme(next_theme)

        # 重新应用样式
        self.setup_styles()

        # 更新界面颜色
        self._update_interface_colors()

        # 显示切换提示
        self.show_theme_notification(next_theme)

    def show_theme_notification(self, theme_name):
        """显示主题切换通知"""
        theme_names = {
            "modern_blue": "现代蓝色",
            "dark_mode": "深色模式",
            "elegant_purple": "优雅紫色"
        }

        notification = tk.Toplevel(self.root)
        notification.title("主题切换")
        notification.geometry("300x100")
        notification.configure(bg=self.theme.CARD_COLOR)
        notification.transient(self.root)
        notification.attributes('-topmost', True)

        # 居中显示
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 150
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 50
        notification.geometry(f"300x100+{x}+{y}")

        label = tk.Label(notification,
                        text=f"{self.theme.ICONS['magic']} 已切换到{theme_names.get(theme_name, theme_name)}主题",
                        font=self.theme.FONTS["body"],
                        bg=self.theme.CARD_COLOR,
                        fg=self.theme.TEXT_PRIMARY)
        label.pack(expand=True)

        # 3秒后自动关闭
        notification.after(3000, notification.destroy)

    def show_settings(self):
        """显示设置对话框"""
        # 这里可以实现设置对话框
        pass

    def setup_keyboard_shortcuts(self):
        """设置键盘快捷键"""
        self.root.bind('<Control-t>', lambda e: self.toggle_theme())
        self.root.bind('<Control-r>', lambda e: self.refresh_all())
        self.root.bind('<F5>', lambda e: self.refresh_all())
        self.root.bind('<Control-q>', lambda e: self.root.quit())

    def refresh_all(self):
        """刷新所有数据"""
        # 实现刷新逻辑
        pass

    def _update_interface_colors(self):
        """更新界面颜色"""
        # 更新主容器
        self.main_container.configure(bg=self.theme.BACKGROUND_COLOR)
        self.root.configure(bg=self.theme.BACKGROUND_COLOR)

    def create_premium_header(self):
        """创建高级标题区域"""
        # 主标题容器 - 使用卡片样式
        header_card = tk.Frame(self.main_container,
                              bg=self.theme.CARD_COLOR,
                              relief='flat',
                              bd=0)
        header_card.pack(fill=tk.X, pady=(0, self.theme.SPACING["xl"]))

        # 添加内边距
        header_content = tk.Frame(header_card, bg=self.theme.CARD_COLOR)
        header_content.pack(fill=tk.X, padx=self.theme.SPACING["2xl"], pady=self.theme.SPACING["xl"])

        # 左侧标题区域
        title_section = tk.Frame(header_content, bg=self.theme.CARD_COLOR)
        title_section.pack(side=tk.LEFT, fill=tk.Y)

        # 主标题 - 使用渐变效果
        main_title = tk.Label(title_section,
                             text=f"{self.theme.ICONS['rocket']} AUG 0.1 Premium",
                             font=self.theme.FONTS["display"],
                             bg=self.theme.CARD_COLOR,
                             fg=self.theme.PRIMARY_COLOR)
        main_title.pack(anchor=tk.W, pady=(0, self.theme.SPACING["sm"]))

        # 副标题
        subtitle = tk.Label(title_section,
                           text="高级编辑器遥测数据修改工具",
                           font=self.theme.FONTS["title_3"],
                           bg=self.theme.CARD_COLOR,
                           fg=self.theme.TEXT_SECONDARY)
        subtitle.pack(anchor=tk.W, pady=(0, self.theme.SPACING["xs"]))

        # 描述文字
        description = tk.Label(title_section,
                              text="专业级隐私保护 • 智能清理 • 现代界面",
                              font=self.theme.FONTS["body"],
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_TERTIARY)
        description.pack(anchor=tk.W)

        # 右侧信息区域
        info_section = tk.Frame(header_content, bg=self.theme.CARD_COLOR)
        info_section.pack(side=tk.RIGHT, fill=tk.Y)

        # 版本信息卡片
        version_card = tk.Frame(info_section,
                               bg=self.theme.PRIMARY_LIGHT,
                               relief='flat',
                               bd=0)
        version_card.pack(anchor=tk.E, pady=self.theme.SPACING["sm"])

        version_content = tk.Frame(version_card, bg=self.theme.PRIMARY_LIGHT)
        version_content.pack(padx=self.theme.SPACING["md"], pady=self.theme.SPACING["sm"])

        version_label = tk.Label(version_content,
                                text=f"{self.theme.ICONS['gem']} v0.1.0 Premium",
                                font=self.theme.FONTS["caption"],
                                bg=self.theme.PRIMARY_LIGHT,
                                fg=self.theme.PRIMARY_COLOR)
        version_label.pack()

        # 状态指示器
        status_card = tk.Frame(info_section,
                              bg=self.theme.SUCCESS_LIGHT,
                              relief='flat',
                              bd=0)
        status_card.pack(anchor=tk.E, pady=(self.theme.SPACING["sm"], 0))

        status_content = tk.Frame(status_card, bg=self.theme.SUCCESS_LIGHT)
        status_content.pack(padx=self.theme.SPACING["md"], pady=self.theme.SPACING["sm"])

        status_label = tk.Label(status_content,
                               text=f"{self.theme.ICONS['check']} 系统就绪",
                               font=self.theme.FONTS["caption"],
                               bg=self.theme.SUCCESS_LIGHT,
                               fg=self.theme.SUCCESS_COLOR)
        status_label.pack()

        # 添加分隔线
        separator = tk.Frame(self.main_container,
                           bg=self.theme.BORDER_PRIMARY,
                           height=1)
        separator.pack(fill=tk.X, pady=(0, self.theme.SPACING["lg"]))
        
    def create_premium_content_area(self):
        """创建高级内容区域"""
        # 创建高级Notebook控件
        self.notebook = ttk.Notebook(self.main_container, style='Premium.TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, self.theme.SPACING["xl"]))

        # 创建各个标签页
        self.create_premium_modify_tab()
        self.create_premium_database_tab()
        self.create_premium_settings_tab()
        self.create_premium_about_tab()

        # 绑定标签页切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self._on_tab_changed)

    def _on_tab_changed(self, event):
        """标签页切换事件处理"""
        selected_tab = self.notebook.select()
        tab_text = self.notebook.tab(selected_tab, "text")

        # 可以在这里添加标签页切换的动画效果
        # 或者更新状态栏信息
        self.update_status(f"当前页面: {tab_text}")

    def create_premium_modify_tab(self):
        """创建高级修改标签页"""
        # 创建滚动容器
        modify_frame = self.create_scrollable_frame()
        self.notebook.add(modify_frame, text=f"{self.theme.ICONS['edit']} 智能修改")

        # 获取内容区域
        content = modify_frame.winfo_children()[0].winfo_children()[0]

        # 创建高级卡片组件
        self.create_premium_mode_card(content)
        self.create_premium_fields_card(content)
        self.create_premium_actions_card(content)

    def create_premium_database_tab(self):
        """创建高级数据库标签页"""
        database_frame = self.create_scrollable_frame()
        self.notebook.add(database_frame, text=f"{self.theme.ICONS['database']} 数据库管理")

        content = database_frame.winfo_children()[0].winfo_children()[0]

        self.create_premium_scan_card(content)
        self.create_premium_clean_card(content)
        self.create_premium_backup_card(content)

    def create_premium_settings_tab(self):
        """创建高级设置标签页"""
        settings_frame = self.create_scrollable_frame()
        self.notebook.add(settings_frame, text=f"{self.theme.ICONS['settings']} 高级设置")

        content = settings_frame.winfo_children()[0].winfo_children()[0]

        self.create_premium_appearance_card(content)
        self.create_premium_behavior_card(content)
        self.create_premium_security_card(content)

    def create_premium_about_tab(self):
        """创建高级关于标签页"""
        about_frame = self.create_scrollable_frame()
        self.notebook.add(about_frame, text=f"{self.theme.ICONS['info']} 关于")

        content = about_frame.winfo_children()[0].winfo_children()[0]

        self.create_premium_info_card(content)
        self.create_premium_features_card(content)
        self.create_premium_support_card(content)

    def create_scrollable_frame(self):
        """创建可滚动框架"""
        # 主容器
        container = tk.Frame(self.notebook, bg=self.theme.BACKGROUND_COLOR)

        # 创建Canvas和Scrollbar
        canvas = tk.Canvas(container,
                          bg=self.theme.BACKGROUND_COLOR,
                          highlightthickness=0,
                          bd=0)

        scrollbar = ttk.Scrollbar(container,
                                 orient="vertical",
                                 command=canvas.yview,
                                 style='Premium.Vertical.TScrollbar')

        scrollable_frame = tk.Frame(canvas, bg=self.theme.BACKGROUND_COLOR)

        # 配置滚动
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 布局
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 鼠标滚轮绑定
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        canvas.bind("<MouseWheel>", _on_mousewheel)

        return container

    def create_premium_status_bar(self):
        """创建高级状态栏"""
        # 状态栏容器
        status_container = tk.Frame(self.main_container, bg=self.theme.BACKGROUND_COLOR)
        status_container.pack(fill=tk.X, pady=(self.theme.SPACING["md"], 0))

        # 主状态栏
        status_frame = tk.Frame(status_container,
                               bg=self.theme.CARD_COLOR,
                               height=50,
                               relief='flat',
                               bd=0)
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)

        # 左侧状态区域
        left_status = tk.Frame(status_frame, bg=self.theme.CARD_COLOR)
        left_status.pack(side=tk.LEFT, fill=tk.Y, padx=self.theme.SPACING["lg"])

        # 状态指示器
        self.status_var = tk.StringVar()
        self.status_var.set(f"{self.theme.ICONS['check']} 系统就绪")

        status_label = tk.Label(left_status,
                               textvariable=self.status_var,
                               font=self.theme.FONTS["body"],
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_PRIMARY)
        status_label.pack(side=tk.LEFT, pady=self.theme.SPACING["md"])

        # 进度条（隐藏状态）
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(left_status,
                                          variable=self.progress_var,
                                          style='Premium.Horizontal.TProgressbar',
                                          length=200)
        # 初始隐藏

        # 中间信息区域
        center_status = tk.Frame(status_frame, bg=self.theme.CARD_COLOR)
        center_status.pack(side=tk.LEFT, expand=True, fill=tk.Y)

        # 操作提示
        self.tip_var = tk.StringVar()
        self.tip_var.set("提示: 使用 Ctrl+T 切换主题")

        tip_label = tk.Label(center_status,
                            textvariable=self.tip_var,
                            font=self.theme.FONTS["caption"],
                            bg=self.theme.CARD_COLOR,
                            fg=self.theme.TEXT_TERTIARY)
        tip_label.pack(pady=self.theme.SPACING["md"])

        # 右侧信息区域
        right_status = tk.Frame(status_frame, bg=self.theme.CARD_COLOR)
        right_status.pack(side=tk.RIGHT, fill=tk.Y, padx=self.theme.SPACING["lg"])

        # 主题指示器
        self.theme_indicator = tk.Label(right_status,
                                       text=f"{self.theme.ICONS['magic']} 现代蓝色",
                                       font=self.theme.FONTS["caption"],
                                       bg=self.theme.CARD_COLOR,
                                       fg=self.theme.PRIMARY_COLOR)
        self.theme_indicator.pack(side=tk.RIGHT, padx=self.theme.SPACING["sm"], pady=self.theme.SPACING["md"])

        # 时间显示
        self.time_var = tk.StringVar()
        time_label = tk.Label(right_status,
                             textvariable=self.time_var,
                             font=self.theme.FONTS["caption"],
                             bg=self.theme.CARD_COLOR,
                             fg=self.theme.TEXT_SECONDARY)
        time_label.pack(side=tk.RIGHT, padx=self.theme.SPACING["sm"], pady=self.theme.SPACING["md"])

        # 启动时间更新
        self.update_time()
        self.update_tips()

    def update_status(self, message, status_type="info"):
        """更新状态信息"""
        icons = {
            "info": self.theme.ICONS['info'],
            "success": self.theme.ICONS['check'],
            "warning": self.theme.ICONS['warning'],
            "error": self.theme.ICONS['error'],
            "loading": self.theme.ICONS['loading']
        }

        icon = icons.get(status_type, self.theme.ICONS['info'])
        self.status_var.set(f"{icon} {message}")

    def show_progress(self, show=True):
        """显示/隐藏进度条"""
        if show:
            self.progress_bar.pack(side=tk.LEFT, padx=self.theme.SPACING["md"], pady=self.theme.SPACING["md"])
        else:
            self.progress_bar.pack_forget()

    def update_progress(self, value):
        """更新进度条"""
        self.progress_var.set(value)

    def update_tips(self):
        """更新提示信息"""
        tips = [
            "提示: 使用 Ctrl+T 切换主题",
            "提示: 使用 F5 刷新数据",
            "提示: 修改前会自动创建备份",
            "提示: 支持批量处理多个编辑器",
            "提示: 使用 Ctrl+Q 快速退出"
        ]

        import random
        tip = random.choice(tips)
        self.tip_var.set(tip)

        # 30秒后更新下一个提示
        self.root.after(30000, self.update_tips)

    # 高级卡片组件占位符方法
    def create_premium_mode_card(self, parent):
        """创建高级模式选择卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['settings']} 智能操作模式")

        # 模式选择内容
        content = tk.Frame(card, bg=self.theme.CARD_COLOR)
        content.pack(fill=tk.X, padx=self.theme.SPACING["xl"], pady=self.theme.SPACING["lg"])

        modes = [
            ("ai_smart", f"{self.theme.ICONS['brain']} AI智能模式", "AI自动分析并选择最佳清理策略"),
            ("professional", f"{self.theme.ICONS['crown']} 专业模式", "完全自定义的高级清理选项"),
            ("quick", f"{self.theme.ICONS['lightning']} 闪电模式", "一键快速清理，适合日常使用"),
            ("safe", f"{self.theme.ICONS['shield']} 安全模式", "保守清理，确保系统稳定性")
        ]

        self.mode_var = tk.StringVar(value="ai_smart")

        for mode_value, mode_title, mode_desc in modes:
            mode_frame = tk.Frame(content, bg=self.theme.CARD_COLOR)
            mode_frame.pack(fill=tk.X, pady=self.theme.SPACING["sm"])

            rb = tk.Radiobutton(mode_frame,
                               text=mode_title,
                               value=mode_value,
                               variable=self.mode_var,
                               font=self.theme.FONTS["body"],
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_PRIMARY,
                               selectcolor=self.theme.PRIMARY_LIGHT,
                               activebackground=self.theme.CARD_COLOR,
                               relief='flat',
                               bd=0)
            rb.pack(anchor=tk.W, padx=self.theme.SPACING["md"])

            desc_label = tk.Label(mode_frame,
                                 text=mode_desc,
                                 font=self.theme.FONTS["caption"],
                                 bg=self.theme.CARD_COLOR,
                                 fg=self.theme.TEXT_SECONDARY)
            desc_label.pack(anchor=tk.W, padx=self.theme.SPACING["3xl"])

    def create_premium_fields_card(self, parent):
        """创建高级字段选择卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['target']} 精准字段控制")

        content = tk.Frame(card, bg=self.theme.CARD_COLOR)
        content.pack(fill=tk.X, padx=self.theme.SPACING["xl"], pady=self.theme.SPACING["lg"])

        # 字段分组
        groups = [
            ("核心隐私", ["machineId", "deviceId", "sessionId"], self.theme.ERROR_COLOR),
            ("遥测数据", ["telemetryLevel", "optIn", "crashReports"], self.theme.WARNING_COLOR),
            ("用户标识", ["userId", "accountId", "profileId"], self.theme.INFO_COLOR)
        ]

        self.field_vars = {}

        for group_name, fields, color in groups:
            group_frame = tk.LabelFrame(content,
                                       text=f"🎯 {group_name}",
                                       font=self.theme.FONTS["headline"],
                                       bg=self.theme.CARD_COLOR,
                                       fg=color,
                                       relief='flat',
                                       bd=1)
            group_frame.pack(fill=tk.X, pady=self.theme.SPACING["md"])

            for field in fields:
                var = tk.BooleanVar(value=True)
                self.field_vars[field] = var

                cb = tk.Checkbutton(group_frame,
                                   text=field,
                                   variable=var,
                                   font=self.theme.FONTS["body"],
                                   bg=self.theme.CARD_COLOR,
                                   fg=self.theme.TEXT_PRIMARY,
                                   selectcolor=self.theme.PRIMARY_LIGHT,
                                   activebackground=self.theme.CARD_COLOR,
                                   relief='flat',
                                   bd=0)
                cb.pack(anchor=tk.W, padx=self.theme.SPACING["lg"], pady=self.theme.SPACING["xs"])

    def create_premium_actions_card(self, parent):
        """创建高级操作卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['rocket']} 智能操作中心")

        content = tk.Frame(card, bg=self.theme.CARD_COLOR)
        content.pack(fill=tk.X, padx=self.theme.SPACING["xl"], pady=self.theme.SPACING["lg"])

        # 主要操作按钮
        main_actions = tk.Frame(content, bg=self.theme.CARD_COLOR)
        main_actions.pack(fill=tk.X, pady=self.theme.SPACING["md"])

        start_btn = tk.Button(main_actions,
                             text=f"{self.theme.ICONS['play']} 开始智能清理",
                             font=self.theme.FONTS["headline"],
                             bg=self.theme.PRIMARY_COLOR,
                             fg='white',
                             relief='flat',
                             padx=self.theme.SPACING["2xl"],
                             pady=self.theme.SPACING["md"],
                             command=self.start_smart_cleaning)
        start_btn.pack(side=tk.LEFT, padx=(0, self.theme.SPACING["md"]))

        analyze_btn = tk.Button(main_actions,
                               text=f"{self.theme.ICONS['search']} 深度分析",
                               font=self.theme.FONTS["body"],
                               bg=self.theme.SUCCESS_COLOR,
                               fg='white',
                               relief='flat',
                               padx=self.theme.SPACING["lg"],
                               pady=self.theme.SPACING["sm"],
                               command=self.deep_analyze)
        analyze_btn.pack(side=tk.LEFT)

    def create_card(self, parent, title):
        """创建标准卡片组件"""
        # 卡片容器
        card_container = tk.Frame(parent, bg=self.theme.BACKGROUND_COLOR)
        card_container.pack(fill=tk.X, pady=self.theme.SPACING["lg"], padx=self.theme.SPACING["md"])

        # 卡片主体
        card = tk.Frame(card_container,
                       bg=self.theme.CARD_COLOR,
                       relief='flat',
                       bd=0)
        card.pack(fill=tk.X)

        # 卡片标题
        title_frame = tk.Frame(card, bg=self.theme.CARD_COLOR)
        title_frame.pack(fill=tk.X, padx=self.theme.SPACING["xl"], pady=(self.theme.SPACING["lg"], 0))

        title_label = tk.Label(title_frame,
                              text=title,
                              font=self.theme.FONTS["title_3"],
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_PRIMARY)
        title_label.pack(anchor=tk.W)

        return card

    # 占位符方法
    def create_premium_scan_card(self, parent):
        """创建高级扫描卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['search']} 智能扫描引擎")
        # 实现扫描功能
        pass

    def create_premium_clean_card(self, parent):
        """创建高级清理卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['clean']} 深度清理系统")
        # 实现清理功能
        pass

    def create_premium_backup_card(self, parent):
        """创建高级备份卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['shield']} 智能备份管理")
        # 实现备份功能
        pass

    def create_premium_appearance_card(self, parent):
        """创建高级外观卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['magic']} 外观个性化")
        # 实现外观设置
        pass

    def create_premium_behavior_card(self, parent):
        """创建高级行为卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['settings']} 行为设置")
        # 实现行为设置
        pass

    def create_premium_security_card(self, parent):
        """创建高级安全卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['lock']} 安全与隐私")
        # 实现安全设置
        pass

    def create_premium_info_card(self, parent):
        """创建高级信息卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['info']} 软件信息")
        # 实现软件信息
        pass

    def create_premium_features_card(self, parent):
        """创建高级功能卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['star']} 功能特性")
        # 实现功能展示
        pass

    def create_premium_support_card(self, parent):
        """创建高级支持卡片"""
        card = self.create_card(parent, f"{self.theme.ICONS['heart']} 支持与帮助")
        # 实现支持信息
        pass

    # 功能方法
    def start_smart_cleaning(self):
        """开始智能清理"""
        self.update_status("正在启动智能清理...", "loading")
        self.show_progress(True)
        # 实现智能清理逻辑

    def deep_analyze(self):
        """深度分析"""
        self.update_status("正在进行深度分析...", "loading")
        # 实现深度分析逻辑

    def update_time(self):
        """更新时间显示"""
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        self.root.after(1000, self.update_time)

    def initialize_features(self):
        """初始化功能"""
        # 初始化备份列表
        self.refresh_backups()

        # 检查PowerShell
        self.check_powershell()

        # 自动检测编辑器
        self.root.after(1000, self.detect_editors_silent)
        
    def create_main_tab(self):
        """创建现代化主要功能标签页"""
        # 创建主标签页容器
        main_tab = tk.Frame(self.notebook, bg=self.theme.BACKGROUND_COLOR)
        self.notebook.add(main_tab, text=f"{self.theme.ICONS['editor']} 主要功能")

        # 创建滚动容器
        canvas = tk.Canvas(main_tab, bg=self.theme.BACKGROUND_COLOR, highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.theme.BACKGROUND_COLOR)

        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")

        # 编辑器选择卡片
        self.create_editor_card(scrollable_frame)

        # 操作模式卡片
        self.create_mode_card(scrollable_frame)

        # 操作按钮卡片
        self.create_action_card(scrollable_frame)

    def create_editor_card(self, parent):
        """创建编辑器选择卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 20), padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=50)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['editor']} 编辑器选择",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=10)

        # 检测按钮
        detect_btn = tk.Button(title_frame,
                              text=f"{self.theme.ICONS['search']} 自动检测",
                              font=self.theme.BODY_FONT,
                              bg=self.theme.SECONDARY_COLOR,
                              fg='white',
                              relief='flat',
                              padx=15,
                              pady=5,
                              command=self.detect_editors)
        detect_btn.pack(side=tk.RIGHT, pady=10)

        # 编辑器列表
        editors_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        editors_content.pack(fill=tk.X, padx=20, pady=(0, 20))

        self.editor_vars = {}
        editors = [
            ("VSCode", "📝 Visual Studio Code"),
            ("VSCode Insiders", "🔬 VS Code Insiders"),
            ("Cursor Editor", "🎯 Cursor Editor"),
            ("VSCodium", "🆓 VSCodium"),
            ("Code - OSS", "🌐 Code - OSS")
        ]

        for i, (editor_key, editor_display) in enumerate(editors):
            var = tk.BooleanVar(value=True if i == 0 else False)
            self.editor_vars[editor_key] = var

            editor_frame = tk.Frame(editors_content, bg=self.theme.CARD_COLOR)
            editor_frame.pack(fill=tk.X, pady=2)

            cb = tk.Checkbutton(editor_frame,
                               text=editor_display,
                               variable=var,
                               font=self.theme.BODY_FONT,
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_COLOR,
                               selectcolor=self.theme.CARD_COLOR,
                               activebackground=self.theme.CARD_COLOR,
                               relief='flat',
                               bd=0)
            cb.pack(anchor=tk.W, padx=10, pady=5)

    def create_mode_card(self, parent):
        """创建操作模式卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 20), padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=50)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['settings']} 操作模式",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=10)

        # 模式选择
        modes_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        modes_content.pack(fill=tk.X, padx=20, pady=(0, 20))

        self.mode_var = tk.StringVar(value="standard")
        modes = [
            ("standard", "⚡ 标准模式", "修改基本遥测字段，适合日常使用"),
            ("enhanced", "🚀 增强模式", "修改所有遥测字段，提供最大隐私保护"),
            ("quick", "⚡ 快速模式", "仅修改核心字段，快速完成操作"),
            ("custom", "🎯 自定义模式", "自由选择要修改的字段")
        ]

        for mode_value, mode_title, mode_desc in modes:
            mode_frame = tk.Frame(modes_content, bg=self.theme.CARD_COLOR)
            mode_frame.pack(fill=tk.X, pady=3)

            rb = tk.Radiobutton(mode_frame,
                               text=mode_title,
                               value=mode_value,
                               variable=self.mode_var,
                               font=self.theme.BODY_FONT,
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_COLOR,
                               selectcolor=self.theme.CARD_COLOR,
                               activebackground=self.theme.CARD_COLOR,
                               relief='flat',
                               bd=0)
            rb.pack(anchor=tk.W, padx=10)

            desc_label = tk.Label(mode_frame,
                                 text=mode_desc,
                                 font=("Microsoft YaHei UI", 9),
                                 bg=self.theme.CARD_COLOR,
                                 fg=self.theme.SECONDARY_COLOR)
            desc_label.pack(anchor=tk.W, padx=30)

    def create_action_card(self, parent):
        """创建操作按钮卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 20), padx=10)

        # 按钮容器
        buttons_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        buttons_content.pack(fill=tk.X, padx=20, pady=20)

        # 主要操作按钮
        start_btn = tk.Button(buttons_content,
                             text=f"{self.theme.ICONS['play']} 开始修改",
                             font=self.theme.BUTTON_FONT,
                             bg=self.theme.PRIMARY_COLOR,
                             fg='white',
                             relief='flat',
                             padx=30,
                             pady=12,
                             command=self.start_modification)
        start_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 辅助按钮
        verify_btn = tk.Button(buttons_content,
                              text=f"{self.theme.ICONS['shield']} 验证状态",
                              font=self.theme.BODY_FONT,
                              bg=self.theme.SUCCESS_COLOR,
                              fg='white',
                              relief='flat',
                              padx=20,
                              pady=10,
                              command=self.verify_status)
        verify_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 帮助按钮
        help_btn = tk.Button(buttons_content,
                            text=f"{self.theme.ICONS['info']} 使用帮助",
                            font=self.theme.BODY_FONT,
                            bg=self.theme.SECONDARY_COLOR,
                            fg='white',
                            relief='flat',
                            padx=20,
                            pady=10,
                            command=self.show_help)
        help_btn.pack(side=tk.LEFT)

    def verify_status(self):
        """验证当前状态"""
        messagebox.showinfo("状态验证", "功能开发中，敬请期待！")

    def show_help(self):
        """显示使用帮助"""
        help_text = """
🚀 AUG 0.1 使用帮助

📝 基本步骤：
1. 选择要修改的编辑器
2. 选择操作模式
3. 点击"开始修改"

⚙️ 操作模式说明：
• 标准模式：修改基本遥测字段
• 增强模式：修改所有遥测字段
• 快速模式：仅修改核心字段
• 自定义模式：自由选择字段

💡 注意事项：
• 修改前请关闭目标编辑器
• 建议先创建备份
• 修改后重启编辑器生效

🛡️ 安全提示：
• 所有操作都会自动创建备份
• 可随时恢复到修改前状态
• 支持批量管理备份文件
        """
        messagebox.showinfo("使用帮助", help_text)

    def detect_editors_silent(self):
        """静默检测编辑器（不显示消息框）"""
        try:
            editors = {
                "VSCode": "%APPDATA%\\Code\\User\\globalStorage\\storage.json",
                "VSCode Insiders": "%APPDATA%\\Code - Insiders\\User\\globalStorage\\storage.json",
                "Cursor Editor": "%APPDATA%\\Cursor\\User\\globalStorage\\storage.json",
                "VSCodium": "%APPDATA%\\VSCodium\\User\\globalStorage\\storage.json",
                "Code - OSS": "%APPDATA%\\Code - OSS\\User\\globalStorage\\storage.json"
            }

            found_count = 0
            for editor, path in editors.items():
                expanded_path = os.path.expandvars(path)
                if os.path.exists(expanded_path):
                    found_count += 1
                    if hasattr(self, 'editor_vars') and editor in self.editor_vars:
                        self.editor_vars[editor].set(True)
                else:
                    if hasattr(self, 'editor_vars') and editor in self.editor_vars:
                        self.editor_vars[editor].set(False)

            self.status_var.set(f"🔍 检测到 {found_count} 个编辑器")
        except Exception:
            pass
        
    def create_advanced_tab(self):
        """创建现代化高级选项标签页"""
        # 创建高级选项标签页容器
        advanced_tab = tk.Frame(self.notebook, bg=self.theme.BACKGROUND_COLOR)
        self.notebook.add(advanced_tab, text=f"{self.theme.ICONS['settings']} 高级选项")

        # 创建滚动容器
        canvas = tk.Canvas(advanced_tab, bg=self.theme.BACKGROUND_COLOR, highlightthickness=0)
        scrollbar = ttk.Scrollbar(advanced_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.theme.BACKGROUND_COLOR)

        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")

        # 创建字段选择卡片
        self.create_fields_card(scrollable_frame)

        # 创建预设配置卡片
        self.create_presets_card(scrollable_frame)

    def create_fields_card(self, parent):
        """创建字段选择卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 20), padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=60)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['settings']} 遥测字段选择",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=15)

        # 操作按钮
        btn_frame = tk.Frame(title_frame, bg=self.theme.CARD_COLOR)
        btn_frame.pack(side=tk.RIGHT, pady=15)

        select_all_btn = tk.Button(btn_frame,
                                  text="全选",
                                  font=self.theme.BODY_FONT,
                                  bg=self.theme.SUCCESS_COLOR,
                                  fg='white',
                                  relief='flat',
                                  padx=12,
                                  pady=5,
                                  command=self.select_all_fields)
        select_all_btn.pack(side=tk.LEFT, padx=(0, 5))

        clear_all_btn = tk.Button(btn_frame,
                                 text="清空",
                                 font=self.theme.BODY_FONT,
                                 bg=self.theme.WARNING_COLOR,
                                 fg='white',
                                 relief='flat',
                                 padx=12,
                                 pady=5,
                                 command=self.clear_all_fields)
        clear_all_btn.pack(side=tk.LEFT)

        # 字段列表
        fields_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        fields_content.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 创建字段分组
        self.field_vars = {}

        # 核心字段组
        core_frame = tk.LabelFrame(fields_content,
                                  text="🎯 核心字段 (推荐修改)",
                                  font=self.theme.BODY_FONT,
                                  bg=self.theme.CARD_COLOR,
                                  fg=self.theme.TEXT_COLOR,
                                  relief='flat',
                                  bd=1)
        core_frame.pack(fill=tk.X, pady=(0, 15))

        core_fields = [
            ("machineId", "🖥️ 机器唯一标识符", "设备的唯一识别码"),
            ("devDeviceId", "📱 设备标识符", "开发设备的标识符"),
            ("sessionId", "🔗 会话标识符", "当前会话的唯一ID")
        ]

        for field_key, field_display, field_desc in core_fields:
            var = tk.BooleanVar(value=True)
            self.field_vars[field_key] = var

            field_frame = tk.Frame(core_frame, bg=self.theme.CARD_COLOR)
            field_frame.pack(fill=tk.X, pady=2, padx=10)

            cb = tk.Checkbutton(field_frame,
                               text=field_display,
                               variable=var,
                               font=self.theme.BODY_FONT,
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_COLOR,
                               selectcolor=self.theme.CARD_COLOR,
                               activebackground=self.theme.CARD_COLOR,
                               relief='flat',
                               bd=0)
            cb.pack(anchor=tk.W, pady=2)

            desc_label = tk.Label(field_frame,
                                 text=field_desc,
                                 font=("Microsoft YaHei UI", 9),
                                 bg=self.theme.CARD_COLOR,
                                 fg=self.theme.SECONDARY_COLOR)
            desc_label.pack(anchor=tk.W, padx=20)

        # 扩展字段组
        extended_frame = tk.LabelFrame(fields_content,
                                      text="🔧 扩展字段 (可选修改)",
                                      font=self.theme.BODY_FONT,
                                      bg=self.theme.CARD_COLOR,
                                      fg=self.theme.TEXT_COLOR,
                                      relief='flat',
                                      bd=1)
        extended_frame.pack(fill=tk.X, pady=(0, 15))

        extended_fields = [
            ("sqmId", "📊 软件质量指标ID", "用于软件质量监控"),
            ("firstSessionDate", "📅 首次会话日期", "首次使用的时间记录"),
            ("lastSessionDate", "🕒 最后会话日期", "最近使用的时间记录"),
            ("isNewAppInstall", "🆕 新安装标记", "标识是否为新安装"),
            ("optIn", "✅ 遥测选择状态", "用户遥测数据收集同意状态")
        ]

        for field_key, field_display, field_desc in extended_fields:
            var = tk.BooleanVar(value=False)
            self.field_vars[field_key] = var

            field_frame = tk.Frame(extended_frame, bg=self.theme.CARD_COLOR)
            field_frame.pack(fill=tk.X, pady=2, padx=10)

            cb = tk.Checkbutton(field_frame,
                               text=field_display,
                               variable=var,
                               font=self.theme.BODY_FONT,
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_COLOR,
                               selectcolor=self.theme.CARD_COLOR,
                               activebackground=self.theme.CARD_COLOR,
                               relief='flat',
                               bd=0)
            cb.pack(anchor=tk.W, pady=2)

            desc_label = tk.Label(field_frame,
                                 text=field_desc,
                                 font=("Microsoft YaHei UI", 9),
                                 bg=self.theme.CARD_COLOR,
                                 fg=self.theme.SECONDARY_COLOR)
            desc_label.pack(anchor=tk.W, padx=20)

        # 用户字段组
        user_frame = tk.LabelFrame(fields_content,
                                  text="👤 用户字段 (高级选项)",
                                  font=self.theme.BODY_FONT,
                                  bg=self.theme.CARD_COLOR,
                                  fg=self.theme.TEXT_COLOR,
                                  relief='flat',
                                  bd=1)
        user_frame.pack(fill=tk.X)

        user_fields = [
            ("userId", "👤 用户标识符", "用户账户的唯一标识"),
            ("accountId", "🏢 账户标识符", "关联账户的标识符"),
            ("profileId", "📋 用户配置文件ID", "用户配置文件标识"),
            ("workspaceId", "📁 工作区标识符", "工作区的唯一标识")
        ]

        for field_key, field_display, field_desc in user_fields:
            var = tk.BooleanVar(value=False)
            self.field_vars[field_key] = var

            field_frame = tk.Frame(user_frame, bg=self.theme.CARD_COLOR)
            field_frame.pack(fill=tk.X, pady=2, padx=10)

            cb = tk.Checkbutton(field_frame,
                               text=field_display,
                               variable=var,
                               font=self.theme.BODY_FONT,
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_COLOR,
                               selectcolor=self.theme.CARD_COLOR,
                               activebackground=self.theme.CARD_COLOR,
                               relief='flat',
                               bd=0)
            cb.pack(anchor=tk.W, pady=2)

            desc_label = tk.Label(field_frame,
                                 text=field_desc,
                                 font=("Microsoft YaHei UI", 9),
                                 bg=self.theme.CARD_COLOR,
                                 fg=self.theme.SECONDARY_COLOR)
            desc_label.pack(anchor=tk.W, padx=20)

    def create_presets_card(self, parent):
        """创建预设配置卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 20), padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=50)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['settings']} 快速预设",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=10)

        # 预设按钮
        presets_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        presets_content.pack(fill=tk.X, padx=20, pady=(0, 20))

        preset_buttons = [
            ("基础隐私", self.theme.SUCCESS_COLOR, self.apply_basic_preset),
            ("标准隐私", self.theme.PRIMARY_COLOR, self.apply_standard_preset),
            ("最大隐私", self.theme.WARNING_COLOR, self.apply_maximum_preset)
        ]

        for preset_name, color, command in preset_buttons:
            btn = tk.Button(presets_content,
                           text=preset_name,
                           font=self.theme.BODY_FONT,
                           bg=color,
                           fg='white',
                           relief='flat',
                           padx=20,
                           pady=8,
                           command=command)
            btn.pack(side=tk.LEFT, padx=(0, 10))

    def select_all_fields(self):
        """选择所有字段"""
        for var in self.field_vars.values():
            var.set(True)
        self.status_var.set("✅ 已选择所有字段")

    def clear_all_fields(self):
        """清空所有字段选择"""
        for var in self.field_vars.values():
            var.set(False)
        self.status_var.set("🔄 已清空字段选择")

    def apply_basic_preset(self):
        """应用基础隐私预设"""
        self.clear_all_fields()
        basic_fields = ["machineId", "devDeviceId"]
        for field in basic_fields:
            if field in self.field_vars:
                self.field_vars[field].set(True)
        self.status_var.set("✅ 已应用基础隐私预设")

    def apply_standard_preset(self):
        """应用标准隐私预设"""
        self.clear_all_fields()
        standard_fields = ["machineId", "devDeviceId", "sessionId", "sqmId"]
        for field in standard_fields:
            if field in self.field_vars:
                self.field_vars[field].set(True)
        self.status_var.set("✅ 已应用标准隐私预设")

    def apply_maximum_preset(self):
        """应用最大隐私预设"""
        self.select_all_fields()
        self.status_var.set("✅ 已应用最大隐私预设")

    def create_database_tab(self):
        """创建现代化数据库清理标签页"""
        # 创建数据库清理标签页容器
        database_tab = tk.Frame(self.notebook, bg=self.theme.BACKGROUND_COLOR)
        self.notebook.add(database_tab, text=f"{self.theme.ICONS['database']} 数据库清理")

        # 创建滚动容器
        canvas = tk.Canvas(database_tab, bg=self.theme.BACKGROUND_COLOR, highlightthickness=0)
        scrollbar = ttk.Scrollbar(database_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.theme.BACKGROUND_COLOR)

        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")

        # 创建数据库清理卡片
        self.create_database_scan_card(scrollable_frame)
        self.create_database_clean_card(scrollable_frame)
        self.create_database_settings_card(scrollable_frame)

    def create_database_scan_card(self, parent):
        """创建数据库扫描卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 20), padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=60)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['search']} 数据库扫描",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=15)

        # 扫描按钮
        scan_btn = tk.Button(title_frame,
                            text=f"{self.theme.ICONS['search']} 扫描数据库",
                            font=self.theme.BODY_FONT,
                            bg=self.theme.PRIMARY_COLOR,
                            fg='white',
                            relief='flat',
                            padx=15,
                            pady=8,
                            command=self.scan_databases)
        scan_btn.pack(side=tk.RIGHT, pady=15)

        # 扫描结果容器
        result_container = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        result_container.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 创建Treeview来显示数据库信息
        columns = ('数据库路径', '编辑器', '大小', '记录数', '状态')
        self.database_tree = ttk.Treeview(result_container, columns=columns, show='headings', height=8)

        # 配置列
        self.database_tree.heading('数据库路径', text='🗄️ 数据库路径')
        self.database_tree.heading('编辑器', text='📝 编辑器')
        self.database_tree.heading('大小', text='📏 大小')
        self.database_tree.heading('记录数', text='📊 记录数')
        self.database_tree.heading('状态', text='🔍 状态')

        self.database_tree.column('数据库路径', width=300)
        self.database_tree.column('编辑器', width=120)
        self.database_tree.column('大小', width=80)
        self.database_tree.column('记录数', width=80)
        self.database_tree.column('状态', width=100)

        # 滚动条
        db_scrollbar = ttk.Scrollbar(result_container, orient="vertical", command=self.database_tree.yview)
        self.database_tree.configure(yscrollcommand=db_scrollbar.set)

        self.database_tree.pack(side="left", fill="both", expand=True)
        db_scrollbar.pack(side="right", fill="y")

    def create_database_clean_card(self, parent):
        """创建数据库清理卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 20), padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=50)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['clean']} 清理选项",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=10)

        # 清理选项
        clean_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        clean_content.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 初始化清理选项变量
        self.clean_telemetry_var = tk.BooleanVar(value=True)
        self.clean_augment_var = tk.BooleanVar(value=True)
        self.clean_workspace_var = tk.BooleanVar(value=False)
        self.clean_cache_var = tk.BooleanVar(value=False)

        clean_options = [
            (self.clean_telemetry_var, "🎯 清理遥测记录", "删除所有遥测相关的数据库记录"),
            (self.clean_augment_var, "🔍 清理Augment记录", "删除包含'augment'关键字的记录"),
            (self.clean_workspace_var, "📁 清理工作区数据", "清理工作区相关的存储数据"),
            (self.clean_cache_var, "🗂️ 清理缓存数据", "清理临时缓存和历史记录")
        ]

        for var, title, description in clean_options:
            option_frame = tk.Frame(clean_content, bg=self.theme.CARD_COLOR)
            option_frame.pack(fill=tk.X, pady=5)

            cb = tk.Checkbutton(option_frame,
                               text=title,
                               variable=var,
                               font=self.theme.BODY_FONT,
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_COLOR,
                               selectcolor=self.theme.CARD_COLOR,
                               activebackground=self.theme.CARD_COLOR,
                               relief='flat',
                               bd=0)
            cb.pack(anchor=tk.W, padx=10)

            desc_label = tk.Label(option_frame,
                                 text=description,
                                 font=("Microsoft YaHei UI", 9),
                                 bg=self.theme.CARD_COLOR,
                                 fg=self.theme.SECONDARY_COLOR)
            desc_label.pack(anchor=tk.W, padx=30)

        # 操作按钮
        button_frame = tk.Frame(clean_content, bg=self.theme.CARD_COLOR)
        button_frame.pack(fill=tk.X, pady=(15, 0))

        # 智能清理按钮
        smart_clean_btn = tk.Button(button_frame,
                                   text=f"🧠 智能清理",
                                   font=self.theme.BUTTON_FONT,
                                   bg=self.theme.SUCCESS_COLOR,
                                   fg='white',
                                   relief='flat',
                                   padx=25,
                                   pady=12,
                                   command=self.smart_database_cleaning)
        smart_clean_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 开始清理按钮
        clean_btn = tk.Button(button_frame,
                             text=f"{self.theme.ICONS['clean']} 标准清理",
                             font=self.theme.BUTTON_FONT,
                             bg=self.theme.WARNING_COLOR,
                             fg='white',
                             relief='flat',
                             padx=25,
                             pady=12,
                             command=self.start_database_cleaning)
        clean_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 预览清理按钮
        preview_btn = tk.Button(button_frame,
                               text=f"{self.theme.ICONS['info']} 预览清理",
                               font=self.theme.BODY_FONT,
                               bg=self.theme.PRIMARY_COLOR,
                               fg='white',
                               relief='flat',
                               padx=20,
                               pady=10,
                               command=self.preview_database_cleaning)
        preview_btn.pack(side=tk.LEFT)

    def create_database_settings_card(self, parent):
        """创建数据库设置卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=50)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['settings']} 数据库设置",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=10)

        # 设置选项
        settings_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        settings_content.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 初始化设置变量
        self.db_backup_var = tk.BooleanVar(value=True)
        self.db_verify_var = tk.BooleanVar(value=True)
        self.db_log_var = tk.BooleanVar(value=True)

        db_settings = [
            (self.db_backup_var, "💾 自动备份数据库", "清理前自动创建数据库备份"),
            (self.db_verify_var, "✅ 验证数据库完整性", "清理后验证数据库完整性"),
            (self.db_log_var, "📋 记录清理日志", "详细记录清理操作和结果")
        ]

        for var, title, description in db_settings:
            setting_frame = tk.Frame(settings_content, bg=self.theme.CARD_COLOR)
            setting_frame.pack(fill=tk.X, pady=5)

            cb = tk.Checkbutton(setting_frame,
                               text=title,
                               variable=var,
                               font=self.theme.BODY_FONT,
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_COLOR,
                               selectcolor=self.theme.CARD_COLOR,
                               activebackground=self.theme.CARD_COLOR,
                               relief='flat',
                               bd=0)
            cb.pack(anchor=tk.W, padx=10)

            desc_label = tk.Label(setting_frame,
                                 text=description,
                                 font=("Microsoft YaHei UI", 9),
                                 bg=self.theme.CARD_COLOR,
                                 fg=self.theme.SECONDARY_COLOR)
            desc_label.pack(anchor=tk.W, padx=30)

    def create_backup_tab(self):
        """创建现代化备份管理标签页"""
        # 创建备份管理标签页容器
        backup_tab = tk.Frame(self.notebook, bg=self.theme.BACKGROUND_COLOR)
        self.notebook.add(backup_tab, text=f"{self.theme.ICONS['backup']} 备份管理")

        # 主容器
        main_container = tk.Frame(backup_tab, bg=self.theme.BACKGROUND_COLOR)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 创建备份列表卡片
        self.create_backup_list_card(main_container)

        # 创建备份操作卡片
        self.create_backup_actions_card(main_container)

    def create_backup_list_card(self, parent):
        """创建备份列表卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=60)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['backup']} 备份文件列表",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=15)

        # 刷新按钮
        refresh_btn = tk.Button(title_frame,
                               text=f"{self.theme.ICONS['refresh']} 刷新",
                               font=self.theme.BODY_FONT,
                               bg=self.theme.PRIMARY_COLOR,
                               fg='white',
                               relief='flat',
                               padx=15,
                               pady=8,
                               command=self.refresh_backups)
        refresh_btn.pack(side=tk.RIGHT, pady=15)

        # 备份列表容器
        list_container = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        list_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # 创建Treeview来显示备份信息
        columns = ('文件名', '编辑器', '创建时间', '大小')
        self.backup_tree = ttk.Treeview(list_container, columns=columns, show='headings', height=12)

        # 配置列
        self.backup_tree.heading('文件名', text='📄 文件名')
        self.backup_tree.heading('编辑器', text='📝 编辑器')
        self.backup_tree.heading('创建时间', text='🕒 创建时间')
        self.backup_tree.heading('大小', text='📏 大小')

        self.backup_tree.column('文件名', width=300)
        self.backup_tree.column('编辑器', width=120)
        self.backup_tree.column('创建时间', width=150)
        self.backup_tree.column('大小', width=80)

        # 滚动条
        tree_scrollbar = ttk.Scrollbar(list_container, orient="vertical", command=self.backup_tree.yview)
        self.backup_tree.configure(yscrollcommand=tree_scrollbar.set)

        self.backup_tree.pack(side="left", fill="both", expand=True)
        tree_scrollbar.pack(side="right", fill="y")

        # 绑定双击事件
        self.backup_tree.bind('<Double-1>', self.on_backup_double_click)

    def create_backup_actions_card(self, parent):
        """创建备份操作卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=50)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['settings']} 备份操作",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=10)

        # 操作按钮容器
        actions_container = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        actions_container.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 左侧按钮组
        left_buttons = tk.Frame(actions_container, bg=self.theme.CARD_COLOR)
        left_buttons.pack(side=tk.LEFT)

        restore_btn = tk.Button(left_buttons,
                               text=f"{self.theme.ICONS['success']} 恢复选中备份",
                               font=self.theme.BUTTON_FONT,
                               bg=self.theme.SUCCESS_COLOR,
                               fg='white',
                               relief='flat',
                               padx=20,
                               pady=10,
                               command=self.restore_backup)
        restore_btn.pack(side=tk.LEFT, padx=(0, 10))

        view_btn = tk.Button(left_buttons,
                            text=f"{self.theme.ICONS['info']} 查看详情",
                            font=self.theme.BODY_FONT,
                            bg=self.theme.PRIMARY_COLOR,
                            fg='white',
                            relief='flat',
                            padx=15,
                            pady=8,
                            command=self.view_backup_details)
        view_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 右侧按钮组
        right_buttons = tk.Frame(actions_container, bg=self.theme.CARD_COLOR)
        right_buttons.pack(side=tk.RIGHT)

        clean_btn = tk.Button(right_buttons,
                             text=f"{self.theme.ICONS['warning']} 清理旧备份",
                             font=self.theme.BODY_FONT,
                             bg=self.theme.WARNING_COLOR,
                             fg='white',
                             relief='flat',
                             padx=15,
                             pady=8,
                             command=self.clean_backups)
        clean_btn.pack(side=tk.LEFT, padx=(0, 10))

        delete_btn = tk.Button(right_buttons,
                              text=f"{self.theme.ICONS['error']} 删除选中",
                              font=self.theme.BODY_FONT,
                              bg=self.theme.ERROR_COLOR,
                              fg='white',
                              relief='flat',
                              padx=15,
                              pady=8,
                              command=self.delete_selected_backup)
        delete_btn.pack(side=tk.LEFT)

    def on_backup_double_click(self, _):
        """双击备份文件时的处理"""
        self.view_backup_details()

    def view_backup_details(self):
        """查看备份详情"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个备份文件")
            return

        item = self.backup_tree.item(selection[0])
        filename = item['values'][0]

        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backups")
        backup_path = os.path.join(backup_dir, filename)

        if os.path.exists(backup_path):
            try:
                with open(backup_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 创建详情窗口
                details_window = tk.Toplevel(self.root)
                details_window.title(f"备份详情 - {filename}")
                details_window.geometry("600x400")
                details_window.configure(bg=self.theme.BACKGROUND_COLOR)

                # 内容显示
                text_frame = tk.Frame(details_window, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
                text_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

                text_widget = tk.Text(text_frame, wrap=tk.WORD, font=self.theme.BODY_FONT)
                text_scrollbar = ttk.Scrollbar(text_frame, command=text_widget.yview)
                text_widget.config(yscrollcommand=text_scrollbar.set)

                text_widget.insert(tk.END, content)
                text_widget.config(state=tk.DISABLED)

                text_widget.pack(side="left", fill="both", expand=True, padx=10, pady=10)
                text_scrollbar.pack(side="right", fill="y", pady=10)

            except Exception as e:
                messagebox.showerror("错误", f"无法读取备份文件:\n{str(e)}")
        else:
            messagebox.showerror("错误", "备份文件不存在")

    def delete_selected_backup(self):
        """删除选中的备份"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的备份文件")
            return

        item = self.backup_tree.item(selection[0])
        filename = item['values'][0]

        if messagebox.askyesno("确认删除", f"确定要删除备份文件 {filename} 吗？\n此操作不可恢复。"):
            backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backups")
            backup_path = os.path.join(backup_dir, filename)

            try:
                os.remove(backup_path)
                messagebox.showinfo("成功", f"已删除备份文件 {filename}")
                self.refresh_backups()
                self.status_var.set(f"🗑️ 已删除备份: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"删除备份文件失败:\n{str(e)}")
        
    def create_settings_tab(self):
        """创建现代化设置标签页"""
        # 创建设置标签页容器
        settings_tab = tk.Frame(self.notebook, bg=self.theme.BACKGROUND_COLOR)
        self.notebook.add(settings_tab, text=f"{self.theme.ICONS['settings']} 设置")

        # 创建滚动容器
        canvas = tk.Canvas(settings_tab, bg=self.theme.BACKGROUND_COLOR, highlightthickness=0)
        scrollbar = ttk.Scrollbar(settings_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.theme.BACKGROUND_COLOR)

        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")

        # 创建设置卡片
        self.create_security_settings_card(scrollable_frame)
        self.create_log_settings_card(scrollable_frame)
        self.create_appearance_settings_card(scrollable_frame)
        self.create_about_card(scrollable_frame)

    def create_security_settings_card(self, parent):
        """创建安全设置卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 20), padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=50)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['shield']} 安全设置",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=10)

        # 安全选项
        security_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        security_content.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 初始化变量
        self.create_backup_var = tk.BooleanVar(value=True)
        self.encrypt_backup_var = tk.BooleanVar(value=False)
        self.verify_integrity_var = tk.BooleanVar(value=True)
        self.secure_delete_var = tk.BooleanVar(value=False)

        security_options = [
            (self.create_backup_var, "🛡️ 自动创建备份", "修改前自动创建备份文件"),
            (self.encrypt_backup_var, "🔐 加密备份文件", "使用加密保护备份文件"),
            (self.verify_integrity_var, "✅ 验证文件完整性", "修改后验证文件完整性"),
            (self.secure_delete_var, "🗑️ 安全删除临时文件", "安全删除临时和缓存文件")
        ]

        for var, title, description in security_options:
            option_frame = tk.Frame(security_content, bg=self.theme.CARD_COLOR)
            option_frame.pack(fill=tk.X, pady=5)

            cb = tk.Checkbutton(option_frame,
                               text=title,
                               variable=var,
                               font=self.theme.BODY_FONT,
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_COLOR,
                               selectcolor=self.theme.CARD_COLOR,
                               activebackground=self.theme.CARD_COLOR,
                               relief='flat',
                               bd=0)
            cb.pack(anchor=tk.W, padx=10)

            desc_label = tk.Label(option_frame,
                                 text=description,
                                 font=("Microsoft YaHei UI", 9),
                                 bg=self.theme.CARD_COLOR,
                                 fg=self.theme.SECONDARY_COLOR)
            desc_label.pack(anchor=tk.W, padx=30)

    def create_log_settings_card(self, parent):
        """创建日志设置卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 20), padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=50)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['log']} 日志设置",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=10)

        # 日志选项
        log_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        log_content.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 日志级别
        level_frame = tk.Frame(log_content, bg=self.theme.CARD_COLOR)
        level_frame.pack(fill=tk.X, pady=10)

        level_label = tk.Label(level_frame,
                              text="📊 日志级别:",
                              font=self.theme.BODY_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        level_label.pack(anchor=tk.W, padx=10)

        self.log_level_var = tk.StringVar(value="Normal")
        level_combo = ttk.Combobox(level_frame,
                                  textvariable=self.log_level_var,
                                  values=["Minimal", "Normal", "Verbose"],
                                  state="readonly",
                                  font=self.theme.BODY_FONT)
        level_combo.pack(anchor=tk.W, padx=30, pady=5, fill=tk.X)

        # 日志级别说明
        level_descriptions = {
            "Minimal": "仅记录错误和重要信息",
            "Normal": "记录常规操作信息",
            "Verbose": "记录详细的调试信息"
        }

        self.log_desc_var = tk.StringVar(value=level_descriptions["Normal"])
        desc_label = tk.Label(level_frame,
                             textvariable=self.log_desc_var,
                             font=("Microsoft YaHei UI", 9),
                             bg=self.theme.CARD_COLOR,
                             fg=self.theme.SECONDARY_COLOR)
        desc_label.pack(anchor=tk.W, padx=30)

        # 绑定选择事件
        def on_level_change(_):
            selected = self.log_level_var.get()
            self.log_desc_var.set(level_descriptions.get(selected, ""))

        level_combo.bind('<<ComboboxSelected>>', on_level_change)

    def create_appearance_settings_card(self, parent):
        """创建外观设置卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 20), padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=50)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text="🎨 外观设置",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=10)

        # 外观选项
        appearance_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        appearance_content.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 主题选择
        theme_frame = tk.Frame(appearance_content, bg=self.theme.CARD_COLOR)
        theme_frame.pack(fill=tk.X, pady=10)

        theme_label = tk.Label(theme_frame,
                              text="🌈 界面主题:",
                              font=self.theme.BODY_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        theme_label.pack(anchor=tk.W, padx=10)

        self.theme_var = tk.StringVar(value="现代蓝色")
        theme_combo = ttk.Combobox(theme_frame,
                                  textvariable=self.theme_var,
                                  values=["现代蓝色", "经典灰色", "深色模式"],
                                  state="readonly",
                                  font=self.theme.BODY_FONT)
        theme_combo.pack(anchor=tk.W, padx=30, pady=5, fill=tk.X)

        # 字体大小
        font_frame = tk.Frame(appearance_content, bg=self.theme.CARD_COLOR)
        font_frame.pack(fill=tk.X, pady=10)

        font_label = tk.Label(font_frame,
                             text="📝 字体大小:",
                             font=self.theme.BODY_FONT,
                             bg=self.theme.CARD_COLOR,
                             fg=self.theme.TEXT_COLOR)
        font_label.pack(anchor=tk.W, padx=10)

        self.font_size_var = tk.StringVar(value="标准")
        font_combo = ttk.Combobox(font_frame,
                                 textvariable=self.font_size_var,
                                 values=["小", "标准", "大", "特大"],
                                 state="readonly",
                                 font=self.theme.BODY_FONT)
        font_combo.pack(anchor=tk.W, padx=30, pady=5, fill=tk.X)

    def create_about_card(self, parent):
        """创建关于卡片"""
        card_frame = tk.Frame(parent, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        card_frame.pack(fill=tk.X, padx=10)

        # 卡片标题
        title_frame = tk.Frame(card_frame, bg=self.theme.CARD_COLOR, height=50)
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 0))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame,
                              text=f"{self.theme.ICONS['about']} 关于软件",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(side=tk.LEFT, anchor=tk.W, pady=10)

        # 关于内容
        about_content = tk.Frame(card_frame, bg=self.theme.CARD_COLOR)
        about_content.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 软件信息
        info_items = [
            ("🚀 软件名称", "AUG 0.1 Augment ID修改器"),
            ("📦 版本号", "0.1.0"),
            ("🏗️ 构建日期", "2025-06-11"),
            ("💻 支持平台", "Windows, macOS, Linux"),
            ("🔧 技术栈", "Python 3.x + Tkinter"),
            ("📄 许可证", "MIT License")
        ]

        for label, value in info_items:
            info_frame = tk.Frame(about_content, bg=self.theme.CARD_COLOR)
            info_frame.pack(fill=tk.X, pady=3)

            label_widget = tk.Label(info_frame,
                                   text=label,
                                   font=self.theme.BODY_FONT,
                                   bg=self.theme.CARD_COLOR,
                                   fg=self.theme.TEXT_COLOR,
                                   width=15,
                                   anchor=tk.W)
            label_widget.pack(side=tk.LEFT, padx=10)

            value_widget = tk.Label(info_frame,
                                   text=value,
                                   font=self.theme.BODY_FONT,
                                   bg=self.theme.CARD_COLOR,
                                   fg=self.theme.SECONDARY_COLOR)
            value_widget.pack(side=tk.LEFT, padx=10)

        # 按钮区域
        button_frame = tk.Frame(about_content, bg=self.theme.CARD_COLOR)
        button_frame.pack(fill=tk.X, pady=(15, 0))

        github_btn = tk.Button(button_frame,
                              text="🌐 GitHub",
                              font=self.theme.BODY_FONT,
                              bg=self.theme.PRIMARY_COLOR,
                              fg='white',
                              relief='flat',
                              padx=15,
                              pady=8,
                              command=self.open_github)
        github_btn.pack(side=tk.LEFT, padx=10)

        help_btn = tk.Button(button_frame,
                            text="📖 帮助文档",
                            font=self.theme.BODY_FONT,
                            bg=self.theme.SUCCESS_COLOR,
                            fg='white',
                            relief='flat',
                            padx=15,
                            pady=8,
                            command=self.open_help_docs)
        help_btn.pack(side=tk.LEFT, padx=10)

    def open_github(self):
        """打开GitHub页面"""
        messagebox.showinfo("GitHub", "GitHub链接功能开发中...")

    def open_help_docs(self):
        """打开帮助文档"""
        messagebox.showinfo("帮助文档", "帮助文档功能开发中...")
        
    def start_modification(self):
        """Start the ID modification process"""
        selected_editors = [editor for editor, var in self.editor_vars.items() if var.get()]
        
        if not selected_editors:
            messagebox.showwarning("警告", "请至少选择一个编辑器")
            return
            
        mode = self.mode_var.get()
        
        # Build command arguments
        cmd_args = []
        
        if mode == "enhanced":
            cmd_args.append("-Enhanced")
        elif mode == "quick":
            cmd_args.append("-Fields")
            cmd_args.append("machineId,devDeviceId")
        elif mode == "custom":
            selected_fields = [field for field, var in self.field_vars.items() if var.get()]
            if not selected_fields:
                messagebox.showwarning("警告", "自定义模式下请至少选择一个字段")
                return
            cmd_args.append("-Fields")
            cmd_args.append(",".join(selected_fields))
            
        # Add editor selection
        cmd_args.append("-Editor")
        cmd_args.append(",".join([e.split(" ")[0] for e in selected_editors]))
        
        # Add security options
        if self.encrypt_backup_var.get():
            cmd_args.append("-EncryptBackups")
            
        # Add log level
        cmd_args.append("-LogLevel")
        cmd_args.append(self.log_level_var.get())
        
        # 创建进度窗口
        progress_window = tk.Toplevel(self.root)
        progress_window.title("执行中")
        progress_window.geometry("500x300")
        progress_window.transient(self.root)
        progress_window.grab_set()
        
        # 进度框架
        progress_frame = ttk.Frame(progress_window, padding=10)
        progress_frame.pack(fill=tk.BOTH, expand=True)
        
        # 进度标签
        progress_label = ttk.Label(progress_frame, text="正在执行修改操作...", font=("Arial", 12))
        progress_label.pack(pady=(0, 10))
        
        # 进度条
        progress_bar = ttk.Progressbar(progress_frame, mode="indeterminate")
        progress_bar.pack(fill=tk.X, pady=(0, 10))
        progress_bar.start(10)
        
        # 日志文本框
        log_frame = ttk.LabelFrame(progress_frame, text="执行日志")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        log_scrollbar = ttk.Scrollbar(log_frame, command=log_text.yview)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        log_text.config(yscrollcommand=log_scrollbar.set)
        
        # 更新状态
        self.status_var.set("正在执行修改...")
        self.root.update()
        
        # 添加日志函数
        def add_log(message):
            log_text.insert(tk.END, message + "\n")
            log_text.see(tk.END)
            log_text.update()
        
        # 验证脚本路径
        script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                  "scripts", "enhanced_id_modifier.ps1")
        
        if not os.path.exists(script_path):
            add_log(f"错误: 脚本文件不存在: {script_path}")
            progress_label.config(text="错误: 脚本文件不存在")
            progress_bar.stop()
            return
        
        add_log(f"找到脚本文件: {script_path}")
        add_log(f"选择的编辑器: {', '.join(selected_editors)}")
        add_log(f"操作模式: {mode}")
        
        try:
            # 使用PowerShell执行脚本
            cmd = ["powershell", "-NonInteractive", "-ExecutionPolicy", "Bypass", "-NoProfile", "-File", script_path] + cmd_args
            
            # 显示执行命令
            command_str = ' '.join(cmd)
            add_log(f"执行命令: {command_str}")
            
            # 执行前检查编辑器是否关闭
            for editor in selected_editors:
                if "VSCode" in editor:
                    process_name = "Code"
                elif "Cursor" in editor:
                    process_name = "Cursor"
                elif "VSCodium" in editor:
                    process_name = "VSCodium"
                else:
                    process_name = editor.split(" ")[0]
                
                check_cmd = ["powershell", "-Command", f"Get-Process {process_name} -ErrorAction SilentlyContinue"]
                check_process = subprocess.run(check_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                
                if check_process.returncode == 0 and check_process.stdout.strip():
                    add_log(f"警告: {editor} 可能仍在运行中，这可能会影响修改效果")
            
            # 执行脚本
            add_log("开始执行脚本...")
            
            def execute_script():
                nonlocal cmd
                # 添加超时参数，避免脚本卡住
                cmd.append("-Silent")
                
                add_log("使用非交互模式执行脚本...")
                
                try:
                    # 使用超时参数运行进程
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1, universal_newlines=True)
                    
                    # 设置超时时间（秒）
                    timeout = 120  # 增加超时时间到2分钟
                    start_time = time.time()
                    
                    # 实时读取输出
                    while True:
                        # 检查是否超时
                        if time.time() - start_time > timeout:
                            add_log("警告: 脚本执行超时，强制终止")
                            process.terminate()
                            # 更新UI
                            progress_window.after(0, lambda: process_result(1, "脚本执行超时，可能是因为等待用户输入或遇到未处理的错误"))
                            return
                            
                        # 非阻塞方式读取输出
                        output_line = process.stdout.readline()
                        if output_line == '' and process.poll() is not None:
                            break
                        if output_line:
                            add_log(output_line.strip())
                            # 重置超时计时器，只要有输出就不算超时
                            start_time = time.time()
                    
                    # 获取返回值和错误输出
                    return_code = process.poll()
                    stderr = process.stderr.read()
                    
                    # 更新UI
                    progress_window.after(0, lambda: process_result(return_code, stderr))
                    
                except Exception as e:
                    add_log(f"执行脚本时出错: {str(e)}")
                    progress_window.after(0, lambda: process_result(1, str(e)))
            
            # 在单独的线程中执行脚本
            import threading
            script_thread = threading.Thread(target=execute_script)
            script_thread.daemon = True
            script_thread.start()
            
            # 添加备用方案按钮
            backup_button = ttk.Button(progress_frame, text="使用直接修改模式", 
                                      command=lambda: self.use_direct_modify(selected_editors, mode))
            backup_button.pack(pady=(10, 0))
            
            # 处理执行结果
            def process_result(return_code, stderr):
                progress_bar.stop()
                
                if return_code == 0:
                    # 验证修改是否生效
                    add_log("脚本执行成功，正在验证修改...")
                    
                    # 检查是否创建了备份文件
                    backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backups")
                    if os.path.exists(backup_dir):
                        backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.json')]
                        if backup_files:
                            add_log(f"已创建备份文件: {len(backup_files)} 个")
                            progress_label.config(text="修改成功完成！")
                            
                            # 刷新备份列表
                            self.refresh_backups()
                            
                            # 显示成功消息
                            messagebox.showinfo("成功", "Augment ID修改成功完成！\n请重启编辑器以应用更改。")
                            self.status_var.set("修改完成")
                            progress_window.destroy()
                        else:
                            add_log("警告: 未找到备份文件，修改可能未成功")
                            progress_label.config(text="警告: 未找到备份文件")
                    else:
                        add_log("警告: 未找到备份目录，修改可能未成功")
                        progress_label.config(text="警告: 未找到备份目录")
                else:
                    add_log(f"错误: 脚本执行失败，返回代码: {return_code}")
                    add_log(f"错误信息: {stderr}")
                    progress_label.config(text="修改失败")
                    messagebox.showerror("错误", f"修改过程中发生错误:\n{stderr}")
                    self.status_var.set("修改失败")
            
        except Exception as e:
            add_log(f"错误: {str(e)}")
            progress_bar.stop()
            progress_label.config(text="执行出错")
            messagebox.showerror("错误", f"执行脚本时发生错误:\n{str(e)}")
            self.status_var.set("执行错误")
    
    def detect_editors(self):
        """Detect installed editors"""
        self.status_var.set("正在检测编辑器...")
        self.root.update()
        
        try:
            # Check common installation paths
            editors = {
                "VSCode": "%APPDATA%\\Code\\User\\globalStorage\\storage.json",
                "VSCode Insiders": "%APPDATA%\\Code - Insiders\\User\\globalStorage\\storage.json",
                "Cursor Editor": "%APPDATA%\\Cursor\\User\\globalStorage\\storage.json",
                "VSCodium": "%APPDATA%\\VSCodium\\User\\globalStorage\\storage.json",
                "Code - OSS": "%APPDATA%\\Code - OSS\\User\\globalStorage\\storage.json"
            }
            
            found_editors = []
            
            for editor, path in editors.items():
                expanded_path = os.path.expandvars(path)
                if os.path.exists(expanded_path):
                    found_editors.append(editor)
                    self.editor_vars[editor].set(True)
                else:
                    self.editor_vars[editor].set(False)
            
            if found_editors:
                messagebox.showinfo("检测结果", f"检测到 {len(found_editors)} 个编辑器:\n" + 
                                   "\n".join(found_editors))
            else:
                messagebox.showinfo("检测结果", "未检测到支持的编辑器")
                
            self.status_var.set(f"检测到 {len(found_editors)} 个编辑器")
        except Exception as e:
            messagebox.showerror("错误", f"检测编辑器时发生错误:\n{str(e)}")
            self.status_var.set("检测失败")
    
    def refresh_backups(self):
        """刷新备份列表"""
        # 清空现有项目
        if hasattr(self, 'backup_tree'):
            for item in self.backup_tree.get_children():
                self.backup_tree.delete(item)

        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backups")
        if not os.path.exists(backup_dir):
            self.status_var.set("📁 备份目录不存在")
            return

        backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.json')]
        backup_files.sort(reverse=True)

        if hasattr(self, 'backup_tree'):
            for backup in backup_files:
                backup_path = os.path.join(backup_dir, backup)

                # 解析文件信息
                try:
                    # 从文件名解析编辑器类型
                    editor_name = backup.split('_')[0]

                    # 获取文件大小
                    file_size = os.path.getsize(backup_path)
                    size_str = self.format_file_size(file_size)

                    # 获取创建时间
                    create_time = os.path.getctime(backup_path)
                    time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(create_time))

                    # 添加到树形视图
                    self.backup_tree.insert('', 'end', values=(backup, editor_name, time_str, size_str))

                except Exception as e:
                    # 如果解析失败，使用默认值
                    self.backup_tree.insert('', 'end', values=(backup, "未知", "未知", "未知"))

        self.status_var.set(f"📁 找到 {len(backup_files)} 个备份文件")

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"
    
    def restore_backup(self):
        """恢复选中的备份"""
        if hasattr(self, 'backup_tree'):
            selection = self.backup_tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请选择一个备份文件")
                return

            item = self.backup_tree.item(selection[0])
            backup_name = item['values'][0]
        else:
            # 兼容旧版本
            if hasattr(self, 'backup_listbox'):
                selection = self.backup_listbox.curselection()
                if not selection:
                    messagebox.showwarning("警告", "请选择一个备份文件")
                    return
                backup_name = self.backup_listbox.get(selection[0])
            else:
                messagebox.showwarning("警告", "备份列表未初始化")
                return
        
        # Extract editor name from backup filename
        editor_name = backup_name.split('_')[0]
        
        if messagebox.askyesno("确认", f"确定要恢复 {backup_name} 吗？\n这将覆盖当前的 {editor_name} 设置。"):
            try:
                script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                          "scripts", "enhanced_id_modifier.ps1")
                
                # Get backup date from filename
                date_part = backup_name.split('_')[2].split('.')[0]
                
                # Use PowerShell to execute the restore
                cmd = ["powershell", "-ExecutionPolicy", "Bypass", "-File", script_path,
                      "-Restore", "-Editor", editor_name, "-BackupDate", date_part]
                
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                _, stderr = process.communicate()
                
                if process.returncode == 0:
                    messagebox.showinfo("成功", f"成功恢复备份 {backup_name}")
                    self.status_var.set("恢复完成")
                else:
                    messagebox.showerror("错误", f"恢复过程中发生错误:\n{stderr}")
                    self.status_var.set("恢复失败")
            except Exception as e:
                messagebox.showerror("错误", f"执行恢复时发生错误:\n{str(e)}")
                self.status_var.set("恢复错误")
    
    def clean_backups(self):
        """Clean old backups"""
        days = tk.simpledialog.askinteger("清理备份", "清理多少天前的备份？", minvalue=1, maxvalue=365)
        if not days:
            return
            
        try:
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                      "scripts", "enhanced_id_modifier.ps1")
            
            # Use PowerShell to execute the cleanup
            cmd = ["powershell", "-ExecutionPolicy", "Bypass", "-File", script_path,
                  "-CleanBackups", "-OlderThan", str(days)]
            
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            _, stderr = process.communicate()
            
            if process.returncode == 0:
                messagebox.showinfo("成功", f"成功清理 {days} 天前的备份")
                self.status_var.set("清理完成")
                self.refresh_backups()
            else:
                messagebox.showerror("错误", f"清理过程中发生错误:\n{stderr}")
                self.status_var.set("清理失败")
        except Exception as e:
            messagebox.showerror("错误", f"执行清理时发生错误:\n{str(e)}")
            self.status_var.set("清理错误")

    def use_direct_modify(self, editors, mode):
        """使用直接修改模式"""
        try:
            # 构建命令行参数
            editors_str = ",".join([e.split(" ")[0] for e in editors])
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "archive", "direct_modify.py")
            cmd = [sys.executable, script_path, "--editors", editors_str, "--mode", mode]
            
            # 显示进度窗口
            progress_window = tk.Toplevel(self.root)
            progress_window.title("直接修改模式")
            progress_window.geometry("500x300")
            progress_window.transient(self.root)
            progress_window.grab_set()
            
            # 进度框架
            progress_frame = ttk.Frame(progress_window, padding=10)
            progress_frame.pack(fill=tk.BOTH, expand=True)
            
            # 进度标签
            progress_label = ttk.Label(progress_frame, text="正在使用直接修改模式...", font=("Arial", 12))
            progress_label.pack(pady=(0, 10))
            
            # 进度条
            progress_bar = ttk.Progressbar(progress_frame, mode="indeterminate")
            progress_bar.pack(fill=tk.X, pady=(0, 10))
            progress_bar.start(10)
            
            # 日志文本框
            log_frame = ttk.LabelFrame(progress_frame, text="执行日志")
            log_frame.pack(fill=tk.BOTH, expand=True)
            
            log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
            log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            
            log_scrollbar = ttk.Scrollbar(log_frame, command=log_text.yview)
            log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            log_text.config(yscrollcommand=log_scrollbar.set)
            
            # 更新状态
            self.status_var.set("正在使用直接修改模式...")
            self.root.update()
            
            # 添加日志函数
            def add_log(message):
                log_text.insert(tk.END, message + "\n")
                log_text.see(tk.END)
                log_text.update()
            
            add_log("启动直接修改模式...")
            add_log(f"选择的编辑器: {', '.join(editors)}")
            add_log(f"操作模式: {mode}")
            
            # 执行Python脚本
            def execute_direct_modify():
                try:
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1, universal_newlines=True)
                    
                    # 实时读取输出
                    while True:
                        output_line = process.stdout.readline()
                        if output_line == '' and process.poll() is not None:
                            break
                        if output_line:
                            add_log(output_line.strip())
                    
                    # 获取返回值和错误输出
                    return_code = process.poll()
                    stderr = process.stderr.read()
                    
                    # 更新UI
                    progress_window.after(0, lambda: process_direct_result(return_code, stderr))
                    
                except Exception as e:
                    add_log(f"执行脚本时出错: {str(e)}")
                    progress_window.after(0, lambda: process_direct_result(1, str(e)))
            
            # 在单独的线程中执行脚本
            script_thread = threading.Thread(target=execute_direct_modify)
            script_thread.daemon = True
            script_thread.start()
            
            # 处理执行结果
            def process_direct_result(return_code, stderr):
                progress_bar.stop()
                
                if return_code == 0 or stderr == "":
                    add_log("直接修改完成")
                    progress_label.config(text="修改成功完成！")
                    
                    # 刷新备份列表
                    self.refresh_backups()
                    
                    # 显示成功消息
                    messagebox.showinfo("成功", "Augment ID修改成功完成！\n请重启编辑器以应用更改。")
                    self.status_var.set("修改完成")
                    progress_window.destroy()
                else:
                    add_log(f"错误: {stderr}")
                    progress_label.config(text="修改失败")
                    messagebox.showerror("错误", f"修改过程中发生错误:\n{stderr}")
                    self.status_var.set("修改失败")
            
        except Exception as e:
            messagebox.showerror("错误", f"启动直接修改模式时发生错误:\n{str(e)}")
            self.status_var.set("执行错误")
    
    def check_powershell(self):
        """Check if PowerShell is available and has proper execution policy"""
        try:
            # Check if PowerShell is available
            process = subprocess.run(["powershell", "-Command", "echo 'PowerShell Test'"], 
                                    capture_output=True, text=True, check=False)
            
            if process.returncode != 0:
                messagebox.showwarning("警告", "无法运行PowerShell。部分功能可能无法正常工作。")
                return False
                
            # Check execution policy
            process = subprocess.run(["powershell", "-Command", "Get-ExecutionPolicy"], 
                                    capture_output=True, text=True, check=False)
            
            policy = process.stdout.strip()
            if policy in ["Restricted", "AllSigned"]:
                messagebox.showwarning("警告", f"PowerShell执行策略设置为 {policy}，可能无法运行脚本。\n"
                                      "请以管理员身份运行PowerShell并执行:\n"
                                      "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser")
                return False
                
            return True
        except Exception as e:
            messagebox.showwarning("警告", f"检查PowerShell时出错: {str(e)}")
            return False

    # 数据库清理相关方法
    def scan_databases(self):
        """扫描编辑器数据库"""
        try:
            self.status_var.set("🔍 正在扫描数据库...")

            # 清空现有项目
            if hasattr(self, 'database_tree'):
                for item in self.database_tree.get_children():
                    self.database_tree.delete(item)

            # 定义编辑器数据库路径
            database_paths = {
                "VSCode": [
                    "%APPDATA%\\Code\\User\\globalStorage\\storage.json",
                    "%APPDATA%\\Code\\logs\\*.log",
                    "%APPDATA%\\Code\\CachedExtensions\\*"
                ],
                "VSCode Insiders": [
                    "%APPDATA%\\Code - Insiders\\User\\globalStorage\\storage.json",
                    "%APPDATA%\\Code - Insiders\\logs\\*.log"
                ],
                "Cursor Editor": [
                    "%APPDATA%\\Cursor\\User\\globalStorage\\storage.json",
                    "%APPDATA%\\Cursor\\logs\\*.log"
                ],
                "VSCodium": [
                    "%APPDATA%\\VSCodium\\User\\globalStorage\\storage.json",
                    "%APPDATA%\\VSCodium\\logs\\*.log"
                ],
                "Code - OSS": [
                    "%APPDATA%\\Code - OSS\\User\\globalStorage\\storage.json",
                    "%APPDATA%\\Code - OSS\\logs\\*.log"
                ]
            }

            found_databases = 0

            for editor, paths in database_paths.items():
                for path_pattern in paths:
                    expanded_path = os.path.expandvars(path_pattern)

                    if '*' in expanded_path:
                        # 处理通配符路径
                        import glob
                        matching_files = glob.glob(expanded_path)
                        for file_path in matching_files:
                            if os.path.exists(file_path):
                                self._add_database_to_tree(file_path, editor)
                                found_databases += 1
                    else:
                        # 处理单个文件路径
                        if os.path.exists(expanded_path):
                            self._add_database_to_tree(expanded_path, editor)
                            found_databases += 1

            self.status_var.set(f"🔍 扫描完成，找到 {found_databases} 个数据库文件")

            if found_databases == 0:
                messagebox.showinfo("扫描结果", "未找到任何编辑器数据库文件")
            else:
                messagebox.showinfo("扫描结果", f"找到 {found_databases} 个数据库文件")

        except Exception as e:
            messagebox.showerror("错误", f"扫描数据库时发生错误:\n{str(e)}")
            self.status_var.set("扫描失败")

    def _add_database_to_tree(self, file_path, editor):
        """添加数据库信息到树形视图"""
        try:
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            size_str = self.format_file_size(file_size)

            # 估算记录数（对于JSON文件）
            record_count = "未知"
            status = "正常"

            if file_path.endswith('.json'):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 简单估算记录数
                        record_count = str(content.count('"telemetry.') + content.count('"augment'))

                        # 检查是否包含敏感数据
                        if 'augment' in content.lower():
                            status = "包含Augment数据"
                        elif 'telemetry' in content.lower():
                            status = "包含遥测数据"
                except:
                    record_count = "读取失败"
                    status = "无法读取"

            # 添加到树形视图
            if hasattr(self, 'database_tree'):
                self.database_tree.insert('', 'end', values=(
                    file_path, editor, size_str, record_count, status
                ))

        except Exception as e:
            # 如果处理失败，仍然添加基本信息
            if hasattr(self, 'database_tree'):
                self.database_tree.insert('', 'end', values=(
                    file_path, editor, "未知", "未知", f"错误: {str(e)}"
                ))

    def preview_database_cleaning(self):
        """预览数据库清理操作"""
        try:
            # 获取选中的清理选项
            clean_options = []
            if self.clean_telemetry_var.get():
                clean_options.append("遥测记录")
            if self.clean_augment_var.get():
                clean_options.append("Augment记录")
            if self.clean_workspace_var.get():
                clean_options.append("工作区数据")
            if self.clean_cache_var.get():
                clean_options.append("缓存数据")

            if not clean_options:
                messagebox.showwarning("警告", "请至少选择一个清理选项")
                return

            # 创建预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title("清理预览")
            preview_window.geometry("700x500")
            preview_window.configure(bg=self.theme.BACKGROUND_COLOR)

            # 预览内容
            preview_frame = tk.Frame(preview_window, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
            preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # 标题
            title_label = tk.Label(preview_frame,
                                  text="🔍 清理预览",
                                  font=self.theme.HEADER_FONT,
                                  bg=self.theme.CARD_COLOR,
                                  fg=self.theme.TEXT_COLOR)
            title_label.pack(pady=15)

            # 预览文本
            preview_text = tk.Text(preview_frame, wrap=tk.WORD, font=self.theme.BODY_FONT)
            preview_scrollbar = ttk.Scrollbar(preview_frame, command=preview_text.yview)
            preview_text.config(yscrollcommand=preview_scrollbar.set)

            # 生成预览内容
            preview_content = f"""
📋 清理操作预览

🎯 将要清理的内容：
{chr(10).join([f"• {option}" for option in clean_options])}

🗄️ 将要处理的数据库：
"""

            # 添加数据库信息
            if hasattr(self, 'database_tree'):
                for item in self.database_tree.get_children():
                    values = self.database_tree.item(item)['values']
                    preview_content += f"• {values[1]}: {values[0]}\n"

            preview_content += f"""
⚠️ 注意事项：
• 清理前将自动创建备份
• 清理操作不可撤销（除非从备份恢复）
• 建议在编辑器关闭时进行清理
• 清理后需要重启编辑器

🛡️ 安全措施：
• 自动备份数据库文件
• 验证文件完整性
• 详细记录清理日志
• 支持一键恢复
"""

            preview_text.insert(tk.END, preview_content)
            preview_text.config(state=tk.DISABLED)

            preview_text.pack(side="left", fill="both", expand=True, padx=10, pady=10)
            preview_scrollbar.pack(side="right", fill="y", pady=10)

            # 按钮框架
            button_frame = tk.Frame(preview_frame, bg=self.theme.CARD_COLOR)
            button_frame.pack(fill=tk.X, padx=20, pady=10)

            # 确认清理按钮
            confirm_btn = tk.Button(button_frame,
                                   text=f"{self.theme.ICONS['clean']} 确认清理",
                                   font=self.theme.BUTTON_FONT,
                                   bg=self.theme.WARNING_COLOR,
                                   fg='white',
                                   relief='flat',
                                   padx=20,
                                   pady=10,
                                   command=lambda: [preview_window.destroy(), self.start_database_cleaning()])
            confirm_btn.pack(side=tk.LEFT, padx=(0, 10))

            # 取消按钮
            cancel_btn = tk.Button(button_frame,
                                  text="取消",
                                  font=self.theme.BODY_FONT,
                                  bg=self.theme.SECONDARY_COLOR,
                                  fg='white',
                                  relief='flat',
                                  padx=20,
                                  pady=10,
                                  command=preview_window.destroy)
            cancel_btn.pack(side=tk.LEFT)

        except Exception as e:
            messagebox.showerror("错误", f"生成预览时发生错误:\n{str(e)}")

    def smart_database_cleaning(self):
        """智能数据库清理"""
        try:
            # 显示智能清理说明
            info_text = """
🧠 智能清理模式

智能清理将自动：
• 检测编辑器运行状态
• 选择最佳清理策略
• 自动处理进程冲突
• 提供最彻底的清理效果

⚠️ 注意：
• 如果编辑器正在运行，将提示您选择处理方式
• 深度清理模式会终止编辑器进程
• 建议保存所有工作后再进行清理

是否继续智能清理？
            """

            if not messagebox.askyesno("智能清理", info_text):
                return

            # 执行智能清理脚本
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Smart-Clean.bat")

            if os.path.exists(script_path):
                self.status_var.set("🧠 正在执行智能清理...")

                # 在新窗口中显示清理过程
                self._show_smart_clean_progress(script_path)
            else:
                # 如果脚本不存在，使用Python实现
                self._execute_smart_clean_python()

        except Exception as e:
            messagebox.showerror("错误", f"启动智能清理时发生错误:\n{str(e)}")
            self.status_var.set("智能清理错误")

    def _show_smart_clean_progress(self, script_path):
        """显示智能清理进度"""
        # 创建进度窗口
        progress_window = tk.Toplevel(self.root)
        progress_window.title("智能清理进度")
        progress_window.geometry("700x500")
        progress_window.configure(bg=self.theme.BACKGROUND_COLOR)
        progress_window.transient(self.root)
        progress_window.grab_set()

        # 进度框架
        progress_frame = tk.Frame(progress_window, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
        progress_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        title_label = tk.Label(progress_frame,
                              text="🧠 智能清理进行中",
                              font=self.theme.HEADER_FONT,
                              bg=self.theme.CARD_COLOR,
                              fg=self.theme.TEXT_COLOR)
        title_label.pack(pady=15)

        # 进度条
        progress_bar = ttk.Progressbar(progress_frame, mode="indeterminate")
        progress_bar.pack(fill=tk.X, padx=20, pady=10)
        progress_bar.start(10)

        # 状态标签
        status_label = tk.Label(progress_frame,
                               text="正在检测系统状态...",
                               font=self.theme.BODY_FONT,
                               bg=self.theme.CARD_COLOR,
                               fg=self.theme.TEXT_COLOR)
        status_label.pack(pady=5)

        # 日志文本框
        log_frame = tk.LabelFrame(progress_frame, text="清理日志", font=self.theme.BODY_FONT)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        log_text = tk.Text(log_frame, height=20, wrap=tk.WORD, font=("Consolas", 9))
        log_scrollbar = ttk.Scrollbar(log_frame, command=log_text.yview)
        log_text.config(yscrollcommand=log_scrollbar.set)

        log_text.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        log_scrollbar.pack(side="right", fill="y", pady=5)

        # 执行智能清理
        def execute_smart_clean():
            try:
                # 执行批处理脚本
                process = subprocess.Popen(
                    script_path,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    shell=True,
                    cwd=os.path.dirname(script_path)
                )

                # 实时读取输出
                while True:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        progress_window.after(0, lambda msg=output.strip(): add_log(msg))

                # 获取返回码
                return_code = process.poll()
                success = return_code == 0

                progress_window.after(0, lambda: process_smart_clean_result(success))

            except Exception as e:
                progress_window.after(0, lambda: add_log(f"执行错误: {str(e)}"))
                progress_window.after(0, lambda: process_smart_clean_result(False))

        # 添加日志函数
        def add_log(message):
            if message.strip():
                timestamp = time.strftime("%H:%M:%S")
                log_text.insert(tk.END, f"[{timestamp}] {message}\n")
                log_text.see(tk.END)
                log_text.update()

        # 处理清理结果
        def process_smart_clean_result(success):
            progress_bar.stop()

            if success:
                status_label.config(text="✅ 智能清理完成")
                add_log("智能清理成功完成")
                self.status_var.set("✅ 智能清理完成")

                # 刷新数据库扫描
                self.scan_databases()

                messagebox.showinfo("成功", "智能清理成功完成！\n建议等待5分钟后再启动编辑器。")
            else:
                status_label.config(text="❌ 智能清理失败")
                add_log("智能清理失败")
                self.status_var.set("❌ 智能清理失败")
                messagebox.showerror("错误", "智能清理过程中发生错误，请查看日志了解详情。")

            # 添加关闭按钮
            close_btn = tk.Button(progress_frame,
                                 text="关闭",
                                 font=self.theme.BUTTON_FONT,
                                 bg=self.theme.PRIMARY_COLOR,
                                 fg='white',
                                 relief='flat',
                                 padx=20,
                                 pady=10,
                                 command=progress_window.destroy)
            close_btn.pack(pady=10)

        # 在单独的线程中执行清理
        cleaning_thread = threading.Thread(target=execute_smart_clean)
        cleaning_thread.daemon = True
        cleaning_thread.start()

    def _execute_smart_clean_python(self):
        """使用Python实现智能清理"""
        try:
            # 检测编辑器状态
            editors_running = self._detect_running_editors()

            if editors_running:
                choice = messagebox.askyesnocancel(
                    "检测到运行中的编辑器",
                    f"检测到以下编辑器正在运行：\n{', '.join(editors_running)}\n\n"
                    "选择清理方式：\n"
                    "• 是：终止编辑器并执行深度清理\n"
                    "• 否：执行安全清理（不影响运行中的编辑器）\n"
                    "• 取消：取消清理操作"
                )

                if choice is None:  # 取消
                    return
                elif choice:  # 深度清理
                    self._execute_deep_clean()
                else:  # 安全清理
                    self.start_database_cleaning()
            else:
                # 没有编辑器运行，直接执行深度清理
                self._execute_deep_clean()

        except Exception as e:
            messagebox.showerror("错误", f"智能清理执行错误:\n{str(e)}")

    def _detect_running_editors(self):
        """检测运行中的编辑器"""
        running_editors = []
        editors_to_check = [
            ("Code.exe", "Visual Studio Code"),
            ("Cursor.exe", "Cursor Editor"),
            ("VSCodium.exe", "VSCodium"),
            ("code-oss.exe", "Code - OSS"),
            ("Code - Insiders.exe", "VS Code Insiders")
        ]

        try:
            for process_name, display_name in editors_to_check:
                result = subprocess.run(
                    ["tasklist", "/FI", f"IMAGENAME eq {process_name}"],
                    capture_output=True, text=True, check=False
                )

                if process_name.lower() in result.stdout.lower():
                    running_editors.append(display_name)

        except Exception as e:
            print(f"检测编辑器进程时出错: {e}")

        return running_editors

    def _execute_deep_clean(self):
        """执行深度清理"""
        try:
            # 调用数据库清理器的深度清理功能
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "database_cleaner.py")

            if os.path.exists(script_path):
                cmd = [
                    "python", script_path,
                    "--deep-clean",
                    "--clean-options", "telemetry", "augment", "workspace", "cache"
                ]

                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    messagebox.showinfo("成功", "深度清理成功完成！\n建议等待5分钟后再启动编辑器。")
                    self.status_var.set("✅ 深度清理完成")
                    self.scan_databases()
                else:
                    messagebox.showerror("错误", f"深度清理失败:\n{result.stderr}")
                    self.status_var.set("❌ 深度清理失败")
            else:
                messagebox.showerror("错误", "找不到数据库清理器脚本")

        except Exception as e:
            messagebox.showerror("错误", f"执行深度清理时发生错误:\n{str(e)}")

    def start_database_cleaning(self):
        """开始数据库清理"""
        try:
            # 检查是否选择了清理选项
            clean_options = []
            if self.clean_telemetry_var.get():
                clean_options.append("telemetry")
            if self.clean_augment_var.get():
                clean_options.append("augment")
            if self.clean_workspace_var.get():
                clean_options.append("workspace")
            if self.clean_cache_var.get():
                clean_options.append("cache")

            if not clean_options:
                messagebox.showwarning("警告", "请至少选择一个清理选项")
                return

            # 确认对话框
            if not messagebox.askyesno("确认清理",
                                      "确定要开始数据库清理吗？\n\n"
                                      "此操作将：\n"
                                      "• 自动备份所有数据库文件\n"
                                      "• 清理选中的数据类型\n"
                                      "• 验证清理结果\n\n"
                                      "建议在编辑器关闭时进行此操作。"):
                return

            # 创建进度窗口
            progress_window = tk.Toplevel(self.root)
            progress_window.title("数据库清理进度")
            progress_window.geometry("600x400")
            progress_window.configure(bg=self.theme.BACKGROUND_COLOR)
            progress_window.transient(self.root)
            progress_window.grab_set()

            # 进度框架
            progress_frame = tk.Frame(progress_window, bg=self.theme.CARD_COLOR, relief='solid', bd=1)
            progress_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # 标题
            title_label = tk.Label(progress_frame,
                                  text="🧹 数据库清理进行中",
                                  font=self.theme.HEADER_FONT,
                                  bg=self.theme.CARD_COLOR,
                                  fg=self.theme.TEXT_COLOR)
            title_label.pack(pady=15)

            # 进度条
            progress_bar = ttk.Progressbar(progress_frame, mode="indeterminate")
            progress_bar.pack(fill=tk.X, padx=20, pady=10)
            progress_bar.start(10)

            # 状态标签
            status_label = tk.Label(progress_frame,
                                   text="正在准备清理...",
                                   font=self.theme.BODY_FONT,
                                   bg=self.theme.CARD_COLOR,
                                   fg=self.theme.TEXT_COLOR)
            status_label.pack(pady=5)

            # 日志文本框
            log_frame = tk.LabelFrame(progress_frame, text="清理日志", font=self.theme.BODY_FONT)
            log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            log_text = tk.Text(log_frame, height=15, wrap=tk.WORD, font=("Consolas", 9))
            log_scrollbar = ttk.Scrollbar(log_frame, command=log_text.yview)
            log_text.config(yscrollcommand=log_scrollbar.set)

            log_text.pack(side="left", fill="both", expand=True, padx=5, pady=5)
            log_scrollbar.pack(side="right", fill="y", pady=5)

            # 更新状态
            self.status_var.set("🧹 正在清理数据库...")

            # 添加日志函数
            def add_log(message):
                timestamp = time.strftime("%H:%M:%S")
                log_text.insert(tk.END, f"[{timestamp}] {message}\n")
                log_text.see(tk.END)
                log_text.update()

            # 执行清理
            def execute_cleaning():
                try:
                    add_log("开始数据库清理操作")
                    add_log(f"清理选项: {', '.join(clean_options)}")

                    # 调用数据库清理脚本
                    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                              "scripts", "database_cleaner.ps1")

                    if not os.path.exists(script_path):
                        add_log("警告: 数据库清理脚本不存在，使用内置清理方法")
                        result = self._clean_databases_builtin(clean_options, add_log)
                    else:
                        add_log("使用PowerShell脚本进行清理")
                        result = self._clean_databases_powershell(script_path, clean_options, add_log)

                    # 更新UI
                    progress_window.after(0, lambda: process_cleaning_result(result))

                except Exception as e:
                    add_log(f"清理过程中发生错误: {str(e)}")
                    progress_window.after(0, lambda: process_cleaning_result(False))

            # 处理清理结果
            def process_cleaning_result(success):
                progress_bar.stop()

                if success:
                    status_label.config(text="✅ 清理完成")
                    add_log("数据库清理成功完成")

                    # 刷新数据库扫描
                    self.scan_databases()

                    messagebox.showinfo("成功", "数据库清理成功完成！\n请重启编辑器以应用更改。")
                    self.status_var.set("✅ 清理完成")
                else:
                    status_label.config(text="❌ 清理失败")
                    add_log("数据库清理失败")
                    messagebox.showerror("错误", "数据库清理过程中发生错误，请查看日志了解详情。")
                    self.status_var.set("❌ 清理失败")

                # 添加关闭按钮
                close_btn = tk.Button(progress_frame,
                                     text="关闭",
                                     font=self.theme.BUTTON_FONT,
                                     bg=self.theme.PRIMARY_COLOR,
                                     fg='white',
                                     relief='flat',
                                     padx=20,
                                     pady=10,
                                     command=progress_window.destroy)
                close_btn.pack(pady=10)

            # 在单独的线程中执行清理
            cleaning_thread = threading.Thread(target=execute_cleaning)
            cleaning_thread.daemon = True
            cleaning_thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动数据库清理时发生错误:\n{str(e)}")
            self.status_var.set("清理错误")

    def _clean_databases_builtin(self, clean_options, add_log):
        """内置数据库清理方法"""
        try:
            add_log("使用内置清理方法")
            success_count = 0
            total_count = 0

            # 获取要清理的数据库文件
            if hasattr(self, 'database_tree'):
                for item in self.database_tree.get_children():
                    values = self.database_tree.item(item)['values']
                    db_path = values[0]
                    editor = values[1]

                    if db_path.endswith('.json'):
                        total_count += 1
                        add_log(f"正在清理: {editor} - {os.path.basename(db_path)}")

                        if self._clean_json_database(db_path, clean_options, add_log):
                            success_count += 1
                            add_log(f"✅ 清理成功: {os.path.basename(db_path)}")
                        else:
                            add_log(f"❌ 清理失败: {os.path.basename(db_path)}")

            add_log(f"清理完成: {success_count}/{total_count} 个文件成功清理")
            return success_count == total_count and total_count > 0

        except Exception as e:
            add_log(f"内置清理方法错误: {str(e)}")
            return False

    def _clean_json_database(self, file_path, clean_options, add_log):
        """清理JSON数据库文件"""
        try:
            # 创建备份
            if self.db_backup_var.get():
                backup_path = self._create_database_backup(file_path, add_log)
                if not backup_path:
                    add_log(f"备份失败，跳过清理: {file_path}")
                    return False

            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            original_size = len(content)
            modified = False

            # 解析JSON
            try:
                import json
                data = json.loads(content)
            except json.JSONDecodeError:
                add_log(f"JSON格式错误，跳过: {os.path.basename(file_path)}")
                return False

            # 执行清理操作
            if "telemetry" in clean_options:
                modified |= self._remove_telemetry_data(data, add_log)

            if "augment" in clean_options:
                modified |= self._remove_augment_data(data, add_log)

            if "workspace" in clean_options:
                modified |= self._remove_workspace_data(data, add_log)

            if "cache" in clean_options:
                modified |= self._remove_cache_data(data, add_log)

            # 如果有修改，保存文件
            if modified:
                new_content = json.dumps(data, indent=2, ensure_ascii=False)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)

                new_size = len(new_content)
                size_reduction = original_size - new_size
                add_log(f"文件大小减少: {size_reduction} 字节")

                # 验证文件完整性
                if self.db_verify_var.get():
                    if self._verify_json_integrity(file_path):
                        add_log("文件完整性验证通过")
                    else:
                        add_log("警告: 文件完整性验证失败")
                        return False
            else:
                add_log("未找到需要清理的数据")

            return True

        except Exception as e:
            add_log(f"清理JSON文件错误: {str(e)}")
            return False

    def _create_database_backup(self, file_path, add_log):
        """创建数据库备份"""
        try:
            backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backups", "database")
            os.makedirs(backup_dir, exist_ok=True)

            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = os.path.basename(file_path)
            backup_filename = f"db_backup_{filename}_{timestamp}"
            backup_path = os.path.join(backup_dir, backup_filename)

            import shutil
            shutil.copy2(file_path, backup_path)

            add_log(f"已创建备份: {backup_filename}")
            return backup_path

        except Exception as e:
            add_log(f"创建备份失败: {str(e)}")
            return None

    def _remove_telemetry_data(self, data, add_log):
        """移除遥测数据"""
        modified = False
        telemetry_keys = []

        def find_telemetry_keys(obj, path=""):
            nonlocal telemetry_keys
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    if "telemetry" in key.lower():
                        telemetry_keys.append((obj, key))
                    elif isinstance(value, (dict, list)):
                        find_telemetry_keys(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, (dict, list)):
                        find_telemetry_keys(item, f"{path}[{i}]")

        find_telemetry_keys(data)

        for obj, key in telemetry_keys:
            del obj[key]
            modified = True
            add_log(f"移除遥测键: {key}")

        return modified

    def _remove_augment_data(self, data, add_log):
        """移除Augment相关数据"""
        modified = False
        augment_keys = []

        def find_augment_keys(obj, path=""):
            nonlocal augment_keys
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    if "augment" in key.lower() or (isinstance(value, str) and "augment" in value.lower()):
                        augment_keys.append((obj, key))
                    elif isinstance(value, (dict, list)):
                        find_augment_keys(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, (dict, list)):
                        find_augment_keys(item, f"{path}[{i}]")

        find_augment_keys(data)

        for obj, key in augment_keys:
            del obj[key]
            modified = True
            add_log(f"移除Augment键: {key}")

        return modified

    def _remove_workspace_data(self, data, add_log):
        """移除工作区数据"""
        modified = False
        workspace_keys = []

        def find_workspace_keys(obj, path=""):
            nonlocal workspace_keys
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    if "workspace" in key.lower() or "project" in key.lower():
                        workspace_keys.append((obj, key))
                    elif isinstance(value, (dict, list)):
                        find_workspace_keys(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, (dict, list)):
                        find_workspace_keys(item, f"{path}[{i}]")

        find_workspace_keys(data)

        for obj, key in workspace_keys:
            del obj[key]
            modified = True
            add_log(f"移除工作区键: {key}")

        return modified

    def _remove_cache_data(self, data, add_log):
        """移除缓存数据"""
        modified = False
        cache_keys = []

        def find_cache_keys(obj, path=""):
            nonlocal cache_keys
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    if any(word in key.lower() for word in ["cache", "temp", "recent", "history"]):
                        cache_keys.append((obj, key))
                    elif isinstance(value, (dict, list)):
                        find_cache_keys(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, (dict, list)):
                        find_cache_keys(item, f"{path}[{i}]")

        find_cache_keys(data)

        for obj, key in cache_keys:
            del obj[key]
            modified = True
            add_log(f"移除缓存键: {key}")

        return modified

    def _verify_json_integrity(self, file_path):
        """验证JSON文件完整性"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import json
                json.load(f)
            return True
        except:
            return False

    def _clean_databases_powershell(self, script_path, clean_options, add_log):
        """使用PowerShell脚本清理数据库"""
        try:
            add_log("使用PowerShell脚本清理")

            # 构建命令
            cmd = ["powershell", "-ExecutionPolicy", "Bypass", "-File", script_path]
            cmd.extend(["-CleanOptions", ",".join(clean_options)])

            if self.db_backup_var.get():
                cmd.extend(["-CreateBackup"])
            if self.db_verify_var.get():
                cmd.extend(["-VerifyIntegrity"])

            # 执行命令
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            stdout, stderr = process.communicate()

            # 处理输出
            if stdout:
                for line in stdout.split('\n'):
                    if line.strip():
                        add_log(line.strip())

            if stderr:
                add_log(f"错误输出: {stderr}")

            return process.returncode == 0

        except Exception as e:
            add_log(f"PowerShell脚本执行错误: {str(e)}")
            return False

if __name__ == "__main__":
    try:
        root = tk.Tk()
        app = AugmentApp(root)
        root.mainloop()
    except Exception as e:
        print(f"程序启动错误: {str(e)}")
        input("按Enter键退出...")