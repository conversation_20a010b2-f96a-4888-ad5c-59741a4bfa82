"""
创建模拟的编辑器存储文件，用于测试GUI功能
"""

import os
import json
import uuid
import random
from datetime import datetime, timedelta
from pathlib import Path

def create_mock_storage_file(editor_name):
    """创建模拟的编辑器存储文件"""
    # 确定存储文件路径
    if editor_name == "VSCode":
        storage_path = os.path.expandvars("%APPDATA%\\Code\\User\\globalStorage")
    elif editor_name == "Cursor":
        storage_path = os.path.expandvars("%APPDATA%\\Cursor\\User\\globalStorage")
    else:
        print(f"不支持的编辑器: {editor_name}")
        return False
    
    # 创建目录（如果不存在）
    os.makedirs(storage_path, exist_ok=True)
    
    # 创建模拟的storage.json文件
    file_path = os.path.join(storage_path, "storage.json")
    
    # 生成模拟数据
    mock_data = {
        "telemetry.machineId": uuid.uuid4().hex,
        "telemetry.devDeviceId": str(uuid.uuid4()),
        "telemetry.sessionId": str(uuid.uuid4()),
        "telemetry.sqmId": str(uuid.uuid4()),
        "telemetry.firstSessionDate": (datetime.now() - timedelta(days=random.randint(30, 365))).isoformat() + "Z",
        "telemetry.lastSessionDate": datetime.now().isoformat() + "Z",
        "telemetry.isNewAppInstall": False,
        "telemetry.optIn": True,
        "workbench.view.explorer.numberOfVisibleViews": 2,
        "workbench.panel.height": 300,
        "workbench.sidebar.width": 240,
        "editor.fontSize": 14
    }
    
    # 写入文件
    try:
        with open(file_path, 'w') as f:
            json.dump(mock_data, f, indent=2)
        print(f"已创建模拟存储文件: {file_path}")
        return True
    except Exception as e:
        print(f"创建模拟存储文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("创建模拟编辑器存储文件...")
    
    # 询问用户要创建哪个编辑器的模拟文件
    print("\n可用的编辑器:")
    print("1. VSCode")
    print("2. Cursor")
    print("3. 两者都创建")
    
    choice = input("\n请选择 (1-3): ")
    
    if choice == "1":
        create_mock_storage_file("VSCode")
    elif choice == "2":
        create_mock_storage_file("Cursor")
    elif choice == "3":
        create_mock_storage_file("VSCode")
        create_mock_storage_file("Cursor")
    else:
        print("无效的选择")
    
    print("\n完成")
    input("按Enter键退出...")

if __name__ == "__main__":
    main()